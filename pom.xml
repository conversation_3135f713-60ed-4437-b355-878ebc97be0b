<?xml version="1.0"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.github.binarywang</groupId>
  <artifactId>wx-java</artifactId>
  <version>4.5.9.B</version>
  <packaging>pom</packaging>
  <name>WxJava - Weixin/Wechat Java SDK</name>
  <description>微信开发Java SDK</description>
  <url>https://github.com/Wechat-Group/WxJava</url>

  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <developers>
    <developer>
      <name><PERSON></name>
      <email><EMAIL></email>
      <url>https://github.com/chanjarster</url>
    </developer>
    <developer>
      <name>Binary Wang</name>
      <email><EMAIL></email>
      <url>https://github.com/binarywang</url>
    </developer>
    <developer>
      <name>gaigeshen</name>
      <email><EMAIL></email>
      <url>https://github.com/gaigeshen</url>
    </developer>
    <developer>
      <name>Liu Mingbo</name>
      <email><EMAIL></email>
      <url>https://github.com/FirenzesEagle</url>
    </developer>
    <developer>
      <name>kakotor</name>
      <email><EMAIL></email>
      <url>https://github.com/kakotor</url>
    </developer>
    <developer>
      <name>xiong</name>
      <email><EMAIL></email>
      <url>https://github.com/ZhaoxiongTan</url>
    </developer>
    <developer>
      <name>LiuJunGuang</name>
      <email><EMAIL></email>
      <url>https://github.com/aimilin6688</url>
    </developer>
    <developer>
      <name>Eric.Tsai</name>
      <email><EMAIL></email>
      <url>https://github.com/iwareserictsai</url>
    </developer>
    <developer>
      <name>withinthefog</name>
      <email><EMAIL></email>
      <url>https://github.com/withinthefog</url>
    </developer>
    <developer>
      <name>Keung</name>
      <email><EMAIL></email>
      <url>https://github.com/johnnytung</url>
    </developer>
    <developer>
      <name>Jonk</name>
      <email><EMAIL></email>
      <url>https://github.com/aimilin6688</url>
    </developer>
    <developer>
      <name>ecoolper</name>
      <email><EMAIL></email>
      <url>https://github.com/crskyp</url>
    </developer>
    <developer>
      <name>007</name>
      <email><EMAIL></email>
      <url>https://github.com/007gzs</url>
    </developer>
    <developer>
      <name>Howard Liu</name>
      <email><EMAIL></email>
      <url>https://github.com/howardliu-cn</url>
    </developer>
    <developer>
      <name>huangxiaoming</name>
      <email><EMAIL></email>
      <url>https://github.com/huangxm129</url>
    </developer>
    <developer>
      <name>xiaohe</name>
      <email><EMAIL></email>
      <url>https://github.com/xiaohe-53</url>
    </developer>
    <developer>
      <name>Wang_Wong</name>
      <email><EMAIL></email>
      <url>https://github.com/0katekate0</url>
    </developer>
    <developer>
      <name>Bincent</name>
      <email><EMAIL></email>
      <url>https://gitee.com/bincent</url>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:https://github.com/Wechat-Group/WxJava.git</connection>
    <developerConnection>scm:git:**************:Wechat-Group/WxJava.git</developerConnection>
    <url>https://github.com/Wechat-Group/WxJava</url>
  </scm>

  <modules>
    <module>weixin-graal</module>
    <module>weixin-java-common</module>
    <module>weixin-java-cp</module>
    <module>weixin-java-mp</module>
    <module>weixin-java-pay</module>
    <module>weixin-java-miniapp</module>
    <module>weixin-java-open</module>
    <module>weixin-java-qidian</module>
    <module>weixin-java-channel</module>
    <module>spring-boot-starters</module>
    <!--module>weixin-java-osgi</module-->
  </modules>

  <properties>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>

    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <httpclient.version>4.5.13</httpclient.version>
    <jetty.version>9.4.51.v20230217</jetty.version>
    <!-- 这个不能用10以上的版本，不支持jdk8-->
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>qrcode-utils</artifactId>
        <version>1.3</version>
      </dependency>
      <dependency>
        <groupId>org.jodd</groupId>
        <artifactId>jodd-http</artifactId>
        <version>6.3.0</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>4.5.0</version>
        <scope>provided</scope>
      </dependency>

      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpmime</artifactId>
        <version>${httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.13</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.7</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.10</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>com.thoughtworks.xstream</groupId>
        <artifactId>xstream</artifactId>
        <version>1.4.20</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>32.1.2-jre</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>2.10.1</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-xml</artifactId>
        <version>2.15.2</version>
      </dependency>

      <!-- 测试所用依赖 -->
      <dependency>
        <groupId>joda-time</groupId>
        <artifactId>joda-time</artifactId>
        <version>2.10.6</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.3.12</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.inject</groupId>
        <artifactId>guice</artifactId>
        <version>4.2.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.testng</groupId>
        <artifactId>testng</artifactId>
        <version>7.5.1</version>
        <!-- 这个不能用7.6以上的版本，不支持jdk8-->
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <artifactId>guice</artifactId>
            <groupId>com.google.inject</groupId>
          </exclusion>
          <exclusion>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-all</artifactId>
        <version>1.10.19</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>${jetty.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>${jetty.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-guava</artifactId>
        <version>3.0.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.github.dreamhead</groupId>
        <artifactId>moco-runner</artifactId>
        <version>1.1.0</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>redis.clients</groupId>
        <artifactId>jedis</artifactId>
        <version>3.3.0</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.github.jedis-lock</groupId>
        <artifactId>jedis-lock</artifactId>
        <version>1.0.0</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson</artifactId>
        <version>3.23.3</version>
        <optional>true</optional>
        <scope>provided</scope>
        <exclusions>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.jodd</groupId>
            <artifactId>jodd-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.reactivestreams</groupId>
            <artifactId>reactive-streams</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-redis</artifactId>
        <version>2.3.3.RELEASE</version>
        <optional>true</optional>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.18.24</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcpkix-jdk15on</artifactId>
        <version>1.70</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <profiles>
    <profile>
      <id>doclint-java8-disable</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <properties>
        <javadoc.opts>-Xdoclint:none</javadoc.opts>
      </properties>
    </profile>

    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.1.0</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.9.1</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <additionalparam>${javadoc.opts}</additionalparam>
              <charset>UTF-8</charset>
              <locale>zh_CN</locale>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.1.0</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>native-image</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
    </profile>

  </profiles>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.17</version>
          <configuration>
            <skip>true</skip>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.3</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>ossrh</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
          <autoReleaseAfterClose>true</autoReleaseAfterClose>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.1</version>
        <configuration>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <useReleaseProfile>false</useReleaseProfile>
          <releaseProfiles>release</releaseProfiles>
          <goals>deploy</goals>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.17</version>
        <configuration>
          <skip>true</skip>
          <configLocation>quality-checks/google_checks.xml</configLocation>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <consoleOutput>true</consoleOutput>
          <failsOnError>true</failsOnError>
        </configuration>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
