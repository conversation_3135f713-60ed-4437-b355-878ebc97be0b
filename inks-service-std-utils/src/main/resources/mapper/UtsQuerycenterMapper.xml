<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.utils.mapper.UtsQuerycenterMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.utils.domain.pojo.UtsQuerycenterPojo">
        <include refid="selectUtsQuerycenterVo"/>
        where Uts_QueryCenter.id = #{key} and Uts_QueryCenter.Tenantid=#{tid}
    </select>
    <sql id="selectUtsQuerycenterVo">
         select
id, QueryCode, QueryTitle, DynType, DynSentence, HeaderColumn, SceneField, PermCode, TimeLimitMark, Databaseid, LocalMark, DatePath, AuthCode, Row<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>anti<PERSON>, TenantName, Revision        from Uts_QueryCenter
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.utils.domain.pojo.UtsQuerycenterPojo">
        <include refid="selectUtsQuerycenterVo"/>
         where 1 = 1 and Uts_QueryCenter.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_QueryCenter.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.querycode != null ">
   and Uts_QueryCenter.QueryCode like concat('%', #{SearchPojo.querycode}, '%')
</if>
<if test="SearchPojo.querytitle != null ">
   and Uts_QueryCenter.QueryTitle like concat('%', #{SearchPojo.querytitle}, '%')
</if>
<if test="SearchPojo.dyntype != null ">
   and Uts_QueryCenter.DynType like concat('%', #{SearchPojo.dyntype}, '%')
</if>
<if test="SearchPojo.dynsentence != null ">
   and Uts_QueryCenter.DynSentence like concat('%', #{SearchPojo.dynsentence}, '%')
</if>
<if test="SearchPojo.headerjson != null ">
   and Uts_QueryCenter.HeaderJson like concat('%', #{SearchPojo.headerjson}, '%')
</if>
<if test="SearchPojo.scene != null ">
   and Uts_QueryCenter.Scene like concat('%', #{SearchPojo.scene}, '%')
</if>
<if test="SearchPojo.permcode != null ">
   and Uts_QueryCenter.PermCode like concat('%', #{SearchPojo.permcode}, '%')
</if>
<if test="SearchPojo.databaseid != null ">
   and Uts_QueryCenter.Databaseid like concat('%', #{SearchPojo.databaseid}, '%')
</if>
<if test="SearchPojo.datepath != null ">
   and Uts_QueryCenter.DatePath like concat('%', #{SearchPojo.datepath}, '%')
</if>
<if test="SearchPojo.authcode != null ">
   and Uts_QueryCenter.AuthCode like concat('%', #{SearchPojo.authcode}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_QueryCenter.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_QueryCenter.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_QueryCenter.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_QueryCenter.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_QueryCenter.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_QueryCenter.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_QueryCenter.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_QueryCenter.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_QueryCenter.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_QueryCenter.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.querycode != null ">
   or Uts_QueryCenter.QueryCode like concat('%', #{SearchPojo.querycode}, '%')
</if>
<if test="SearchPojo.querytitle != null ">
   or Uts_QueryCenter.QueryTitle like concat('%', #{SearchPojo.querytitle}, '%')
</if>
<if test="SearchPojo.dyntype != null ">
   or Uts_QueryCenter.DynType like concat('%', #{SearchPojo.dyntype}, '%')
</if>
<if test="SearchPojo.dynsentence != null ">
   or Uts_QueryCenter.DynSentence like concat('%', #{SearchPojo.dynsentence}, '%')
</if>
<if test="SearchPojo.headerjson != null ">
   or Uts_QueryCenter.HeaderJson like concat('%', #{SearchPojo.headerjson}, '%')
</if>
<if test="SearchPojo.scene != null ">
   or Uts_QueryCenter.Scene like concat('%', #{SearchPojo.scene}, '%')
</if>
<if test="SearchPojo.permcode != null ">
   or Uts_QueryCenter.PermCode like concat('%', #{SearchPojo.permcode}, '%')
</if>
<if test="SearchPojo.databaseid != null ">
   or Uts_QueryCenter.Databaseid like concat('%', #{SearchPojo.databaseid}, '%')
</if>
<if test="SearchPojo.datepath != null ">
   or Uts_QueryCenter.DatePath like concat('%', #{SearchPojo.datepath}, '%')
</if>
<if test="SearchPojo.authcode != null ">
   or Uts_QueryCenter.AuthCode like concat('%', #{SearchPojo.authcode}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_QueryCenter.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_QueryCenter.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_QueryCenter.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_QueryCenter.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_QueryCenter.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_QueryCenter.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_QueryCenter.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_QueryCenter.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_QueryCenter.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_QueryCenter.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_QueryCenter(id, QueryCode, QueryTitle, DynType, DynSentence, HeaderColumn, SceneField, PermCode, TimeLimitMark, Databaseid, LocalMark, DatePath, AuthCode, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{querycode}, #{querytitle}, #{dyntype}, #{dynsentence}, #{headercolumn}, #{scenefield}, #{permcode}, #{timelimitmark}, #{databaseid}, #{localmark}, #{datepath}, #{authcode}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_QueryCenter
        <set>
            <if test="querycode != null ">
                QueryCode =#{querycode},
            </if>
            <if test="querytitle != null ">
                QueryTitle =#{querytitle},
            </if>
            <if test="dyntype != null ">
                DynType =#{dyntype},
            </if>
            <if test="dynsentence != null ">
                DynSentence =#{dynsentence},
            </if>
            <if test="headercolumn != null ">
                HeaderColumn =#{headercolumn},
            </if>
            <if test="scenefield != null ">
                SceneField =#{scenefield},
            </if>
            <if test="permcode != null ">
                PermCode =#{permcode},
            </if>
            <if test="timelimitmark != null">
                TimeLimitMark =#{timelimitmark},
            </if>
            <if test="databaseid != null ">
                Databaseid =#{databaseid},
            </if>
            <if test="localmark != null">
                LocalMark =#{localmark},
            </if>
            <if test="datepath != null ">
                DatePath =#{datepath},
            </if>
            <if test="authcode != null ">
                AuthCode =#{authcode},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_QueryCenter where id = #{key} and Tenantid=#{tid}
    </delete>
</mapper>

