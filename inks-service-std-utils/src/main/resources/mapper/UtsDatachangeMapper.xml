<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.utils.mapper.UtsDatachangeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.utils.domain.pojo.UtsDatachangePojo">
        select
          id, RefNo, BillType, BillDate, BillTitle, ChgReason, PermCode, ModuleCode, OrgJson, NewJson, DataInterface, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Submitterid, Submitter, SubmitDate, Assessor, Assessorid, AssessDate, UpdateBy, UpdateByid, UpdateDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision        from Uts_DataChange
        where Uts_DataChange.id = #{key} and Uts_DataChange.Tenantid=#{tid}
    </select>
    <sql id="selectUtsDatachangeVo">
         select
          id, RefNo, BillType, BillDate, BillTitle, ChgReason, PermCode, ModuleCode, OrgJson, NewJson, DataInterface, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Submitterid, Submitter, SubmitDate, Assessor, Assessorid, AssessDate, UpdateBy, UpdateByid, UpdateDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision        from Uts_DataChange
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.utils.domain.pojo.UtsDatachangePojo">
        <include refid="selectUtsDatachangeVo"/>
         where 1 = 1 and Uts_DataChange.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_DataChange.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Uts_DataChange.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Uts_DataChange.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Uts_DataChange.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.chgreason != null ">
   and Uts_DataChange.ChgReason like concat('%', #{SearchPojo.chgreason}, '%')
</if>
<if test="SearchPojo.permcode != null ">
   and Uts_DataChange.PermCode like concat('%', #{SearchPojo.permcode}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and Uts_DataChange.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.orgjson != null ">
   and Uts_DataChange.OrgJson like concat('%', #{SearchPojo.orgjson}, '%')
</if>
<if test="SearchPojo.newjson != null ">
   and Uts_DataChange.NewJson like concat('%', #{SearchPojo.newjson}, '%')
</if>
<if test="SearchPojo.datainterface != null ">
   and Uts_DataChange.DataInterface like concat('%', #{SearchPojo.datainterface}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_DataChange.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_DataChange.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_DataChange.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_DataChange.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_DataChange.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.submitterid != null ">
   and Uts_DataChange.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
</if>
<if test="SearchPojo.submitter != null ">
   and Uts_DataChange.Submitter like concat('%', #{SearchPojo.submitter}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Uts_DataChange.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Uts_DataChange.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.updateby != null ">
   and Uts_DataChange.UpdateBy like concat('%', #{SearchPojo.updateby}, '%')
</if>
<if test="SearchPojo.updatebyid != null ">
   and Uts_DataChange.UpdateByid like concat('%', #{SearchPojo.updatebyid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_DataChange.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_DataChange.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_DataChange.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_DataChange.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_DataChange.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Uts_DataChange.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Uts_DataChange.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Uts_DataChange.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Uts_DataChange.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Uts_DataChange.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Uts_DataChange.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_DataChange.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Uts_DataChange.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Uts_DataChange.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Uts_DataChange.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.chgreason != null ">
   or Uts_DataChange.ChgReason like concat('%', #{SearchPojo.chgreason}, '%')
</if>
<if test="SearchPojo.permcode != null ">
   or Uts_DataChange.PermCode like concat('%', #{SearchPojo.permcode}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or Uts_DataChange.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.orgjson != null ">
   or Uts_DataChange.OrgJson like concat('%', #{SearchPojo.orgjson}, '%')
</if>
<if test="SearchPojo.newjson != null ">
   or Uts_DataChange.NewJson like concat('%', #{SearchPojo.newjson}, '%')
</if>
<if test="SearchPojo.datainterface != null ">
   or Uts_DataChange.DataInterface like concat('%', #{SearchPojo.datainterface}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_DataChange.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_DataChange.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_DataChange.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_DataChange.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_DataChange.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.submitterid != null ">
   or Uts_DataChange.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
</if>
<if test="SearchPojo.submitter != null ">
   or Uts_DataChange.Submitter like concat('%', #{SearchPojo.submitter}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Uts_DataChange.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Uts_DataChange.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.updateby != null ">
   or Uts_DataChange.UpdateBy like concat('%', #{SearchPojo.updateby}, '%')
</if>
<if test="SearchPojo.updatebyid != null ">
   or Uts_DataChange.UpdateByid like concat('%', #{SearchPojo.updatebyid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_DataChange.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_DataChange.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_DataChange.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_DataChange.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_DataChange.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Uts_DataChange.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Uts_DataChange.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Uts_DataChange.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Uts_DataChange.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Uts_DataChange.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Uts_DataChange.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_DataChange.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_DataChange(id, RefNo, BillType, BillDate, BillTitle, ChgReason, PermCode, ModuleCode, OrgJson, NewJson, DataInterface, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Submitterid, Submitter, SubmitDate, Assessor, Assessorid, AssessDate, UpdateBy, UpdateByid, UpdateDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{chgreason}, #{permcode}, #{modulecode}, #{orgjson}, #{newjson}, #{datainterface}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{submitterid}, #{submitter}, #{submitdate}, #{assessor}, #{assessorid}, #{assessdate}, #{updateby}, #{updatebyid}, #{updatedate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_DataChange
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="chgreason != null ">
                ChgReason =#{chgreason},
            </if>
            <if test="permcode != null ">
                PermCode =#{permcode},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="orgjson != null ">
                OrgJson =#{orgjson},
            </if>
            <if test="newjson != null ">
                NewJson =#{newjson},
            </if>
            <if test="datainterface != null ">
                DataInterface =#{datainterface},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="submitterid != null ">
                Submitterid =#{submitterid},
            </if>
            <if test="submitter != null ">
                Submitter =#{submitter},
            </if>
            <if test="submitdate != null">
                SubmitDate =#{submitdate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="updateby != null ">
                UpdateBy =#{updateby},
            </if>
            <if test="updatebyid != null ">
                UpdateByid =#{updatebyid},
            </if>
            <if test="updatedate != null">
                UpdateDate =#{updatedate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_DataChange where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                            <!--通过主键审核数据-->
    <update id="approval">
        update Uts_DataChange SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
                                                                                </mapper>

