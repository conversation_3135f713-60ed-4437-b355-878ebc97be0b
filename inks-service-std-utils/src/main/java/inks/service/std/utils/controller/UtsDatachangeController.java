package inks.service.std.utils.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.utils.domain.pojo.UtsDatachangePojo;
import inks.service.std.utils.service.UtsDatachangeService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 数据变更(Uts_DataChange)表控制层
 *
 * <AUTHOR>
 * @since 2022-06-01 14:33:58
 */
public class UtsDatachangeController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(UtsDatachangeController.class);
    /**
     * 服务对象
     */
    @Resource
    private UtsDatachangeService utsDatachangeService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取数据变更详细信息", notes = "获取数据变更详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Uts_DataChange.List")
    public R<UtsDatachangePojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsDatachangeService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Uts_DataChange.List")
    public R<PageInfo<UtsDatachangePojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_DataChange.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsDatachangeService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增数据变更", notes = "新增数据变更", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Uts_DataChange.Add")
    public R<UtsDatachangePojo> create(@RequestBody String json) {
        try {
            UtsDatachangePojo utsDatachangePojo = JSONArray.parseObject(json, UtsDatachangePojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            R r = systemFeignService.getBillCode("DxxMxxB1", loginUser.getToken());
            if (r.getCode() == 200)
                utsDatachangePojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            utsDatachangePojo.setCreateby(loginUser.getRealName());   // 创建者
            utsDatachangePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsDatachangePojo.setCreatedate(new Date());   // 创建时间
            utsDatachangePojo.setLister(loginUser.getRealname());   // 制表
            utsDatachangePojo.setListerid(loginUser.getUserid());    // 制表id  
            utsDatachangePojo.setModifydate(new Date());   //修改时间
            utsDatachangePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.utsDatachangeService.insert(utsDatachangePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改数据变更", notes = "修改数据变更", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Uts_DataChange.Edit")
    public R<UtsDatachangePojo> update(@RequestBody String json) {
        try {
            UtsDatachangePojo utsDatachangePojo = JSONArray.parseObject(json, UtsDatachangePojo.class);
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            utsDatachangePojo.setLister(loginUser.getRealname());   // 制表
            utsDatachangePojo.setListerid(loginUser.getUserid());    // 制表id  
            utsDatachangePojo.setTenantid(loginUser.getTenantid());   //租户id
            utsDatachangePojo.setModifydate(new Date());   //修改时间
//            utsDatachangePojo.setAssessor(""); // 审核员
//            utsDatachangePojo.setAssessorid(""); // 审核员id
//            utsDatachangePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsDatachangeService.update(utsDatachangePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除数据变更", notes = "删除数据变更", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Uts_DataChange.Delete")
    public R<Integer> delete(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsDatachangeService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核数据变更", notes = "审核数据变更", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Uts_DataChange.Approval")
    public R<UtsDatachangePojo> approval(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            UtsDatachangePojo utsDatachangePojo = this.utsDatachangeService.getEntity(key, loginUser.getTenantid());
            if (utsDatachangePojo.getAssessor().equals("")) {
                utsDatachangePojo.setAssessor(loginUser.getRealname()); //审核员
                utsDatachangePojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                utsDatachangePojo.setAssessor(""); //审核员
                utsDatachangePojo.setAssessorid(""); //审核员
            }
            utsDatachangePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsDatachangeService.approval(utsDatachangePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Uts_DataChange.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsDatachangePojo utsDatachangePojo = this.utsDatachangeService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsDatachangePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

