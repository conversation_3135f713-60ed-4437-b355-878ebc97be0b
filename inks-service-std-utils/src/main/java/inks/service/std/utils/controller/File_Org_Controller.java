//package inks.service.std.utils.controller;
//
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.aliyun.oss.HttpMethod;
//import com.aliyun.oss.OSS;
//import com.aliyun.oss.OSSClientBuilder;
//import com.aliyun.oss.model.GeneratePresignedUrlRequest;
//import com.aliyun.oss.model.OSSObject;
//import com.aliyun.oss.model.ResponseHeaderOverrides;
//import inks.api.feign.AuthFeignService;
//import inks.common.core.constant.InksConstants;
//import inks.common.core.domain.FileInfo;
//import inks.common.core.domain.LoginUser;
//import inks.common.core.domain.R;
//import inks.common.core.exception.BaseBusinessException;
//import inks.common.core.exception.GlobalException;
//import inks.common.core.utils.AESUtil;
//import inks.common.core.utils.DateUtils;
//import inks.common.core.utils.ServletUtils;
//import inks.common.redis.service.RedisService;
//import inks.common.security.service.TokenService;
//import inks.service.std.utils.config.ossConfig.OssConstant;
//import inks.service.std.utils.config.ossConfig.StorageException;
//import inks.service.std.utils.config.ossConfig.StorageType;
//import inks.service.std.utils.constant.MyConstant;
//import inks.service.std.utils.domain.pojo.UtsDirrulePojo;
//import inks.service.std.utils.domain.vo.UploadImageVO;
//import inks.service.std.utils.service.impl.OssAliyunServiceImpl;
//import inks.service.std.utils.service.impl.OssMinioServiceImpl;
//import inks.service.std.utils.utils.FileUtil;
//import inks.service.std.utils.utils.ImageUtil;
//import io.minio.*;
//import io.minio.http.Method;
//import io.swagger.annotations.ApiOperation;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.io.FilenameUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.util.AntPathMatcher;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.servlet.HandlerMapping;
//import org.springframework.web.servlet.view.RedirectView;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.*;
//import java.net.URI;
//import java.net.URLEncoder;
//import java.nio.charset.StandardCharsets;
//import java.nio.file.Files;
//import java.util.*;
//import java.util.concurrent.ConcurrentHashMap;
//
//import static org.apache.commons.lang3.StringUtils.*;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @Author: song
// * @Date: 2021/08/28/9:26
// * @Description:
// */
//@RestController
//@RequestMapping("File")
//public class File_Org_Controller {
//    // 最大图片上传大小10M
//    public static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024;
//    private static final Logger log = LoggerFactory.getLogger(File_Org_Controller.class);
//    private static final long URL_EXPIRATION_TIME = 7 * 24 * 3600 * 1000L; // 7天
//    private static final long CACHE_EXPIRATION_BUFFER = 300000L; // 5分钟
//    // 缓存常用的Content-Type映射，提高性能
//    private static final Map<String, String> CONTENT_TYPE_MAP = new ConcurrentHashMap<>();
//
//    static {
//        CONTENT_TYPE_MAP.put("gif", "image/gif");
//        CONTENT_TYPE_MAP.put("jpg", "image/jpeg");
//        CONTENT_TYPE_MAP.put("jpeg", "image/jpeg");
//        CONTENT_TYPE_MAP.put("png", "image/png");
//        CONTENT_TYPE_MAP.put("pdf", "application/pdf");
//        CONTENT_TYPE_MAP.put("txt", "text/plain");
//        CONTENT_TYPE_MAP.put("html", "text/html");
//        CONTENT_TYPE_MAP.put("htm", "text/html");
//        CONTENT_TYPE_MAP.put("xml", "text/xml");
//        CONTENT_TYPE_MAP.put("json", "application/json");
//        CONTENT_TYPE_MAP.put("mp4", "video/mp4");
//        CONTENT_TYPE_MAP.put("mp3", "audio/mpeg");
//    }
//
//    private final AntPathMatcher antPathMatcher = new AntPathMatcher();
//    private final ConcurrentHashMap<String, SignedUrlCache> urlCache = new ConcurrentHashMap<>();
//    @Resource
//    private OssAliyunServiceImpl fileInfoAliyunService; //aliyun上传
//    @Resource
//    private OssMinioServiceImpl fileInfoMinioService; //minio上传
//    @Value("${oss.type}")
//    private String osstype;
//    //    @Autowired
////    private OSS ossClient;
//    @Value("${oss.minio.bucket}")
//    private String BUCKET_NAME_MINIO;
//    @Value("${oss.aliyun.bucket}")
//    private String BUCKET_NAME_ALIYUN;
//    /**
//     * 引用Token服务
//     */
//    @Resource
//    private TokenService tokenService;
//    @Resource
//    private MinioClient minioClient;
//    @Resource
//    private OSS ossClient;
//
//    @Resource
//    private RedisService redisService;
//    @Resource
//    private AuthFeignService authFeignService;
//
//    public R<FileInfo> upload(MultipartFile file, String bucket, String dir, String osstype) {
//        return uploadByAuth(file, bucket, dir, osstype, null);
//    }
//
//    // =====================================================================================
//    // 1️⃣ 通用文件上传类
//    // =====================================================================================
//
//    @ApiOperation(value = "上传单个文件ByOssType", notes = "上传单个文件")
//    @PostMapping(value = "upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    public R<FileInfo> uploadByAuth(MultipartFile file, String bucket, String dir, String osstype, String auth) {
//        try {
//            // 1. 获取目录规则列表
//            List<UtsDirrulePojo> utsDirruleList = redisService.getCacheObject(MyConstant.DIR_RULE_LIST + InksConstants.DEFAULT_TENANT);
//            boolean skipSecValidation = false;
//
//            // 2. 判断是否命中 publicmark=1 的白名单前缀
//            if (CollectionUtils.isNotEmpty(utsDirruleList)) {
//                for (UtsDirrulePojo rule : utsDirruleList) {
//                    if (Objects.equals(rule.getPublicmark(), 1)) {
//                        if (dir.startsWith(rule.getDirname())) {
//                            skipSecValidation = true;
//                            break;
//                        }
//                    }
//                }
//            }
//
//            // 3. 非公共目录校验权限
//            if (!skipSecValidation) {
//                LoginUser loginUser = this.getLoginUserByAuthCode(auth);
//                // 没传auth就走之前的token校验
//                if (loginUser == null) {
//                    loginUser = tokenService.getLoginUser();
//                }
//                if (CollectionUtils.isNotEmpty(utsDirruleList)) {
//
//                    String userId = loginUser.getUserid();
//
//                    UtsDirrulePojo matchedRule = null;
//                    for (UtsDirrulePojo rule : utsDirruleList) {
//                        if (dir.startsWith(rule.getDirname())) {
//                            matchedRule = rule;
//                            break;
//                        }
//                    }
//
//                    if (matchedRule != null) {
//                        // 黑名单校验
//                        if (StringUtils.isNotBlank(matchedRule.getBlackuserids())) {
//                            List<String> blackIds = Arrays.asList(matchedRule.getBlackuserids().split(","));
//                            if (blackIds.contains(userId)) {
//                                log.error("用户 {} 在黑名单中，禁止访问目录 {}", userId, matchedRule.getDirname());
//                                return R.fail("您无权限访问该目录，黑名单");
//                            }
//                        }
//
//                        // 白名单校验
//                        if (StringUtils.isNotBlank(matchedRule.getWhiteuserids())) {
//                            List<String> whiteIds = Arrays.asList(matchedRule.getWhiteuserids().split(","));
//                            if (!whiteIds.contains(userId)) {
//                                log.error("用户 {} 不在白名单中，禁止访问目录 {}", userId, matchedRule.getDirname());
//                                return R.fail("您无权限访问该目录，非白名单");
//                            }
//                        }
//                    }
//                }
//            }
//
//            // 4. 设置默认 bucket
//            osstype = StringUtils.defaultIfBlank(osstype, this.osstype);
//            if (StringUtils.isBlank(bucket)) {
//                if (OssConstant.OSSTYPE_MINIO.equals(osstype)) {
//                    bucket = this.BUCKET_NAME_MINIO;
//                } else if (OssConstant.OSSTYPE_ALIYUN.equals(osstype)) {
//                    bucket = this.BUCKET_NAME_ALIYUN;
//                } else {
//                    return R.fail("osstype: oss类型错误");
//                }
//            }
//
//            // 5. 拼接目录日期
//            dir = dir + "/" + DateUtils.parseDateToStr("yyyyMM", new Date());
//
//            // 6. 上传处理
//            if (OssConstant.OSSTYPE_MINIO.equals(osstype)) {
//                return R.ok(this.fileInfoMinioService.putFile(file, bucket, dir), "上传成功");
//            } else if (OssConstant.OSSTYPE_ALIYUN.equals(osstype)) {
//                return R.ok(this.fileInfoAliyunService.putFile(file, bucket, dir), "上传成功");
//            } else {
//                return R.fail("不支持的 oss类型");
//            }
//
//        } catch (Exception e) {
//            log.error("上传失败", e);
//            return R.fail("上传失败: " + e.getMessage());
//        }
//    }
//
//
//    // 拷贝tokenService的auth登录逻辑：
//    public LoginUser getLoginUserByAuthCode(String auth) {
//        if (StringUtils.isNotEmpty(auth)) {
//            String authKey;
//            try {
//                authKey = "auth_tokens:" + AESUtil.Encrypt(auth);
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//
//            String token = redisService.getCacheObject(authKey);
//            if (StringUtils.isNotEmpty(token)) {
//                return this.tokenService.getLoginUser(token);
//            }
//
//            try {
//                Map<String, Object> data = authFeignService.loginByAuthCode(auth).getData();
//                String loginUserJson = JSON.toJSONString(data.get("loginuser"));
//                return JSON.parseObject(loginUserJson, LoginUser.class);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        return null;
//    }
//
//
//    /**
//     * @return R<FileInfo>
//     * @Description Minio上传单个图片, 自定义桶名bucket，会上传两张图片(原图+缩略图)，返回两张图片地址
//     * 返回格式为 {"originUrl":"http://dev.inksyun.com:9080/box-im/image/20231205/1701762645385.png"
//     * ,"thumbUrl":"http://dev.inksyun.com:9080/box-im/image/20231205/1701762645617.png"}
//     * <AUTHOR>
//     * @param[1] file
//     * @param[2] bucket 桶名
//     * @param[3] dir 桶名后跟的文件夹名
//     * @time 2023/5/31 10:31
//     */
//    @ApiOperation(value = "上传一张图片，返回原图+缩略图2个地址,bucket:桶名,dir:桶名后跟的文件夹名,osstype为minio", notes = "")
//    @PostMapping(value = "upload2Image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    public R<UploadImageVO> upload2Image(MultipartFile file, String bucket, String dir) {
//        // 大小校验
//        if (file.getSize() > MAX_IMAGE_SIZE) {
//            throw new BaseBusinessException("图片大小不能超过10M");
//        }
//        // 图片格式校验
//        if (!FileUtil.isImage(file.getOriginalFilename())) {
//            throw new BaseBusinessException("图片格式不合法");
//        }
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (loginUser == null) throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        if (isBlank(osstype)) {
//            osstype = this.osstype;
//        }
//        //加一层日期文件夹
//        dir = dir + "/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
//
//
//        // 构建返回vo,原图+缩略图url
//        UploadImageVO vo = new UploadImageVO();
//        String url = "";
//        try {
//            // 上传原图
//            FileInfo fileInfo = this.fileInfoMinioService.putFile(file, bucket, dir);
//            url = fileInfo.getFileurl();
//            vo.setOriginUrl(url);
//            // 大于30K的文件需上传缩略图
//            if (file.getSize() > 30 * 1024) {
//                byte[] imageByte = ImageUtil.compressForScale(file.getBytes(), 30);
//                url = fileInfoMinioService.uploadImageByByte(bucket, dir, file.getOriginalFilename(), imageByte, file.getContentType());
//                if (isBlank(url)) {
//                    throw new GlobalException("图片上传失败");
//                }
//            }
//            vo.setThumbUrl(url);
//            return R.ok(vo);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /**
//     * @return R
//     * @Description 删除单个文件
//     * <AUTHOR>
//     * @param[1] json 只接收bucketname和filename(例如：{"bucketName":"pic","fileName":"test/20210531/xx.jpg"})
//     * @time 2023/5/31 10:58
//     */
//    @ApiOperation(value = "删除单个文件ByOssType", notes = "删除单个文件json 只接收bucketname和filename(例如：{\"bucketName\":\"pic\",\"fileName\":\"test/20210531/xx.jpg\"})")
//    @PostMapping(value = "remove")
//    public R remove(@RequestBody String json, String osstype) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (loginUser == null) {
//            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        }
//        if (isBlank(osstype)) {
//            osstype = this.osstype;
//        }
//        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
//        try {
//            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
//                return R.ok(this.fileInfoMinioService.removeFile(fileInfo), "删除成功");
//            } else if (osstype.equals(OssConstant.OSSTYPE_ALIYUN)) {
//                return R.ok(this.fileInfoAliyunService.removeFile(fileInfo), "删除成功");
//            } else {
//                return R.fail("osstype:oss类型错误");
//            }
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "通过文件路径上传单个文件ByOssType", notes = "通过文件路径上传单个文件")
//    @PostMapping(value = "uploadByPath")
//    public R<FileInfo> uploadByPath(String filepath, String bucket, String dir, String objectname, String osstype) {
//        //LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        //if (loginUser == null) throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        if (isBlank(osstype)) {
//            osstype = this.osstype;
//        }
//        try {
//            ////加一层日期文件夹
//            //dir = dir + "/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
//            log.info("-----------------------------开始Minio通过文件路径上传单个文件-----------------------------");
//            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
//                return R.ok(this.fileInfoMinioService.putFile(filepath, bucket, dir, objectname), "上传成功");
//            } else if (osstype.equals(OssConstant.OSSTYPE_ALIYUN)) {
//                return R.ok(this.fileInfoAliyunService.putFile(filepath, bucket, dir, objectname), "上传成功");
//            } else {
//                return R.fail("osstype:oss类型错误");
//            }
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//
////       ----------------------minio,aliyun预览下载文件接口----------------
//
//    @ApiOperation(value = "通过文件路径上传单个文件使用自定义连接参数", notes = "仅返回访问URL")
//    @PostMapping(value = "uploadByPathCustom")
//    public R<String> uploadByPathCustom(String filepath, String ossbucket, String dir, String objectname, String
//                                                osstype,
//                                        String ossaccesskey, String osssecretkey,
//                                        String ossendpoint) {
//        if (isBlank(osstype)) {
//            return R.fail("osstype不能为空");
//        }
//
//        try {
//            File file = new File(filepath);
//            if (!file.exists()) {
//                return R.fail("文件不存在");
//            }
//
//            // 处理文件名
//            String fileName = StringUtils.isNotBlank(objectname) ? objectname : file.getName();
//            String fileSuffix = org.springframework.util.StringUtils.getFilenameExtension(file.getName());
//            if (StringUtils.isNotBlank(fileSuffix)) {
//                fileName = fileName.concat(".").concat(fileSuffix);
//            }
//
//            // 构建完整的对象路径
//            String objectPath = StringUtils.isNotBlank(dir) ?
//                    dir + "/" + fileName : fileName;
//
//            String fileUrl;
//            FileInputStream inputStream = new FileInputStream(file);
//
//            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
//                // MinIO上传
//                try {
//                    MinioClient minioClient = MinioClient.builder()
//                            .endpoint(ossendpoint)
//                            .credentials(ossaccesskey, osssecretkey)
//                            .build();
//
//                    // 确保bucket存在
//                    boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder()
//                            .bucket(ossbucket)
//                            .build());
//                    if (!bucketExists) {
//                        minioClient.makeBucket(MakeBucketArgs.builder()
//                                .bucket(ossbucket)
//                                .build());
//                    }
//
//                    // 上传文件
//                    minioClient.putObject(PutObjectArgs.builder()
//                            .bucket(ossbucket)
//                            .object(objectPath)
//                            .stream(inputStream, file.length(), -1)
//                            .contentType(Files.probeContentType(file.toPath()))
//                            .build());
//
//                    // 构建访问URL
//                    fileUrl = ossendpoint + "/" + ossbucket + "/" + objectPath;
//
//                } catch (Exception e) {
//                    throw new RuntimeException("MinIO上传失败: " + e.getMessage());
//                }
//
//            } else if (osstype.equals(OssConstant.OSSTYPE_ALIYUN)) {
//                // 阿里云OSS上传
//                try {
//                    OSS ossClient = new OSSClientBuilder().build(
//                            ossendpoint,
//                            ossaccesskey,
//                            osssecretkey
//                    );
//
//                    // 上传文件
//                    ossClient.putObject(ossbucket, objectPath, inputStream);
//                    ossClient.shutdown();
//
//                    // 构建访问URL
//                    fileUrl = "https://" + ossbucket + "." + ossendpoint + "/" + objectPath;
//
//                } catch (Exception e) {
//                    throw new RuntimeException("阿里云OSS上传失败: " + e.getMessage());
//                }
//
//            } else {
//                return R.fail("不支持的osstype类型");
//            }
//
//            inputStream.close();
//            return R.ok(fileUrl, "上传成功");
//
//        } catch (Exception e) {
//            log.error("文件上传失败", e);
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "上传文本 AliYun", notes = "AliYun上传文本")
//    @PostMapping(value = "saveContent")
//    public R<FileInfo> saveContent(@RequestBody String json) {
//        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (loginUser == null) {
//            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        }
//        try {
//            return R.ok(this.fileInfoAliyunService.putContent(fileInfo), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "上传ImageBase64 AliYun", notes = "AliYun上传ImageBase64")
//    @PostMapping(value = "saveImage")
//    public R<FileInfo> saveImage(@RequestBody String json) {
//        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (loginUser == null) {
//            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        }
//        try {
//            return R.ok(this.fileInfoAliyunService.putImage(fileInfo), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    // 通用上传文件流
//    @ApiOperation(value = "通用上传文件流ByOssType", notes = "通用上传文件流")
//    @PostMapping(value = "uploadInputStream")
//    public R<String> uploadInputStream(InputStream inputStream, String dirname, String fileName, String osstype) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (loginUser == null) throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        if (isBlank(osstype)) {
//            osstype = this.osstype;
//        }
//        try {
//            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
//                return R.ok(this.fileInfoMinioService.uploadInputStream(inputStream, dirname, fileName), "上传成功");
//            } else if (osstype.equals(OssConstant.OSSTYPE_ALIYUN)) {
//                return R.ok(this.fileInfoAliyunService.uploadInputStream(inputStream, dirname, fileName), "上传成功");
//            } else {
//                return R.fail("osstype:oss类型错误");
//            }
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "String字符串转html并上传", notes = "上传文件")
//    @PostMapping(value = "uploadHtml")
//    public R<FileInfo> uploadHtml(String content, String bucket, String dir) throws IOException {
////        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
////        if (loginUser == null) {
////            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
////        }
//        String fileName = "example.html"; // 临时文件名
//        String fileType = "text/html"; // 文件类型
//        // 将字符串内容转换为字节数组
//        byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
//        // 创建MultipartFile对象
//        MultipartFile file = new MockMultipartFile(fileName, fileName, fileType, contentBytes);
//        try {
//            //加一层日期文件夹
//            dir = dir + "/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
//            log.info("-----------------------------开始上传html文件-----------------------------");
//            return R.ok(this.fileInfoMinioService.putFile(file, bucket, dir), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    // 统一的预览接口 代理
//    @GetMapping(value = {"getAliOssUrl/**", "getMinioUrl/**", "getUrl/**"})
//    public RedirectView getStorageUrl(HttpServletRequest request) {
//        String objectName = getObjectNameFromRequest(request);
//        // 如果是"/getUrl"路径，使用YML配置的存储类型
//        StorageType storageType;
//        if (request.getRequestURI().contains("getUrl")) {
//            // 使用YML中配置的类型
//            storageType = StorageType.valueOf(osstype.toUpperCase());
//        } else {
//            // 默认通过路径判断存储类型
//            storageType = request.getRequestURI().contains("AliOss") ?
//                    StorageType.ALIYUN : StorageType.MINIO;
//        }
//        // 通过缓存的文件签名读取/下载文件
//        String signedUrl = generateSignedUrl(objectName, false, storageType);
//
//        RedirectView redirectView = new RedirectView();
//        redirectView.setUrl(signedUrl);
//        return redirectView;
//    }
//
//    // 统一的下载接口
//    @GetMapping(value = {"downloadAliOssUrl/**", "downloadMinioUrl/**", "downloadUrl/**"})
//    public ResponseEntity<Object> downloadStorageUrl(HttpServletRequest request) {
//        try {
//            String objectName = getObjectNameFromRequest(request);
//            // 如果是"/downloadUrl"路径，使用YML配置的存储类型
//            StorageType storageType;
//            if (request.getRequestURI().contains("downloadUrl")) {
//                // 使用YML中配置的类型
//                storageType = StorageType.valueOf(osstype.toUpperCase());
//            } else {
//                // 默认通过路径判断存储类型
//                storageType = request.getRequestURI().contains("AliOss") ?
//                        StorageType.ALIYUN : StorageType.MINIO;
//            }
//            // 通过缓存的文件签名读取/下载文件
//            String signedUrl = generateSignedUrl(objectName, true, storageType);
//
//            HttpHeaders headers = new HttpHeaders();
//            headers.setLocation(URI.create(signedUrl));
//            return new ResponseEntity<>(headers, HttpStatus.FOUND);
//
//        } catch (StorageException e) {
//            log.error("Storage operation failed", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body("Storage Error: " + e.getMessage());
//        } catch (Exception e) {
//            log.error("Unexpected error", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body("Unexpected Error: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 文件代理访问接口。
//     * <p>
//     * 根据请求 URI 提取出对象名（objectName），并根据租户配置的目录规则
//     * 决定是否跳过 token 校验；未跳过时再进行用户身份校验、黑白名单检查。
//     * 最终根据路径或 YML 配置选择 Aliyun OSS 或 MinIO 存储，
//     * 设置响应头并将对应文件流写入响应。
//     * </p>
//     *
//     * @param request  HttpServletRequest，对应客户端的 HTTP 请求，
//     *                 请求示例：http://host/.../proxy/{objectName}?sec={token}
//     * @param response HttpServletResponse，用于写出文件流及设置 HTTP 状态码和响应头
//     * @param sec      客户端传入的安全访问令牌（token），用于从 tokenService 获取当前登录用户
//     *
//     *                 <ul>
//     *                   <li>步骤 1：通过 HandlerMapping 提取 URI 中的 objectName，例如 “D96M03/202505/1.png”</li>
//     *                   <li>步骤 2：从 Redis 获取当前租户的目录规则列表（UtsDirrulePojo）</li>
//     *                   <li>步骤 3：若 objectName 前缀匹配到 publicmark=1 的规则，则跳过后续的 token 校验和名单检查</li>
//     *                   <li>步骤 4：若未跳过，调用 tokenService.getLoginUser(sec) 获取用户，</li>
//     *                   <ul>
//     *                     <li>查找匹配的目录规则（matchedRule），根据 blackuserids 拆分并拒绝黑名单用户</li>
//     *                     <li>根据 whiteuserids 拆分并拒绝不在白名单的用户</li>
//     *                   </ul>
//     *                   <li>步骤 5：校验 objectName 的合法性（validateObjectName），</li>
//     *                   <li>步骤 6：根据请求路径（AliOss / Minio / proxy）或 YML 中 oss.type，
//     *                            确定 StorageType（ALIYUN 或 MINIO）</li>
//     *                   <li>步骤 7：调用 configureResponseHeaders 设置响应头，
//     *                            再调用 transferFileStream 传输文件流</li>
//     *                   <li>异常处理：针对非法文件名、访问被拒绝、文件处理出错，分别返回 400、403、500</li>
//     *                 </ul>
//     */
//    @ApiOperation("通用图片预览/文件下载（支持Range，代理访问OSS）")
//    @GetMapping("/proxy/**")
//    public void getStorageProxy(HttpServletRequest request, HttpServletResponse response, String sec) {
//        // 1. 获取 objectName 例如http://dev.inksyun.com:31080/utils/File/proxy/D96M03/202505/1.png?sec=b8
//        // 返回objectName: D96M03/202505/1.png
//        String objectName = getObjectNameFromRequest(request);
//
//        // 2. 获取目录规则列表
//        List<UtsDirrulePojo> utsDirruleList = redisService.getCacheObject(MyConstant.DIR_RULE_LIST + InksConstants.DEFAULT_TENANT);
//
//        // 3. 判断是否命中 publicmark=1 的白名单前缀
//        boolean skipSecValidation = false;
//        if (CollectionUtils.isNotEmpty(utsDirruleList)) {
//            for (UtsDirrulePojo rule : utsDirruleList) {
//                if (Objects.equals(rule.getPublicmark(), 1)) {
//                    String prefix = rule.getDirname();
//                    if (objectName.startsWith(prefix)) {
//                        skipSecValidation = true;
//                        break;
//                    }
//                }
//            }
//        }
//        // 4. 校验token-sec（非公共目录时才校验白名单和黑名单）
//        if (!skipSecValidation) {
//            // 4.1 获取当前登录用户
//            LoginUser loginUser = tokenService.getLoginUser(sec);
//            if (CollectionUtils.isNotEmpty(utsDirruleList)) {
//
//                String userId = loginUser.getUserid();
//
//                // 4.2 找到和 objectName 匹配的目录规则
//                UtsDirrulePojo matchedRule = null;
//                for (UtsDirrulePojo rule : utsDirruleList) {
//                    String prefix = rule.getDirname();
//                    if (objectName.startsWith(prefix)) {
//                        matchedRule = rule;
//                        break;
//                    }
//                }
//
//                // 4.3 黑名单校验
//                if (matchedRule != null && StringUtils.isNotBlank(matchedRule.getBlackuserids())) {
//                    List<String> blackIds = Arrays.asList(matchedRule.getBlackuserids().split(","));
//                    if (blackIds.contains(userId)) {
//                        response.setStatus(HttpStatus.FORBIDDEN.value());
//                        log.error("用户 {} 在黑名单中，禁止访问目录 {}", userId, matchedRule.getDirname());
//                        return;
//                    }
//                }
//
//                // 4.4 白名单校验（如果配置了白名单，则不在名单中的用户一律拒绝）
//                if (matchedRule != null && StringUtils.isNotBlank(matchedRule.getWhiteuserids())) {
//                    List<String> whiteIds = Arrays.asList(matchedRule.getWhiteuserids().split(","));
//                    if (!whiteIds.contains(userId)) {
//                        response.setStatus(HttpStatus.FORBIDDEN.value());
//                        log.error("用户 {} 不在白名单中，禁止访问目录 {}", userId, matchedRule.getDirname());
//                        return;
//                    }
//                }
//                // 如果既没配置白名单，也没配置黑名单，或都校验通过，则继续后续逻辑
//            }
//        }
//
//        try {
//            validateObjectName(objectName); // 安全检查
//
//            // 5. 选择存储类型
//            StorageType storageType;
//            if (request.getRequestURI().contains("proxy")) {
//                storageType = StorageType.valueOf(osstype.toUpperCase());
//            } else {
//                storageType = request.getRequestURI().contains("AliOss") ?
//                        StorageType.ALIYUN : StorageType.MINIO;
//            }
//
//            // 设置响应头并传输文件
//            configureResponseHeaders(response, objectName);
//            transferFileStream(objectName, storageType, response);
//
//        } catch (IllegalArgumentException e) {
//            handleError(response, HttpServletResponse.SC_BAD_REQUEST,
//                    "无效的文件名: " + objectName, e);
//        } catch (SecurityException e) {
//            handleError(response, HttpServletResponse.SC_FORBIDDEN,
//                    "访问被拒绝: " + objectName, e);
//        } catch (Exception e) {
//            handleError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
//                    "处理文件时发生错误: " + objectName, e);
//        }
//    }
//
//
//    private void validateObjectName(String objectName) {
//        if (objectName == null || objectName.isEmpty()) {
//            throw new IllegalArgumentException("文件名不能为空");
//        }
//        // 防止目录遍历攻击
//        if (objectName.contains("..") || objectName.contains("//")) {
//            throw new SecurityException("检测到非法的文件路径");
//        }
//    }
//
//    private void configureResponseHeaders(HttpServletResponse response, String objectName) {
//        response.setContentType(determineContentType(objectName));
//        response.setHeader("Cache-Control", "max-age=604800"); // 7天缓存
//        response.setHeader("X-Content-Type-Options", "nosniff");
//        response.setHeader("X-XSS-Protection", "1; mode=block");
//    }
//
//    private void transferFileStream(String objectName, StorageType storageType,
//                                    HttpServletResponse response) throws Exception {
//        try (InputStream inputStream = getObjectStream(objectName, storageType);
//             OutputStream outputStream = response.getOutputStream()) {
//
//            byte[] buffer = new byte[8192];
//            int bytesRead;
//            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                outputStream.write(buffer, 0, bytesRead);
//            }
//            outputStream.flush();
//        }
//    }
//
//    private InputStream getObjectStream(String objectName, StorageType storageType) throws Exception {
//        try {
//            if (storageType == StorageType.ALIYUN) {
//                return getAliOssStream(objectName);
//            } else {
//                return getMinioStream(objectName);
//            }
//        } catch (Exception e) {
//            log.error("获取文件流失败 - {}: {}", storageType, objectName, e);
//            throw new IOException("获取文件流失败", e);
//        }
//    }
//
//    private InputStream getAliOssStream(String objectName) {
//        OSSObject ossObject = ossClient.getObject(BUCKET_NAME_ALIYUN, objectName);
//        return ossObject.getObjectContent();
//    }
//
//    private InputStream getMinioStream(String objectName) throws Exception {
//        return minioClient.getObject(
//                GetObjectArgs.builder()
//                        .bucket(BUCKET_NAME_MINIO)
//                        .object(objectName)
//                        .build()
//        );
//    }
//
//    private String determineContentType(String objectName) {
//        String extension = FilenameUtils.getExtension(objectName).toLowerCase();
//        return CONTENT_TYPE_MAP.getOrDefault(extension, "application/octet-stream");
//    }
//
//    private void handleError(HttpServletResponse response, int status, String message, Exception e) {
//        log.error(message, e);
//        try {
//            response.sendError(status, message);
//        } catch (IOException ex) {
//            log.error("发送错误响应失败", ex);
//        }
//    }
//
//
//    @Scheduled(fixedRate = 3600000) // 每小时执行一次
//    public void cleanExpiredCache() {
//        int beforeSize = urlCache.size();
//        urlCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
//        int afterSize = urlCache.size();
//        log.info("清理过期缓存完成(minio,aliyun图片签名缓存)，清理前：{}，清理后：{}", beforeSize, afterSize);
//    }
//
//
//    // 统一的URL获取方法
//    //http://dev.inksyun.com:31080/utils/File/proxy/D96M03/202505/1.png?sec=b8
//    //请求路径: /File/proxy/D96M03/202505/1.png, 匹配模式: /File/proxy/**, 返回: D96M03/202505/1.png
//    private String getObjectNameFromRequest(HttpServletRequest request) {
//        String uri = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
//        String pattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
//        log.info("请求路径: {}, 匹配模式: {}, 返回: {}", uri, pattern, antPathMatcher.extractPathWithinPattern(pattern, uri));
//        return antPathMatcher.extractPathWithinPattern(pattern, uri);
//    }
//
//    // 统一的签名URL生成方法
//    private String generateSignedUrl(String objectName, boolean isDownload, StorageType storageType) {
//        String cacheKey = getCacheKey(objectName, isDownload, storageType);
//        SignedUrlCache cachedUrl = urlCache.get(cacheKey);
//
//        if (cachedUrl != null && !cachedUrl.isExpired()) {
//            log.info("使用缓存中的{}签名，key: {}", storageType, cacheKey);
//            return cachedUrl.getUrl();
//        }
//
//        try {
//            String signedUrl = storageType == StorageType.ALIYUN ?
//                    generateAliOssSignedUrl(objectName, isDownload) :
//                    generateMinioSignedUrl(objectName, isDownload);
//
//            // 存入缓存
//            cacheSignedUrl(cacheKey, signedUrl, isDownload);
//            return signedUrl;
//
//        } catch (Exception e) {
//            log.error("生成{}签名URL失败: ", storageType, e);
//            throw new StorageException("生成签名URL失败", e);
//        }
//    }
//
//    // AliOSS签名URL生成
//    private String generateAliOssSignedUrl(String objectName, boolean isDownload) throws
//            UnsupportedEncodingException {
//        Date expiration = new Date(System.currentTimeMillis() + URL_EXPIRATION_TIME);
//        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(BUCKET_NAME_ALIYUN, objectName, HttpMethod.GET);
//        request.setExpiration(expiration);
//
//        if (isDownload) {
//            String fileName = getFileNameFromPath(objectName);
//            ResponseHeaderOverrides responseHeaders = new ResponseHeaderOverrides();
//            responseHeaders.setContentDisposition("attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
//            request.setResponseHeaders(responseHeaders);
//        }
//
//        return ossClient.generatePresignedUrl(request).toString();
//    }
//
//    // MinIO签名URL生成
//    private String generateMinioSignedUrl(String objectName, boolean isDownload) throws Exception {
//        GetPresignedObjectUrlArgs.Builder builder = GetPresignedObjectUrlArgs.builder()
//                .method(Method.GET)
//                .bucket(BUCKET_NAME_MINIO)
//                .object(objectName);
//
//        if (isDownload) {
//            String fileName = getFileNameFromPath(objectName);
//            Map<String, String> reqParams = new HashMap<>();
//            reqParams.put("response-content-disposition",
//                    "attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
//            builder.extraQueryParams(reqParams);
//        }
//
//        return minioClient.getPresignedObjectUrl(builder.build());
//    }
//
//    // 缓存签名URL
//    private void cacheSignedUrl(String cacheKey, String signedUrl, boolean isDownload) {
//        SignedUrlCache newCache = new SignedUrlCache(
//                signedUrl,
//                new Date(System.currentTimeMillis() + URL_EXPIRATION_TIME - CACHE_EXPIRATION_BUFFER),
//                isDownload
//        );
//        urlCache.put(cacheKey, newCache);
//        log.info("新的签名URL已缓存，key: {}", cacheKey);
//    }
//
//    private String getCacheKey(String objectName, boolean isDownload, StorageType storageType) {
//        return String.format("%s:%s:%s", storageType, objectName, isDownload);
//    }
//
//    private String getFileNameFromPath(String objectName) {
//        return objectName.substring(objectName.lastIndexOf("/") + 1);
//    }
//
//    public class SignedUrlCache {
//        private String url;
//        private Date expirationTime;
//        private boolean isDownload;
//
//        // 无参构造函数
//        public SignedUrlCache() {
//        }
//
//        // 有参构造函数
//        public SignedUrlCache(String url, Date expirationTime, boolean isDownload) {
//            this.url = url;
//            this.expirationTime = expirationTime;
//            this.isDownload = isDownload;
//        }
//
//        // Getter 和 Setter 方法
//        public String getUrl() {
//            return url;
//        }
//
//        public void setUrl(String url) {
//            this.url = url;
//        }
//
//        public Date getExpirationTime() {
//            return expirationTime;
//        }
//
//        public void setExpirationTime(Date expirationTime) {
//            this.expirationTime = expirationTime;
//        }
//
//        public boolean isDownload() {
//            return isDownload;
//        }
//
//        public void setDownload(boolean download) {
//            isDownload = download;
//        }
//
//        // 判断是否过期
//        public boolean isExpired() {
//            return new Date().after(expirationTime);
//        }
//    }
//
//
//    @ApiOperation(value = "获取文件树结构及大小(MB) path：文件夹路径，recursive：是否递归(默认false只展示一层)", notes = "")
//    @GetMapping(value = "getFileTreeSizes")
//    public R getFileTreeSizes(String bucket, String path, @RequestParam(defaultValue = "false") boolean recursive,
//                              @RequestParam(required = false) String osstype) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (isBlank(osstype)) {
//            osstype = this.osstype;
//        }
//        try {
//            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
//                return R.ok(this.fileInfoMinioService.getFileTreeSizes(bucket, path, recursive));
//            } else if (osstype.equals(OssConstant.OSSTYPE_ALIYUN)) {
//                return R.ok(this.fileInfoAliyunService.getFileTreeSizes(bucket, path, recursive));
//            } else {
//                return R.fail("osstype:oss类型错误");
//            }
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "批量下载文件目录并打包为zip (zip命名规则：bucket_path.zip）", notes = "")
//    @GetMapping(value = "downloadFilesAsZip", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
//    public ResponseEntity<byte[]> downloadFilesAsZip(
//            @RequestParam String bucket,
//            @RequestParam String path,
//            @RequestParam(required = false) String osstype) {
//
//        if (isBlank(osstype)) {
//            osstype = this.osstype;
//        }
//
//        try {
//            byte[] zipBytes;
//            String filename;
//
//            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
//                zipBytes = this.fileInfoMinioService.downloadFilesAsZip(bucket, path);
//                // 下载的文件名: bucket_path.zip 把/替换成_
//                filename = bucket + "_" + path.replace("/", "_") + ".zip";
//            } else if (osstype.equals(OssConstant.OSSTYPE_ALIYUN)) {
//                zipBytes = this.fileInfoAliyunService.downloadFilesAsZip(bucket, path);
//                filename = "aliyun_未完成功能.zip";
//            } else {
//                return ResponseEntity.badRequest().body("osstype:oss类型错误".getBytes());
//            }
//
//            return ResponseEntity.ok()
//                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
//                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
//                    .contentLength(zipBytes.length)
//                    .body(zipBytes);
//
//        } catch (Exception e) {
//            log.error("下载并打包ZIP失败", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(("下载失败: " + e.getMessage()).getBytes());
//        }
//    }
//
//
//    @ApiOperation(value = "上传 ZIP 文件并恢复目录结构到 MinIO指定路径", notes = "上传 ZIP 文件并恢复原始目录结构")
//    @PostMapping(value = "uploadZipStructure", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    public R<List<String>> uploadZipStructure(
//            @RequestParam("zipFile") MultipartFile zipFile,
//            @RequestParam String bucket,
//            @RequestParam(required = false) String path,
//            @RequestParam(required = false) String osstype) {
//
//        if (zipFile.isEmpty()) {
//            return R.fail("上传的文件为空");
//        }
//
//        if (isBlank(osstype)) {
//            osstype = this.osstype;
//        }
//
//        try {
//            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
//                List<String> uploadedFiles = this.fileInfoMinioService.uploadZipAndRebuildStructure(zipFile, bucket, path);
//                return R.ok(uploadedFiles, "成功上传 " + uploadedFiles.size() + " 个文件");
//            } else {
//                return R.fail("不支持的存储类型: " + osstype);
//            }
//        } catch (Exception e) {
//            log.error("上传 ZIP 并恢复结构失败", e);
//            return R.fail("" +
//                    "上传失败: " + e.getMessage());
//        }
//    }
//
//
//    @ApiOperation(value = "批量删除指定路径下的所有文件", notes = "bucket：桶名，path：路径（如 folder/）")
//    @GetMapping(value = "removeFilesByPath")
//    public R<Integer> removeFilesByPath(
//            @RequestParam String bucket,
//            @RequestParam String path,
//            @RequestParam(required = false) String osstype) {
//
//        if (isBlank(osstype)) {
//            osstype = this.osstype;
//        }
//
//        try {
//            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
//                int count = this.fileInfoMinioService.removeFilesByPath(bucket, path);
//                return R.ok(count, "成功删除 " + count + " 个文件");
//            } else if (osstype.equals(OssConstant.OSSTYPE_ALIYUN)) {
//                //int count = this.fileInfoAliyunService.removeFilesByPath(bucket, path);
//                //return R.ok(count, "成功删除 " + count + " 个文件");
//                return R.fail("不支持的存储类型: " + osstype);
//            } else {
//                return R.fail("osstype: oss类型错误");
//            }
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//}
