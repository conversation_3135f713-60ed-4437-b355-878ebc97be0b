package inks.service.std.utils.config.ossConfig;

import inks.service.std.utils.oss.MinioStorage;
import inks.service.std.utils.oss.Storage;
import io.minio.MinioClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * minio oss配置类
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({MinioClient.class})
public class MinioOSSConfiguration {

    MinioOSSConfiguration() {
    }

    @Bean
//    @ConditionalOnProperty(prefix = "oss", name = {"type"}, havingValue = "minio")
    Storage minioStorage(OSSProperties ossProperties){
        OSSProperties.Minio minio = ossProperties.getMinio();

        MinioClient minioClient =
                MinioClient.builder()
                        .endpoint(minio.getEndpoint())
                        .credentials(minio.getAccessKey(), minio.getSecretKey())
                        .build();
        return new MinioStorage(minioClient);
    }

    @Bean
//    @ConditionalOnProperty(prefix = "oss", name = {"type"}, havingValue = "minio")
    MinioClient minioClient(OSSProperties ossProperties)  {
        OSSProperties.Minio minio = ossProperties.getMinio();
        return MinioClient.builder()
                .endpoint(minio.getEndpoint())
                .credentials(minio.getAccessKey(), minio.getSecretKey())
                .build();
    }

}
