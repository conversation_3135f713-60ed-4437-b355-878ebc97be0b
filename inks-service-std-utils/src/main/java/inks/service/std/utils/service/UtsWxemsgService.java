package inks.service.std.utils.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.utils.domain.pojo.UtsWxemsgPojo;

/**
 * 企业微信信息(UtsWxemsg)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:38
 */
public interface UtsWxemsgService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxemsgPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsWxemsgPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsWxemsgPojo 实例对象
     * @return 实例对象
     */
    UtsWxemsgPojo insert(UtsWxemsgPojo utsWxemsgPojo);

    /**
     * 修改数据
     *
     * @param utsWxemsgpojo 实例对象
     * @return 实例对象
     */
    UtsWxemsgPojo update(UtsWxemsgPojo utsWxemsgpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxemsgPojo getEntityByMsgCode(String key, String tid);

    String getWxeUserIdByOmsUserid(String omsUserid, String tid);
}
