/**
  * Copyright 2022 json.cn 
  */
package inks.service.std.utils.domain.CompanyJsonBean;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class PhoneSourceList {

    private String phoneNumber;
    private String oriPhoneNumber;
    private String showSource;
    private int hasMoreCompany;
    private int companyCount;
    private String companyCountStr;
    private String companyTotalStr;
    private int phoneType;
    private String phoneTips;
    private String phoneTag;
    private String phoneTagList;
    public void setPhoneNumber(String phoneNumber) {
         this.phoneNumber = phoneNumber;
     }
     public String getPhoneNumber() {
         return phoneNumber;
     }

    public void setOriPhoneNumber(String oriPhoneNumber) {
         this.oriPhoneNumber = oriPhoneNumber;
     }
     public String getOriPhoneNumber() {
         return oriPhoneNumber;
     }

    public void setShowSource(String showSource) {
         this.showSource = showSource;
     }
     public String getShowSource() {
         return showSource;
     }

    public void setHasMoreCompany(int hasMoreCompany) {
         this.hasMoreCompany = hasMoreCompany;
     }
     public int getHasMoreCompany() {
         return hasMoreCompany;
     }

    public void setCompanyCount(int companyCount) {
         this.companyCount = companyCount;
     }
     public int getCompanyCount() {
         return companyCount;
     }

    public void setCompanyCountStr(String companyCountStr) {
         this.companyCountStr = companyCountStr;
     }
     public String getCompanyCountStr() {
         return companyCountStr;
     }

    public void setCompanyTotalStr(String companyTotalStr) {
         this.companyTotalStr = companyTotalStr;
     }
     public String getCompanyTotalStr() {
         return companyTotalStr;
     }

    public void setPhoneType(int phoneType) {
         this.phoneType = phoneType;
     }
     public int getPhoneType() {
         return phoneType;
     }

    public void setPhoneTips(String phoneTips) {
         this.phoneTips = phoneTips;
     }
     public String getPhoneTips() {
         return phoneTips;
     }

    public void setPhoneTag(String phoneTag) {
         this.phoneTag = phoneTag;
     }
     public String getPhoneTag() {
         return phoneTag;
     }

    public void setPhoneTagList(String phoneTagList) {
         this.phoneTagList = phoneTagList;
     }
     public String getPhoneTagList() {
         return phoneTagList;
     }

}