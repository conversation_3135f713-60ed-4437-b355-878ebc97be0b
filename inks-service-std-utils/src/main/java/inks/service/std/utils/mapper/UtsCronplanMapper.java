package inks.service.std.utils.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.utils.domain.UtsCronplanEntity;
import inks.service.std.utils.domain.pojo.UtsCronplanPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (UtsCronplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-27 15:57:34
 */
@Mapper
public interface UtsCronplanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsCronplanPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsCronplanPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param utsCronplanEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsCronplanEntity utsCronplanEntity);

    
    /**
     * 修改数据
     *
     * @param utsCronplanEntity 实例对象
     * @return 影响行数
     */
    int update(UtsCronplanEntity utsCronplanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<UtsCronplanPojo> getAllList();
}

