
// SqlExecutionResult.java
package inks.service.std.utils.service.sql;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel("SQL执行结果")
public class SqlExecutionResult {
    @ApiModelProperty("执行结果数据")
    private Object data;
    
    @ApiModelProperty("SQL类型")
    private String type;
    
    @ApiModelProperty("影响行数")
    private Integer affectedRows;
    
    @ApiModelProperty("总记录数")
    private Integer totalRows;
    
    @ApiModelProperty("执行时间(毫秒)")
    private Long executionTime;
    
    @ApiModelProperty("是否成功")
    private boolean success;
    
    @ApiModelProperty("错误信息")
    private String errorMessage;
    
    @ApiModelProperty("错误码")
    private Integer errorCode;
    
    @ApiModelProperty("SQL状态")
    private String sqlState;

    //當前時間
    private String currentTime;
    //初始化当前时间 YYYY-MM-DD HH:mm:ss
    public SqlExecutionResult() {
        this.currentTime = new Date().toString();
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getAffectedRows() {
        return affectedRows;
    }

    public void setAffectedRows(Integer affectedRows) {
        this.affectedRows = affectedRows;
    }

    public Integer getTotalRows() {
        return totalRows;
    }

    public void setTotalRows(Integer totalRows) {
        this.totalRows = totalRows;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getSqlState() {
        return sqlState;
    }

    public void setSqlState(String sqlState) {
        this.sqlState = sqlState;
    }

    public String getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(String currentTime) {
        this.currentTime = currentTime;
    }
}

