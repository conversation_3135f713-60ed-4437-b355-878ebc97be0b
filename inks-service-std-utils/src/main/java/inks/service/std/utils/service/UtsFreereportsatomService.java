package inks.service.std.utils.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.utils.domain.pojo.UtsFreereportsatomPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 自由报表原子数据(UtsFreereportsatom)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-23 09:51:05
 */
public interface UtsFreereportsatomService {

    UtsFreereportsatomPojo getEntity(String key,String tid);

    PageInfo<UtsFreereportsatomPojo> getPageList(QueryParam queryParam);

    UtsFreereportsatomPojo insert(UtsFreereportsatomPojo utsFreereportsatomPojo);

    UtsFreereportsatomPojo update(UtsFreereportsatomPojo utsFreereportsatompojo);

    int delete(String key,String tid);

    UtsFreereportsatomPojo getEntityByCode(String code, String tenantid);
}
