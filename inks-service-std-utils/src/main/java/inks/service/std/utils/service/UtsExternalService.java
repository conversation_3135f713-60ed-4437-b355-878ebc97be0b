package inks.service.std.utils.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.utils.domain.pojo.UtsExternalPojo;

/**
 * 扩展应用(UtsExternal)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-24 14:26:13
 */
public interface UtsExternalService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsExternalPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsExternalPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsExternalPojo 实例对象
     * @return 实例对象
     */
    UtsExternalPojo insert(UtsExternalPojo utsExternalPojo);

    /**
     * 修改数据
     *
     * @param utsExternalpojo 实例对象
     * @return 实例对象
     */
    UtsExternalPojo update(UtsExternalPojo utsExternalpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
