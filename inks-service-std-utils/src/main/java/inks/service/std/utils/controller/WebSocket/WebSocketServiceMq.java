//package inks.service.std.utils.controller.WebSocket;
//
/// **
// * <AUTHOR>
// * @date 2023年02月22日 16:49
// */
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.rabbitmq.client.Channel;
//import inks.service.std.utils.utils.PrintColor;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.rabbit.annotation.Queue;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//
////用于接收MQ消息
//@Service
//public class WebSocketServiceMq {
//    int i = 0;
//    @Resource
//    private WebSocketServer webSocketServer;
//
//    //MQ消息监听 队列名：updateGoodsQty 用于更新货品信息
//    @RabbitListener(queuesToDeclare = @Queue(value = "websocket"))
//    public void process(String msg, Channel channel, Message message) throws IOException {
//        try {
//            PrintColor.zi("[WebSocketServiceMq]接收到MQ消息：" + msg);
//            JSONObject jsonObject = JSONArray.parseObject(msg);
//            // 订阅号
//            String codes = jsonObject.getString("codes");
//            // 消息内容
//            String date = jsonObject.getString("date");
//            PrintColor.zi("订阅号：" + codes + "，消息内容：" + date);
////            //更新类型
////            String type = jsonObject.getString("type");
//            if (StringUtils.isNotBlank(codes)) {
//                webSocketServer.onMessage(codes);
//            }
//
//        } catch (Exception e) {
//            System.out.println("\u001B[31m============catch (Exception e)错误生产者消息为" + msg + "\u001B[0m");
//            e.printStackTrace();
//        }
//        try {
//            i++;
//            LocalDateTime now = LocalDateTime.now();
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
//            String formattedNow = now.format(formatter);
//            // 输出黄色的文本
//            System.out.println("\u001B[33m============channel.basicAck消费第" + i + "条消息:" + msg + "\u001B[0m");
//            System.out.println("\u001B[36m============当前时间：" + formattedNow + "\u001B[0m");
//            //告诉MQ服务器收到这条消息 已经被我消费了 可以在队列删掉 这样以后就不会再发了 否则消息服务器以为这条消息没处理掉 后续还会在发
//            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//    }
//}
