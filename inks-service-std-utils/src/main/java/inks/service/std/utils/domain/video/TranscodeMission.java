package inks.service.std.utils.domain.video;

import java.io.Serializable;
import java.util.Date;

public class TranscodeMission implements Serializable {
    private String url;
    private String resolution;
    private String id;
    private int totalFrames;
    private int currentFrame;
    private double progress;
    private Date startTime;
    private Date endTime;
    private Boolean end = false;

    public double getProgress() {
        return currentFrame*1.0/totalFrames*1.0;
    }

    public void setProgress(double progress) {
        this.progress = progress;
    }

    public void setEnd(Boolean end){
        this.end = end;
        this.endTime = new Date();
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getTotalFrames() {
        return totalFrames;
    }

    public void setTotalFrames(int totalFrames) {
        this.totalFrames = totalFrames;
    }

    public int getCurrentFrame() {
        return currentFrame;
    }

    public void setCurrentFrame(int currentFrame) {
        this.currentFrame = currentFrame;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getEnd() {
        return end;
    }
}
