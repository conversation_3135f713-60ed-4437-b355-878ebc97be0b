package inks.service.std.utils.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.utils.domain.pojo.UtsQuerycenterPojo;
import inks.service.std.utils.domain.UtsQuerycenterEntity;
import inks.service.std.utils.mapper.UtsQuerycenterMapper;
import inks.service.std.utils.service.UtsQuerycenterService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 查询中心(UtsQuerycenter)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-26 13:21:13
 */
@Service("utsQuerycenterService")
public class UtsQuerycenterServiceImpl implements UtsQuerycenterService {
    @Resource
    private UtsQuerycenterMapper utsQuerycenterMapper;

    @Override
    public UtsQuerycenterPojo getEntity(String key, String tid) {
        return this.utsQuerycenterMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<UtsQuerycenterPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsQuerycenterPojo> lst = utsQuerycenterMapper.getPageList(queryParam);
            PageInfo<UtsQuerycenterPojo> pageInfo = new PageInfo<UtsQuerycenterPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsQuerycenterPojo insert(UtsQuerycenterPojo utsQuerycenterPojo) {
        //初始化NULL字段
        cleanNull(utsQuerycenterPojo);
        UtsQuerycenterEntity utsQuerycenterEntity = new UtsQuerycenterEntity(); 
        BeanUtils.copyProperties(utsQuerycenterPojo,utsQuerycenterEntity);
          //生成雪花id
          utsQuerycenterEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsQuerycenterEntity.setRevision(1);  //乐观锁
          this.utsQuerycenterMapper.insert(utsQuerycenterEntity);
        return this.getEntity(utsQuerycenterEntity.getId(),utsQuerycenterEntity.getTenantid());
    }


    @Override
    public UtsQuerycenterPojo update(UtsQuerycenterPojo utsQuerycenterPojo) {
        UtsQuerycenterEntity utsQuerycenterEntity = new UtsQuerycenterEntity(); 
        BeanUtils.copyProperties(utsQuerycenterPojo,utsQuerycenterEntity);
        this.utsQuerycenterMapper.update(utsQuerycenterEntity);
        return this.getEntity(utsQuerycenterEntity.getId(),utsQuerycenterEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.utsQuerycenterMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(UtsQuerycenterPojo utsQuerycenterPojo) {
        if(utsQuerycenterPojo.getQuerycode()==null) utsQuerycenterPojo.setQuerycode("");
        if(utsQuerycenterPojo.getQuerytitle()==null) utsQuerycenterPojo.setQuerytitle("");
        if(utsQuerycenterPojo.getDyntype()==null) utsQuerycenterPojo.setDyntype("");
        if(utsQuerycenterPojo.getDynsentence()==null) utsQuerycenterPojo.setDynsentence("");
        if(utsQuerycenterPojo.getHeadercolumn()==null) utsQuerycenterPojo.setHeadercolumn("");
        if(utsQuerycenterPojo.getScenefield()==null) utsQuerycenterPojo.setScenefield("");
        if(utsQuerycenterPojo.getPermcode()==null) utsQuerycenterPojo.setPermcode("");
        if(utsQuerycenterPojo.getTimelimitmark()==null) utsQuerycenterPojo.setTimelimitmark(0);
        if(utsQuerycenterPojo.getDatabaseid()==null) utsQuerycenterPojo.setDatabaseid("");
        if(utsQuerycenterPojo.getLocalmark()==null) utsQuerycenterPojo.setLocalmark(0);
        if(utsQuerycenterPojo.getDatepath()==null) utsQuerycenterPojo.setDatepath("");
        if(utsQuerycenterPojo.getAuthcode()==null) utsQuerycenterPojo.setAuthcode("");
        if(utsQuerycenterPojo.getRownum()==null) utsQuerycenterPojo.setRownum(0);
        if(utsQuerycenterPojo.getCreateby()==null) utsQuerycenterPojo.setCreateby("");
        if(utsQuerycenterPojo.getCreatebyid()==null) utsQuerycenterPojo.setCreatebyid("");
        if(utsQuerycenterPojo.getCreatedate()==null) utsQuerycenterPojo.setCreatedate(new Date());
        if(utsQuerycenterPojo.getLister()==null) utsQuerycenterPojo.setLister("");
        if(utsQuerycenterPojo.getListerid()==null) utsQuerycenterPojo.setListerid("");
        if(utsQuerycenterPojo.getModifydate()==null) utsQuerycenterPojo.setModifydate(new Date());
        if(utsQuerycenterPojo.getCustom1()==null) utsQuerycenterPojo.setCustom1("");
        if(utsQuerycenterPojo.getCustom2()==null) utsQuerycenterPojo.setCustom2("");
        if(utsQuerycenterPojo.getCustom3()==null) utsQuerycenterPojo.setCustom3("");
        if(utsQuerycenterPojo.getCustom4()==null) utsQuerycenterPojo.setCustom4("");
        if(utsQuerycenterPojo.getCustom5()==null) utsQuerycenterPojo.setCustom5("");
        if(utsQuerycenterPojo.getTenantid()==null) utsQuerycenterPojo.setTenantid("");
        if(utsQuerycenterPojo.getTenantname()==null) utsQuerycenterPojo.setTenantname("");
        if(utsQuerycenterPojo.getRevision()==null) utsQuerycenterPojo.setRevision(0);
   }

}
