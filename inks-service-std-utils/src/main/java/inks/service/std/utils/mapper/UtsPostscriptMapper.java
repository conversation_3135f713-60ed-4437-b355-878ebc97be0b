package inks.service.std.utils.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.utils.domain.pojo.UtsPostscriptPojo;
import inks.service.std.utils.domain.UtsPostscriptEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 单据附言(UtsPostscript)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-14 09:09:57
 */
@Mapper
public interface UtsPostscriptMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsPostscriptPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsPostscriptPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param utsPostscriptEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsPostscriptEntity utsPostscriptEntity);

    
    /**
     * 修改数据
     *
     * @param utsPostscriptEntity 实例对象
     * @return 影响行数
     */
    int update(UtsPostscriptEntity utsPostscriptEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                                      }

