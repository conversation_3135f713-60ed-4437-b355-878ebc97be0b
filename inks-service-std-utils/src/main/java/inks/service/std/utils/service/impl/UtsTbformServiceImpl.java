package inks.service.std.utils.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.utils.domain.UtsTbformEntity;
import inks.service.std.utils.domain.UtsTbformitemEntity;
import inks.service.std.utils.domain.pojo.UtsTbformPojo;
import inks.service.std.utils.domain.pojo.UtsTbformitemPojo;
import inks.service.std.utils.domain.pojo.UtsTbformitemdetailPojo;
import inks.service.std.utils.mapper.UtsTbformMapper;
import inks.service.std.utils.mapper.UtsTbformitemMapper;
import inks.service.std.utils.service.UtsTbformService;
import inks.service.std.utils.service.UtsTbformitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 表格窗体(UtsTbform)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-08 09:49:43
 */
@Service("utsTbformService")
public class UtsTbformServiceImpl implements UtsTbformService {
    @Resource
    private UtsTbformMapper utsTbformMapper;

    @Resource
    private UtsTbformitemMapper utsTbformitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private UtsTbformitemService utsTbformitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsTbformPojo getEntity(String key, String tid) {
        return this.utsTbformMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<UtsTbformitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsTbformitemdetailPojo> lst = utsTbformMapper.getPageList(queryParam);
            PageInfo<UtsTbformitemdetailPojo> pageInfo = new PageInfo<UtsTbformitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsTbformPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            UtsTbformPojo utsTbformPojo = this.utsTbformMapper.getEntity(key, tid);
            //读取子表
            utsTbformPojo.setItem(utsTbformitemMapper.getList(utsTbformPojo.getId(), utsTbformPojo.getTenantid()));
            return utsTbformPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<UtsTbformPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsTbformPojo> lst = utsTbformMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(utsTbformitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<UtsTbformPojo> pageInfo = new PageInfo<UtsTbformPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<UtsTbformPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsTbformPojo> lst = utsTbformMapper.getPageTh(queryParam);
            PageInfo<UtsTbformPojo> pageInfo = new PageInfo<UtsTbformPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param utsTbformPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public UtsTbformPojo insert(UtsTbformPojo utsTbformPojo) {
//初始化NULL字段
        if (utsTbformPojo.getModulecode() == null) utsTbformPojo.setModulecode("");
        if (utsTbformPojo.getSourceid() == null) utsTbformPojo.setSourceid("");
        if (utsTbformPojo.getSourcetype() == null) utsTbformPojo.setSourcetype(0);
        if (utsTbformPojo.getFormname() == null) utsTbformPojo.setFormname("");
        if (utsTbformPojo.getDescription() == null) utsTbformPojo.setDescription("");
        if (utsTbformPojo.getUserid() == null) utsTbformPojo.setUserid("");
        if (utsTbformPojo.getFormtype() == null) utsTbformPojo.setFormtype("");
        if (utsTbformPojo.getFormstatus() == null) utsTbformPojo.setFormstatus(0);
        if (utsTbformPojo.getIsdeleted() == null) utsTbformPojo.setIsdeleted(0);
        if (utsTbformPojo.getIsfolder() == null) utsTbformPojo.setIsfolder(0);
        if (utsTbformPojo.getFolderid() == null) utsTbformPojo.setFolderid(0);
        if (utsTbformPojo.getRemark() == null) utsTbformPojo.setRemark("");
        if (utsTbformPojo.getCreatebyid() == null) utsTbformPojo.setCreatebyid("");
        if (utsTbformPojo.getCreateby() == null) utsTbformPojo.setCreateby("");
        if (utsTbformPojo.getCreatedate() == null) utsTbformPojo.setCreatedate(new Date());
        if (utsTbformPojo.getListerid() == null) utsTbformPojo.setListerid("");
        if (utsTbformPojo.getLister() == null) utsTbformPojo.setLister("");
        if (utsTbformPojo.getModifydate() == null) utsTbformPojo.setModifydate(new Date());
        if (utsTbformPojo.getCustom1() == null) utsTbformPojo.setCustom1("");
        if (utsTbformPojo.getCustom2() == null) utsTbformPojo.setCustom2("");
        if (utsTbformPojo.getCustom3() == null) utsTbformPojo.setCustom3("");
        if (utsTbformPojo.getCustom4() == null) utsTbformPojo.setCustom4("");
        if (utsTbformPojo.getCustom5() == null) utsTbformPojo.setCustom5("");
        if (utsTbformPojo.getCustom6() == null) utsTbformPojo.setCustom6("");
        if (utsTbformPojo.getCustom7() == null) utsTbformPojo.setCustom7("");
        if (utsTbformPojo.getCustom8() == null) utsTbformPojo.setCustom8("");
        if (utsTbformPojo.getCustom9() == null) utsTbformPojo.setCustom9("");
        if (utsTbformPojo.getCustom10() == null) utsTbformPojo.setCustom10("");
        if (utsTbformPojo.getDeptid() == null) utsTbformPojo.setDeptid("");
        if (utsTbformPojo.getTenantid() == null) utsTbformPojo.setTenantid("");
        if (utsTbformPojo.getTenantname() == null) utsTbformPojo.setTenantname("");
        if (utsTbformPojo.getRevision() == null) utsTbformPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        UtsTbformEntity utsTbformEntity = new UtsTbformEntity();
        BeanUtils.copyProperties(utsTbformPojo, utsTbformEntity);

        //设置id和新建日期
        utsTbformEntity.setId(id);
        utsTbformEntity.setRevision(1);  //乐观锁
        //插入主表
        this.utsTbformMapper.insert(utsTbformEntity);
        //Item子表处理
        List<UtsTbformitemPojo> lst = utsTbformPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                UtsTbformitemPojo itemPojo = this.utsTbformitemService.clearNull(lst.get(i));
                UtsTbformitemEntity utsTbformitemEntity = new UtsTbformitemEntity();
                BeanUtils.copyProperties(itemPojo, utsTbformitemEntity);
                //设置id和Pid
                utsTbformitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                utsTbformitemEntity.setPid(id);
                utsTbformitemEntity.setTenantid(utsTbformPojo.getTenantid());
                utsTbformitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.utsTbformitemMapper.insert(utsTbformitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(utsTbformEntity.getId(), utsTbformEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param utsTbformPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public UtsTbformPojo update(UtsTbformPojo utsTbformPojo) {
        //主表更改
        UtsTbformEntity utsTbformEntity = new UtsTbformEntity();
        BeanUtils.copyProperties(utsTbformPojo, utsTbformEntity);
        this.utsTbformMapper.update(utsTbformEntity);
        if (utsTbformPojo.getItem() != null) {
            //Item子表处理
            List<UtsTbformitemPojo> lst = utsTbformPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = utsTbformMapper.getDelItemIds(utsTbformPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.utsTbformitemMapper.delete(lstDelIds.get(i), utsTbformEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    UtsTbformitemEntity utsTbformitemEntity = new UtsTbformitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        UtsTbformitemPojo itemPojo = this.utsTbformitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, utsTbformitemEntity);
                        //设置id和Pid
                        utsTbformitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        utsTbformitemEntity.setPid(utsTbformEntity.getId());  // 主表 id
                        utsTbformitemEntity.setTenantid(utsTbformPojo.getTenantid());   // 租户id
                        utsTbformitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.utsTbformitemMapper.insert(utsTbformitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), utsTbformitemEntity);
                        utsTbformitemEntity.setTenantid(utsTbformPojo.getTenantid());
                        this.utsTbformitemMapper.update(utsTbformitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(utsTbformEntity.getId(), utsTbformEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        UtsTbformPojo utsTbformPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<UtsTbformitemPojo> lst = utsTbformPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.utsTbformitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.utsTbformMapper.delete(key, tid);
    }


}
