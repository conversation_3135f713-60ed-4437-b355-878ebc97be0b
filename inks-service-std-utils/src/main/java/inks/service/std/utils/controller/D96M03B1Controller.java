package inks.service.std.utils.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.utils.domain.pojo.UtsAttachmentPojo;
import inks.service.std.utils.service.UtsAttachmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 附件中心(Uts_Attachment)表控制层
 *
 * <AUTHOR>
 * @since 2022-05-27 08:54:27
 */
@RestController
@RequestMapping("D96M03B1")
@Api(tags = "D96M03B1:附件中心")
public class D96M03B1Controller extends UtsAttachmentController {

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(D96M03B1Controller.class);
    /**
     * 服务对象
     */
    @Resource
    private UtsAttachmentService utsAttachmentService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "上传附件", notes = "上传附件")
    @PostMapping("upload")
    public R<UtsAttachmentPojo> upload(MultipartFile file, String relateid,
                                       @RequestParam(defaultValue = "0") int publicmark,
                                       @RequestParam(required = false) String module,
                                       @RequestParam(required = false) String modulecode,
                                       String bucket, String osstype) {
        LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        try {
            return R.ok(this.utsAttachmentService.upload(file, "D96M03", relateid, publicmark, module, modulecode, bucket, osstype, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询查modulecode和module匹配数据中    且自己+公共的", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Uts_Attachment.List")
    public R<List<UtsAttachmentPojo>> getList(@RequestParam(required = false) String module, @RequestParam(required = false) String modulecode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsAttachmentService.getList(module, modulecode, loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除附件中心", notes = "删除附件中心", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Uts_Attachment.Delete")
    public R<Integer> delete(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsAttachmentService.delete(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //@ApiOperation(value = "生成分享文件", notes = "生成分享文件", produces = "application/json")
    //@RequestMapping(value = "/share", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_Attachment.List")
    //public R<String> shareFile(@RequestBody String json) {
    //    try {
    //        Map<String, Object> params = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
    //        });
    //        LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());
    //        String workid = this.utsAttachmentService.shareFile(params, loginUser);
    //        return R.ok(workid);
    //    } catch (Exception e) {
    //        return R.fail(e.getMessage());
    //    }
    //}
    //
    //@ApiOperation(value = "保存分享文件", notes = "保存分享文件", produces = "application/json")
    //@RequestMapping(value = "/pullFile", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_Attachment.List")
    //public R<String> pullFile(@RequestBody String json) {
    //    try {
    //        Map<String, Object> params = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
    //        });
    //        LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());
    //        this.utsAttachmentService.pullFile(params, loginUser);
    //        return R.ok("success");
    //    } catch (Exception e) {
    //        return R.fail(e.getMessage());
    //    }
    //}
}
