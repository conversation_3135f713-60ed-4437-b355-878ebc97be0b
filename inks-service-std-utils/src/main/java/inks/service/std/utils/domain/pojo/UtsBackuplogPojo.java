package inks.service.std.utils.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 数据库日志(UtsBackuplog)实体类
 *
 * <AUTHOR>
 * @since 2025-05-21 10:48:16
 */
public class UtsBackuplogPojo implements Serializable {
    private static final long serialVersionUID = 785189070216211553L;
     // id
    @Excel(name = "id") 
    private String id;
     // 操作类型(0=备份 1=还原)
    @Excel(name = "操作类型(0=备份 1=还原)") 
    private Integer operatetype;
     // 对应Uts_BackupConfig.id
    @Excel(name = "对应Uts_BackupConfig.id") 
    private String configid;
     // 配置名称
    @Excel(name = "配置名称") 
    private String configname;
     // 备份/还原开始时间
    @Excel(name = "备份/还原开始时间") 
    private Date backuptime;
     // 完成时间
    @Excel(name = "完成时间") 
    private Date finishtime;
     // 耗时(秒)
    @Excel(name = "耗时(秒)") 
    private Integer duration;
     // 本地保存路径
    @Excel(name = "本地保存路径") 
    private String localpath;
     // 云端/接口保存路径
    @Excel(name = "云端/接口保存路径") 
    private String cloudpath;
     // 还原来源路径/文件
    @Excel(name = "还原来源路径/文件") 
    private String restoresrc;
     // 还原到的数据库
    @Excel(name = "还原到的数据库") 
    private String restoredb;
     // 备份文件大小(字节)
    @Excel(name = "备份文件大小(字节)") 
    private Long backupsize;
     // 备份文件名
    @Excel(name = "备份文件名") 
    private String backupfile;
     // 状态(0失败/1成功)
    @Excel(name = "状态(0失败/1成功)") 
    private Integer status;
     // 异常信息或补充说明
    @Excel(name = "异常信息或补充说明") 
    private String message;
     // ip地址
    @Excel(name = "ip地址") 
    private String ipaddress;
     // 序号
    @Excel(name = "序号") 
    private Integer rownum;
     // 备注说明
    @Excel(name = "备注说明") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 操作类型(0=备份 1=还原)
    public Integer getOperatetype() {
        return operatetype;
    }
    
    public void setOperatetype(Integer operatetype) {
        this.operatetype = operatetype;
    }
        
   // 对应Uts_BackupConfig.id
    public String getConfigid() {
        return configid;
    }
    
    public void setConfigid(String configid) {
        this.configid = configid;
    }
        
   // 配置名称
    public String getConfigname() {
        return configname;
    }
    
    public void setConfigname(String configname) {
        this.configname = configname;
    }
        
   // 备份/还原开始时间
    public Date getBackuptime() {
        return backuptime;
    }
    
    public void setBackuptime(Date backuptime) {
        this.backuptime = backuptime;
    }
        
   // 完成时间
    public Date getFinishtime() {
        return finishtime;
    }
    
    public void setFinishtime(Date finishtime) {
        this.finishtime = finishtime;
    }
        
   // 耗时(秒)
    public Integer getDuration() {
        return duration;
    }
    
    public void setDuration(Integer duration) {
        this.duration = duration;
    }
        
   // 本地保存路径
    public String getLocalpath() {
        return localpath;
    }
    
    public void setLocalpath(String localpath) {
        this.localpath = localpath;
    }
        
   // 云端/接口保存路径
    public String getCloudpath() {
        return cloudpath;
    }
    
    public void setCloudpath(String cloudpath) {
        this.cloudpath = cloudpath;
    }
        
   // 还原来源路径/文件
    public String getRestoresrc() {
        return restoresrc;
    }
    
    public void setRestoresrc(String restoresrc) {
        this.restoresrc = restoresrc;
    }
        
   // 还原到的数据库
    public String getRestoredb() {
        return restoredb;
    }
    
    public void setRestoredb(String restoredb) {
        this.restoredb = restoredb;
    }
        
   // 备份文件大小(字节)
    public Long getBackupsize() {
        return backupsize;
    }
    
    public void setBackupsize(Long backupsize) {
        this.backupsize = backupsize;
    }
        
   // 备份文件名
    public String getBackupfile() {
        return backupfile;
    }
    
    public void setBackupfile(String backupfile) {
        this.backupfile = backupfile;
    }
        
   // 状态(0失败/1成功)
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
        
   // 异常信息或补充说明
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
        
   // ip地址
    public String getIpaddress() {
        return ipaddress;
    }
    
    public void setIpaddress(String ipaddress) {
        this.ipaddress = ipaddress;
    }
        
   // 序号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注说明
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

