package inks.service.std.utils.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.utils.domain.pojo.UtsBcnPojo;
import inks.service.std.utils.domain.pojo.UtsBcnitemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 单据变更通知单(UtsBcn)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-11 14:35:33
 */
public interface UtsBcnService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsBcnPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsBcnitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsBcnPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsBcnPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsBcnPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param utsBcnPojo 实例对象
     * @return 实例对象
     */
    UtsBcnPojo insert(UtsBcnPojo utsBcnPojo);

    /**
     * 修改数据
     *
     * @param utsBcnpojo 实例对象
     * @return 实例对象
     */
    UtsBcnPojo update(UtsBcnPojo utsBcnpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 审核数据
     *
     * @param utsBcnPojo 实例对象
     * @return 实例对象
     */
     UtsBcnPojo approval(UtsBcnPojo utsBcnPojo);
}
