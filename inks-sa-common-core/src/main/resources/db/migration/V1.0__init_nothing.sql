/*
 Navicat Premium Dump SQL

 Source Server         : ---240MySQL8.0.27
 Source Server Type    : MySQL
 Source Server Version : 80031 (8.0.31)
 Source Host           : **************:53308
 Source Schema         : inksfw

 Target Server Type    : MySQL
 Target Server Version : 80031 (8.0.31)
 File Encoding         : 65001

 Date: 15/07/2025 14:09:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for Sa_Attachment
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Attachment`;
CREATE TABLE `Sa_Attachment`  (
                                  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                  `GenGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通用分组',
                                  `FileOriName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '原文件名',
                                  `BucketName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件桶',
                                  `DirName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '目录',
                                  `FileName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件名',
                                  `FileSize` bigint NOT NULL COMMENT '文件大小',
                                  `ContentType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件格式',
                                  `FileSuffix` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件后缀 扩展名',
                                  `Storage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '存储方式',
                                  `Relateid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联id 单据id',
                                  `FileUrl` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'OSS位置',
                                  `EnabledMark` int NOT NULL COMMENT '有效',
                                  `RowNum` int NOT NULL COMMENT '行号',
                                  `Remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                  `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                  `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                  `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                  `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                  `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                  `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                  `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '功能编码',
                                  `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                  `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                  `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                  `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                  `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                  `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                                  `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                                  `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                                  `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                                  `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                                  `Deptid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '部门ID',
                                  `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                  `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                  `Revision` int NOT NULL COMMENT '乐观锁',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '附件中心' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_Attachment
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_AuthCode
-- ----------------------------
DROP TABLE IF EXISTS `Sa_AuthCode`;
CREATE TABLE `Sa_AuthCode`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `AuthCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '授权Code',
                                `AuthDesc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '授权作用',
                                `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录名',
                                `UserPassword` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '密码',
                                `RowNum` int NOT NULL COMMENT '行号',
                                `EnabledMark` int NOT NULL COMMENT '有效标识',
                                `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户Name',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '授权码' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_AuthCode
-- ----------------------------
INSERT INTO `Sa_AuthCode` VALUES ('1', 'admin22', '', 'admin', '123456', 0, 1, '', '', '', '2024-08-15 10:59:22', '', '', '2024-08-15 10:59:50', '', '', '', '', '', '', '', 1);

-- ----------------------------
-- Table structure for Sa_BillCode
-- ----------------------------
DROP TABLE IF EXISTS `Sa_BillCode`;
CREATE TABLE `Sa_BillCode`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '功能编码',
                                `BillName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单据名称',
                                `Prefix1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀1',
                                `Suffix1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀1',
                                `Prefix2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀2',
                                `Suffix2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀2',
                                `Prefix3` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀3',
                                `Suffix3` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀3',
                                `Prefix4` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀4',
                                `Suffix4` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀4',
                                `Prefix5` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀5',
                                `Suffix5` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀5',
                                `CountType` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '年月日',
                                `Step` int NOT NULL COMMENT '跳步',
                                `CurrentNum` int NOT NULL COMMENT '当前序号',
                                `TableName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据表',
                                `DateColumn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '时间字段',
                                `ColumnName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '列名',
                                `DbFilter` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据过滤',
                                `AllowEdit` int NOT NULL COMMENT '编号允许修改',
                                `AllowDelete` int NOT NULL COMMENT '允许删除复用',
                                `Param1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数1',
                                `Param2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数2',
                                `Param3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数3',
                                `Param4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数4',
                                `Param5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数5',
                                `Remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '单据编码' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_BillCode
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_BillExpression
-- ----------------------------
DROP TABLE IF EXISTS `Sa_BillExpression`;
CREATE TABLE `Sa_BillExpression`  (
                                      `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                      `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '功能编码',
                                      `BillName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单据名称',
                                      `OrgColumns` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '源字段集',
                                      `ExprTemp` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公式模板',
                                      `TgColumn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '目标字段',
                                      `DecNum` int NULL DEFAULT NULL COMMENT '小数位',
                                      `ReturnType` int NULL DEFAULT NULL COMMENT '目标字段类型：0数字,1字符串',
                                      `EnabledMark` int NOT NULL COMMENT '有效性',
                                      `RowNum` int NOT NULL COMMENT '行号',
                                      `Remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                      `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                      `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                      `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                      `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                      `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                      `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                      `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                      `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                      `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                      `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                      `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                      `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                      `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                      `Revision` int NOT NULL COMMENT '乐观锁',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '单据公式' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_BillExpression
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_BillGroup
-- ----------------------------
DROP TABLE IF EXISTS `Sa_BillGroup`;
CREATE TABLE `Sa_BillGroup`  (
                                 `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                 `Parentid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Parentid',
                                 `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '功能编码',
                                 `GroupCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组编码',
                                 `GroupName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组名称',
                                 `EnabledMark` int NOT NULL COMMENT '有效性',
                                 `RowNum` int NOT NULL COMMENT '行号',
                                 `Remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                 `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                 `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                 `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                 `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '通用分组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_BillGroup
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_Company
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Company`;
CREATE TABLE `Sa_Company`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                               `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
                               `EnglishName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '英文名',
                               `CreditCode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '信用代码',
                               `Address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地址',
                               `BankAccount` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '银行账号',
                               `BankOfDeposit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '开户银行',
                               `ContactPerson` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人',
                               `Tel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电话',
                               `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '备注',
                               `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者',
                               `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                               `CreateDate` datetime NULL DEFAULT NULL COMMENT '新建日期',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表',
                               `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                               `ModifyDate` datetime NULL DEFAULT NULL COMMENT '修改日期',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户id',
                               `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                               `Revision` int NOT NULL COMMENT '乐观锁',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '公司信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Company
-- ----------------------------
INSERT INTO `Sa_Company` VALUES ('1', '嘉兴应凯科技有限公司', 'Jiaxing Yingkai Technology Co., Ltd.', '91330421MA2JE8W42K', '浙江嘉善农村商业银行营业部(编码:402335120008)', '201000255323304', '任爱军', '任爱军', '18606858807', '测试发消息', 'admin', ' ', '2024-02-27 10:34:05', '汪洋', '1', '2024-03-23 17:01:43', ' ', ' ', 69);

-- ----------------------------
-- Table structure for Sa_Config
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Config`;
CREATE TABLE `Sa_Config`  (
                              `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                              `Parentid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '父级主键',
                              `CfgName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参数名称',
                              `CfgKey` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块.key',
                              `CfgValue` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'value',
                              `CfgType` int NOT NULL COMMENT '0System/1Module',
                              `CfgLevel` int NOT NULL COMMENT '0平台/1租户/2用户',
                              `CtrlType` int NOT NULL COMMENT '控件类型 0文本1数字2下拉框3开关',
                              `CfgOption` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '可选值',
                              `CfgIcon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标',
                              `AllowUi` int NOT NULL DEFAULT 0 COMMENT '允许前端应用1',
                              `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                              `RowNum` int NOT NULL COMMENT '行号',
                              `EnabledMark` int NOT NULL DEFAULT 1 COMMENT '有效性1',
                              `AllowDelete` int NOT NULL DEFAULT 1 COMMENT '允许删除1',
                              `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '摘要',
                              `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                              `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                              `CreateDate` datetime NOT NULL COMMENT '新建日期',
                              `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                              `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                              `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                              `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                              `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                              `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                              `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                              `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                              `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                              `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                              `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                              `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                              `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                              `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                              `Revision` int NOT NULL COMMENT '乐观锁',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Config
-- ----------------------------
INSERT INTO `Sa_Config` VALUES ('111111', '', '', 'system.oss.type', 'minio', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('111112', '', '', 'system.oss.minio.bucket', 'utils', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('111113', '', '', 'system.oss.minio.accesskey', 'lnkmarkdown', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('111114', '', '', 'system.oss.minio.accesssecret', 'lnkmarkdown', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('111115', '', '', 'system.oss.minio.endpoint', 'http://*************:9080', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('111116', '', '', 'system.oss.minio.urlprefix', 'http://192.168.99.96:8081/File/proxy/{dirname}/{filename}', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('1111192', '', '', 'system.oss.aliyun.bucket', 'inksoms', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('1111193', '', '', 'system.oss.aliyun.accesskey', 'LTAI5tL76QGhNx5eSkzkLnbv', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('1111194', '', '', 'system.oss.aliyun.accesssecret', '******************************', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('1111195', '', '', 'system.oss.aliyun.endpoint', 'http://oss.oms.inksyun.com', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('1111196', '', '', 'system.oss.aliyun.urlprefix', 'http://oss.oms.inksyun.com/', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('1867403878270537728', '', '', 'system.registrkey', 'CnrUbrA3FnybEgoiZJ7CEiY54rOH7wTiLu7zjfr/Cqg+9queb0jOv3KehWD++4cfRrNbjWwMXZGaR5LATSAj9uJbV6gp0Kg578Z7NroN00+8lYLzRP766nLUV8Y6hmd9vLQw6CMxTMbabMulXOrV6RwBijcop2xqk7+wbPn8ZX0RxyNiBohV85k3YXYjlHb9c2PVteYLwuP3RDUc4CERlYdwAHXxbjnscPp//yWXoRG8s3uO3zWuCfykVlxkoksHW7SG9I5cYv8fKfDy/tWROCOAR9DWIIdKnytMWduY1Z7rNwzdVURRWU/iV76ig78bOK+RiIqXr1j4bxIjAh/rYw==', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2025-04-12 15:41:33', '', '', '2025-04-12 15:41:51', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO `Sa_Config` VALUES ('1941030321428402176', '1940993137728065536', '采购单价来源', 'module.buy.pricesource', 'goods25552', 0, 3, 2, '[{\"name\":\"货品\",\"value\":\"goods\"},{\"name\":\"货品最后一次\",\"value\":\"lastgd\"},{\"name\":\"本供应商最后一次\",\"value\":\"lastwg\"}]', '', 1, '', 1, 1, 0, '', 'admin', '1', '2025-07-04 12:38:02', 'admin', '1', '2025-07-04 16:49:45', '', '', '', '', '', '', '', '', '', '', '', 3);

-- ----------------------------
-- Table structure for Sa_Dept
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Dept`;
CREATE TABLE `Sa_Dept`  (
                            `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                            `Parentid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Parentid',
                            `Ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '祖组列表',
                            `DeptCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
                            `DeptName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
                            `EnabledMark` int NOT NULL COMMENT '有效性',
                            `Leader` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '负责人',
                            `Phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电话',
                            `Email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
                            `RowNum` int NOT NULL COMMENT '行号',
                            `Remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                            `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                            `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                            `CreateDate` datetime NOT NULL COMMENT '新建日期',
                            `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                            `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                            `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                            `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                            `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                            `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                            `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                            `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                            `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                            `Revision` int NOT NULL COMMENT '乐观锁',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户组织架构' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_Dept
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_DeptUser
-- ----------------------------
DROP TABLE IF EXISTS `Sa_DeptUser`;
CREATE TABLE `Sa_DeptUser`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `Deptid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '组织id',
                                `DeptCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '组织编码',
                                `DeptName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '组织名称',
                                `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
                                `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录名',
                                `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '姓名',
                                `IsAdmin` int NOT NULL COMMENT '是否主管',
                                `RowNum` int NOT NULL COMMENT '行号',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '组织用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_DeptUser
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_DgFormat
-- ----------------------------
DROP TABLE IF EXISTS `Sa_DgFormat`;
CREATE TABLE `Sa_DgFormat`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                `FormGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '窗体分组',
                                `FormCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '窗体编码',
                                `FormName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '窗体名称',
                                `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                                `EnabledMark` int NOT NULL COMMENT '有效标识',
                                `DefMark` int NULL DEFAULT NULL COMMENT '租户默认',
                                `Summary` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '简述',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '列表格式' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_DgFormat
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_DgFormatItem
-- ----------------------------
DROP TABLE IF EXISTS `Sa_DgFormatItem`;
CREATE TABLE `Sa_DgFormatItem`  (
                                    `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                    `Pid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Pid',
                                    `ItemCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
                                    `ItemName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
                                    `DefWidth` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '默认宽度',
                                    `MinWidth` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '最小宽度',
                                    `DisplayMark` int NOT NULL COMMENT '1为显示',
                                    `Fixed` int NOT NULL COMMENT '1固定0否',
                                    `Sortable` int NOT NULL COMMENT '1可排序',
                                    `OrderField` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '排序表.字段',
                                    `Overflow` int NOT NULL COMMENT '1溢出隐藏',
                                    `Formatter` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '格式化',
                                    `ClassName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义类',
                                    `AlignType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'left/center/right',
                                    `EventName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '事件名称',
                                    `EditMark` int NULL DEFAULT NULL COMMENT '可编辑',
                                    `OperationMark` int NULL DEFAULT NULL COMMENT '可操作',
                                    `RowNum` int NOT NULL COMMENT '行号',
                                    `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                    `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                    `Revision` int NOT NULL COMMENT '乐观锁',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '列表项目' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_DgFormatItem
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_Dict
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Dict`;
CREATE TABLE `Sa_Dict`  (
                            `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                            `DictGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组id',
                            `DictCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典编码',
                            `DictName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典名称',
                            `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '功能编码',
                            `EnabledMark` int NOT NULL COMMENT '有效',
                            `RowNum` int NOT NULL COMMENT '排序码',
                            `Summary` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                            `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                            `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                            `CreateDate` datetime NOT NULL COMMENT '新建日期',
                            `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                            `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                            `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                            `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                            `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                            `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                            `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                            `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                            `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                            `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                            `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                            `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                            `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                            `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                            `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名',
                            `Revision` int NOT NULL COMMENT '乐观锁',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据字典' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Dict
-- ----------------------------
INSERT INTO `Sa_Dict` VALUES ('1743547522263060480', '', 'goods.goodsunit', '', '', 1, 0, '', 'admin', '1', '2024-01-06 16:18:17', 'admin', '1', '2024-01-06 16:18:27', '', '', '', '', '', '', '', '', '', '', '', '', 2);
INSERT INTO `Sa_Dict` VALUES ('1743547584472977408', '', 'goods.surface', '', '', 1, 0, '', 'admin', '1', '2024-01-06 16:18:32', 'admin', '1', '2024-01-06 16:18:32', '', '', '', '', '', '', '', '', '', '', '', '', 1);
INSERT INTO `Sa_Dict` VALUES ('1743547914447261696', '', 'app_wg_customer.groupclass', '', '', 1, 0, '', 'admin', '1', '2024-01-06 16:19:51', 'admin', '1', '2024-01-06 16:19:51', '', '', '', '', '', '', '', '', '', '', '', '', 1);
INSERT INTO `Sa_Dict` VALUES ('1743549269710770176', '', 'app_wg_customer.groupstate', '', '', 1, 0, '', 'admin', '1', '2024-01-06 16:25:14', 'admin', '1', '2024-01-06 16:25:14', '', '', '', '', '', '', '', '', '', '', '', '', 1);
INSERT INTO `Sa_Dict` VALUES ('1743549283627470848', '', 'app_wg_customer.source', '', '', 1, 0, '', 'admin', '1', '2024-01-06 16:25:17', 'admin', '1', '2024-01-06 16:25:17', '', '', '', '', '', '', '', '', '', '', '', '', 1);
INSERT INTO `Sa_Dict` VALUES ('1743549298693410816', '', 'app_wg_customer.grouplabel', '', '', 1, 0, '', 'admin', '1', '2024-01-06 16:25:21', 'admin', '1', '2024-01-06 16:25:21', '', '', '', '', '', '', '', '', '', '', '', '', 1);
INSERT INTO `Sa_Dict` VALUES ('1743549315541929984', '', 'app_wg_customer.paymentmethod', '', '', 1, 0, '', 'admin', '1', '2024-01-06 16:25:25', 'admin', '1', '2024-01-06 16:25:25', '', '', '', '', '', '', '', '', '', '', '', '', 1);
INSERT INTO `Sa_Dict` VALUES ('1743555258166054912', '', 'sale.salesman', '', '', 1, 0, '', 'admin', '1', '2024-01-06 16:49:02', 'admin', '1', '2024-01-06 16:49:02', '', '', '', '', '', '', '', '', '', '', '', '', 1);

-- ----------------------------
-- Table structure for Sa_DictItem
-- ----------------------------
DROP TABLE IF EXISTS `Sa_DictItem`;
CREATE TABLE `Sa_DictItem`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `Pid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '父id',
                                `DictCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典编码',
                                `DictValue` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值',
                                `Essential` int NOT NULL COMMENT '必要',
                                `CssClass` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'css样式',
                                `EnabledMark` int NOT NULL COMMENT '有效',
                                `RowNum` int NOT NULL COMMENT '排序码',
                                `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                                `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                                `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                                `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                                `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '字典子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_DictItem
-- ----------------------------
INSERT INTO `Sa_DictItem` VALUES ('1743547561517551616', '1743547522263060480', 'bu', '部', 0, '', 1, 0, '', '', '', '2024-01-06 16:18:27', '', '', '2024-01-06 16:18:27', '', '', '', '', '', '', '', '', '', '', '', '', 1);

-- ----------------------------
-- Table structure for Sa_DirRule
-- ----------------------------
DROP TABLE IF EXISTS `Sa_DirRule`;
CREATE TABLE `Sa_DirRule`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                               `DirName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '目录名',
                               `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
                               `PublicMark` int NULL DEFAULT NULL COMMENT '是否公共目录标识',
                               `BlackUserids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '黑名单用户ID',
                               `BlackUserNames` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '黑名单用户名',
                               `WhiteUserids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '白名单用户ID',
                               `WhiteUserNames` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '白名单用户名',
                               `RowNum` int NOT NULL COMMENT '行号',
                               `Remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                               `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                               `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '功能编码',
                               `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                               `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                               `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                               `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                               `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                               `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                               `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                               `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                               `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                               `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                               `Deptid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '部门ID',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                               `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                               `Revision` int NOT NULL COMMENT '乐观锁',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '目录规则配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_DirRule
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_FileLog
-- ----------------------------
DROP TABLE IF EXISTS `Sa_FileLog`;
CREATE TABLE `Sa_FileLog`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                               `OpType` tinyint NOT NULL COMMENT '操作类型(0=上传,1=下载)',
                               `UsedMark` tinyint NOT NULL DEFAULT 0 COMMENT '业务使用标识',
                               `FileOriName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '原文件名',
                               `BucketName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件桶',
                               `DirName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '目录',
                               `FileName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '存储文件名',
                               `FileUrl` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'OSS url',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '功能编码',
                               `Module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块',
                               `FileSize` bigint NOT NULL COMMENT '文件大小(Byte)',
                               `ContentType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件格式（MIME）',
                               `FileSuffix` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件后缀',
                               `Storage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '存储方式',
                               `RowNum` int NOT NULL COMMENT '排序码',
                               `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '备注',
                               `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者',
                               `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                               `CreateDate` datetime NULL DEFAULT NULL COMMENT '新建日期',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表',
                               `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                               `ModifyDate` datetime NULL DEFAULT NULL COMMENT '修改日期',
                               `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                               `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                               `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                               `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                               `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                               `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                               `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                               `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                               `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                               `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户ID',
                               `Revision` int NOT NULL COMMENT '乐观锁',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '文件上传/下载日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_FileLog
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_FormCustom
-- ----------------------------
DROP TABLE IF EXISTS `Sa_FormCustom`;
CREATE TABLE `Sa_FormCustom`  (
                                  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                  `GenGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '通用分组',
                                  `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                                  `FrmCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '界面编码',
                                  `FrmName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '界面名称',
                                  `FrmContent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '界面内容',
                                  `RowNum` int NOT NULL COMMENT '序号',
                                  `EnabledMark` int NOT NULL COMMENT '有效标识',
                                  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '摘要',
                                  `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                  `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                  `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                  `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                  `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                  `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                  `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                  `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                  `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                  `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                  `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                  `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                  `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                  `Revision` int NOT NULL COMMENT '乐观锁',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '自定义界面' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_FormCustom
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_IndexImg
-- ----------------------------
DROP TABLE IF EXISTS `Sa_IndexImg`;
CREATE TABLE `Sa_IndexImg`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                                `PictureUrl` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                                `DirName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '目录名',
                                `FileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'minio文件名',
                                `Prodid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联产品id',
                                `Demandid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                                `Seq` int NULL DEFAULT NULL COMMENT '轮播图显示顺序',
                                `Status` int NULL DEFAULT NULL COMMENT '是否展示',
                                `Type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '轮播图类型',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                                `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                                `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                                `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                                `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_IndexImg
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_JustAuth
-- ----------------------------
DROP TABLE IF EXISTS `Sa_JustAuth`;
CREATE TABLE `Sa_JustAuth`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户id',
                                `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录名',
                                `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '姓名',
                                `NickName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '昵称',
                                `AuthType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ding/wxe/openid',
                                `AuthUuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'uuid',
                                `Unionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Unionid',
                                `AuthAvatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'avatar',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '第三方登录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_JustAuth
-- ----------------------------
INSERT INTO `Sa_JustAuth` VALUES ('111', '1', 'admin', ' ', ' ', 'openid', '100', ' ', ' ', ' ', ' ', '2024-09-18 14:32:12', ' ', ' ', '2024-09-18 14:32:20', ' ', '  ', ' ', ' ', ' ', ' ', ' ', 1);

-- ----------------------------
-- Table structure for Sa_Log
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Log`;
CREATE TABLE `Sa_Log`  (
                           `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                           `LogType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志类型',
                           `LogLevel` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '日志级别（INFO/WARNING/ERROR/DEBUG等）',
                           `Message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '日志内容',
                           `RequestUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求的URL地址',
                           `IpAddress` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户端IP地址',
                           `Module` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '所属模块',
                           `OperationType` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作类型（CREATE/UPDATE/DELETE/QUERY）',
                           `CreateDate` datetime NOT NULL COMMENT '创建时间',
                           `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作用户ID',
                           `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作用户名',
                           `UserAgent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '客户端User-Agent',
                           `HttpMethod` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'HTTP请求方法，如GET、POST',
                           `ResourceType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联资源类型',
                           `Resourceid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联资源ID',
                           `Result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作结果（SUCCESS/FAILURE）',
                           `ErrorMessage` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '错误信息（当Result为FAILURE时）',
                           `CustomData` json NULL COMMENT '扩展字段，存储JSON格式的自定义数据',
                           `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户ID',
                           `Revision` int NOT NULL DEFAULT 0 COMMENT '乐观锁',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '通用日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_Log
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_LoginLog
-- ----------------------------
DROP TABLE IF EXISTS `Sa_LoginLog`;
CREATE TABLE `Sa_LoginLog`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
                                `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录号',
                                `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '中文名',
                                `IpAddr` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '主机IP',
                                `LoginLocation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主机地址',
                                `BrowserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '浏览器名称',
                                `HostSystem` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作系统',
                                `Direction` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录/登出',
                                `LoginStatus` int NOT NULL COMMENT '登录状态0成功 1失败',
                                `LoginMsg` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作信息',
                                `LoginTime` datetime NOT NULL COMMENT '访问时间',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户id',
                                `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '登录日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_LoginLog
-- ----------------------------
INSERT INTO `Sa_LoginLog` VALUES ('1926106356130025472', '1', 'admin', 'admin', '************', '内网IP', 'Unknown', 'Unknown', '登录', 0, '用户：admin登录成功,登录时间：2025-05-24 10:41:54,登录系统：Unknown,操作浏览器：Unknown,登录地址：内网IP', '2025-05-24 10:41:54', NULL, NULL);
INSERT INTO `Sa_LoginLog` VALUES ('1926106685110259712', '1', 'admin', 'admin', '************', '内网IP', 'Chrome 13', 'Windows 10', '登录', 0, '用户：admin登录成功,登录时间：2025-05-24 10:43:13,登录系统：Windows 10,操作浏览器：Chrome 13,登录地址：内网IP', '2025-05-24 10:43:13', NULL, NULL);
INSERT INTO `Sa_LoginLog` VALUES ('1926107413497286656', '', 'admin', '', '************', '内网IP', 'Chrome 13', 'Windows 10', '登录', 1, '用户：admin登录失败,登录时间：2025-05-24 10:46:07,原因：用户名或密码错误,登录系统：Windows 10,操作浏览器：Chrome 13,登录地址：内网IP', '2025-05-24 10:46:07', NULL, NULL);

-- ----------------------------
-- Table structure for Sa_MenuApp
-- ----------------------------
DROP TABLE IF EXISTS `Sa_MenuApp`;
CREATE TABLE `Sa_MenuApp`  (
                               `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Navid',
                               `NavPid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '父级id',
                               `NavType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航类型',
                               `NavCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航编码',
                               `NavName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航名称',
                               `NavGroup` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组(备用)',
                               `RowNum` int NOT NULL COMMENT '排列序号',
                               `ImageCss` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Css图标',
                               `IconUrl` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Web图标',
                               `NavigateUrl` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Web位置',
                               `MvcUrl` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'MVC位置',
                               `ModuleType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块类型',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块编码',
                               `RoleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '角色编码',
                               `ImageIndex` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标',
                               `ImageStyle` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标样式',
                               `EnabledMark` int NOT NULL COMMENT '有效标识',
                               `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                               `PermissionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '许可编码',
                               `Functionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务id',
                               `FunctionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务编码',
                               `FunctionName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务名称',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `DeleteMark` int NOT NULL COMMENT '删除标识',
                               `DeleteLister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `DeleteDate` datetime NULL DEFAULT NULL COMMENT '新建日期',
                               PRIMARY KEY (`Navid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_MenuApp
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_MenuWeb
-- ----------------------------
DROP TABLE IF EXISTS `Sa_MenuWeb`;
CREATE TABLE `Sa_MenuWeb`  (
                               `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Navid',
                               `NavPid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '父级id',
                               `NavType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航类型',
                               `NavCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航编码',
                               `NavName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航名称',
                               `NavGroup` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分组(备用)',
                               `RowNum` int NOT NULL COMMENT '排列序号',
                               `ImageCss` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Css图标',
                               `IconUrl` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Web图标',
                               `NavigateUrl` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Web位置',
                               `MvcUrl` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'MVC位置',
                               `ModuleType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块类型',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块编码',
                               `RoleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '角色编码',
                               `ImageIndex` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标',
                               `ImageStyle` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标样式',
                               `EnabledMark` int NOT NULL COMMENT '有效标识',
                               `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                               `PermissionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '许可编码',
                               `Functionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务id',
                               `FunctionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务编码',
                               `FunctionName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务名称',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `DeleteMark` int NOT NULL COMMENT '删除标识',
                               `DeleteLister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `DeleteDate` datetime NULL DEFAULT NULL COMMENT '新建日期',
                               PRIMARY KEY (`Navid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_MenuWeb
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_Notice
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Notice`;
CREATE TABLE `Sa_Notice`  (
                              `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                              `Title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公告标题',
                              `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公告类型',
                              `Content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公告内容',
                              `Status` int NULL DEFAULT NULL COMMENT '公告状态1关闭',
                              `Remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                              `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                              `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                              `CreateDate` datetime NOT NULL COMMENT '新建日期',
                              `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                              `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                              `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                              `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                              `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                              `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                              `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                              `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                              `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                              `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                              `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                              `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                              `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                              `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                              `Revision` int NOT NULL COMMENT '乐观锁',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Notice
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_OperLog
-- ----------------------------
DROP TABLE IF EXISTS `Sa_OperLog`;
CREATE TABLE `Sa_OperLog`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                               `OperTitle` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块标题',
                               `BusinessType` int NULL DEFAULT NULL COMMENT '业务类型（0其它 1新增 2修改 3删除）',
                               `Method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '方法名称',
                               `RequestMethod` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求方式',
                               `OperatorType` int NULL DEFAULT NULL COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
                               `OperUserid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人员id',
                               `OperName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人员',
                               `DeptName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门名称',
                               `OperUrl` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求URL',
                               `OperIp` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主机地址',
                               `OperLocation` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作地点',
                               `OperParam` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求参数',
                               `JsonResult` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '返回参数',
                               `Status` int NULL DEFAULT NULL COMMENT '操作状态（0正常 1异常）',
                               `ErrorMsg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误消息',
                               `OperTime` datetime NULL DEFAULT NULL COMMENT '操作时间',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_OperLog
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_PermCode
-- ----------------------------
DROP TABLE IF EXISTS `Sa_PermCode`;
CREATE TABLE `Sa_PermCode`  (
                                `Permid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Id',
                                `Parentid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '父级主键',
                                `PermType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限类型',
                                `PermCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限编码',
                                `PermName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限名称',
                                `RowNum` int NOT NULL COMMENT '排列序号',
                                `IsPublic` int NOT NULL COMMENT '是否公开',
                                `EnabledMark` int NOT NULL COMMENT '是否有效',
                                `AllowDelete` int NOT NULL COMMENT '允许删除',
                                `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限编码' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_PermCode
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_Permission
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Permission`;
CREATE TABLE `Sa_Permission`  (
                                  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                  `ResourceType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '对象类别',
                                  `Resourceid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '对象id',
                                  `Permid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限id',
                                  `PermCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限编码',
                                  `PermName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限名称',
                                  `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                  `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                  `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                  `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                  `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                  `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                  `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                  `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                  `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Permission
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_Redis
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Redis`;
CREATE TABLE `Sa_Redis`  (
                             `RedisKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'key是MySQL关键字',
                             `RedisValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'value',
                             `ExpireTime` bigint NOT NULL COMMENT '过期时间戳(-1永不过期),每天23点执行SQL清除过期key:DELETE FROM Sa_Redis WHERE UNIX_TIMESTAMP(NOW()) * 1000 > expiretime AND expiretime != -1',
                             `CreateTime` bigint NULL DEFAULT NULL COMMENT '创建时间戳',
                             `Hkey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '哈希内部的键',
                             PRIMARY KEY (`RedisKey`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MySQL暂替Redis' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Redis
-- ----------------------------
INSERT INTO `Sa_Redis` VALUES ('login_tokens:admin', '{\"avatar\":\"picture/20230225/d3a0a08f2ea447ba9f1a.jpg\",\"expiretime\":1722435518486,\"ipaddr\":\"127.0.0.1\",\"isadmin\":2,\"isregister\":1,\"logintime\":1722392318486,\"realName\":\"admin\",\"realname\":\"admin\",\"tenantid\":\"inks-tid-repair\",\"tenantinfo\":{\"company\":\"嘉兴应凯科技有限公司\",\"companyadd\":\"浙江嘉善农村商业银行营业部(编码:402335120008)\",\"companytel\":\"18606858807\",\"contactor\":\"任爱军\",\"previouvisit\":1722392318483,\"tenantcode\":\"tid-code\",\"tenantid\":\"inks-tid-repair\",\"tenantname\":\"tid-name\"},\"token\":\"admin\",\"userid\":\"1\",\"username\":\"admin\"}', -1, 1722392319294, NULL);
INSERT INTO `Sa_Redis` VALUES ('tenant_config:', '{\"system.oss.aliyun.accesskey\":\"LTAI5tL76QGhNx5eSkzkLnbv\",\"system.oss.aliyun.bucket\":\"inksoms\",\"system.oss.minio.endpoint\":\"http://*************:9080\",\"system.oss.minio.accesssecret\":\"lnkmarkdown\",\"system.oss.minio.bucket\":\"utils\",\"system.oss.aliyun.endpoint\":\"http://oss.oms.inksyun.com\",\"system.oss.aliyun.urlprefix\":\"http://oss.oms.inksyun.com/\",\"system.oss.minio.accesskey\":\"lnkmarkdown\",\"module.buy.pricesource\":\"goods25552\",\"system.oss.aliyun.accesssecret\":\"******************************\",\"system.registrkey\":\"CnrUbrA3FnybEgoiZJ7CEiY54rOH7wTiLu7zjfr/Cqg+9queb0jOv3KehWD++4cfRrNbjWwMXZGaR5LATSAj9uJbV6gp0Kg578Z7NroN00+8lYLzRP766nLUV8Y6hmd9vLQw6CMxTMbabMulXOrV6RwBijcop2xqk7+wbPn8ZX0RxyNiBohV85k3YXYjlHb9c2PVteYLwuP3RDUc4CERlYdwAHXxbjnscPp//yWXoRG8s3uO3zWuCfykVlxkoksHW7SG9I5cYv8fKfDy/tWROCOAR9DWIIdKnytMWduY1Z7rNwzdVURRWU/iV76ig78bOK+RiIqXr1j4bxIjAh/rYw==\",\"system.oss.type\":\"minio\",\"system.oss.minio.urlprefix\":\"http://192.168.99.96:8081/File/proxy/{dirname}/{filename}\"}', 1754210999276, 1751618999286, NULL);

-- ----------------------------
-- Table structure for Sa_Reports
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Reports`;
CREATE TABLE `Sa_Reports`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                               `GenGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通用分组',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                               `RptType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '报表类型',
                               `RptName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '报表名称',
                               `RptData` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '报表数据',
                               `PageRow` int NOT NULL DEFAULT 0 COMMENT '单页行数',
                               `TempUrl` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '远程打印模版Url',
                               `FileName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模版文件名',
                               `PrinterSn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '远程打印机SN',
                               `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                               `EnabledMark` int NOT NULL COMMENT '有效标识',
                               `GrfData` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'grf文本备用',
                               `PaperLength` decimal(18, 2) NULL DEFAULT NULL COMMENT '长',
                               `PaperWidth` decimal(18, 2) NULL DEFAULT NULL COMMENT '宽',
                               `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                               `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                               `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                               `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                               `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                               `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                               `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                               `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                               `Revision` int NOT NULL DEFAULT 0 COMMENT '乐观锁',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '报表中心' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Reports
-- ----------------------------
INSERT INTO `Sa_Reports` VALUES ('1904089032225230848', '', '', '', '', '', 0, '', '', '', 0, 0, '', 0.00, 0.00, '', '张三', 'fc2a723f-fc73-48a8-98ae-8d4150a5da4f', '2025-03-24 16:32:55', '张三', 'fc2a723f-fc73-48a8-98ae-8d4150a5da4f', '2025-03-24 16:32:55', '', '', '', '', '', '', '', 1);

-- ----------------------------
-- Table structure for Sa_Role
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Role`;
CREATE TABLE `Sa_Role`  (
                            `Roleid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
                            `RoleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
                            `RoleName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
                            `Functionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务id',
                            `FunctionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务编码',
                            `FunctionName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务名称',
                            `EnabledMark` int NOT NULL COMMENT '是否有效',
                            `RowNum` int NOT NULL COMMENT '排列序号',
                            `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                            `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                            `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                            `CreateDate` datetime NOT NULL COMMENT '新建日期',
                            `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                            `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                            `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                            `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                            `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                            `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Role
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_RoleMenuApp
-- ----------------------------
DROP TABLE IF EXISTS `Sa_RoleMenuApp`;
CREATE TABLE `Sa_RoleMenuApp`  (
                                   `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                   `Roleid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
                                   `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '菜单ID',
                                   `RowNum` int NOT NULL COMMENT '排列序号',
                                   `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                   `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                   `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                   `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                   `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                   `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                   `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                   `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                   `Revision` int NOT NULL COMMENT '乐观锁',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_RoleMenuApp
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_RoleMenuWeb
-- ----------------------------
DROP TABLE IF EXISTS `Sa_RoleMenuWeb`;
CREATE TABLE `Sa_RoleMenuWeb`  (
                                   `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                   `Roleid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
                                   `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '菜单ID',
                                   `RowNum` int NOT NULL COMMENT '排列序号',
                                   `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                   `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                   `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                   `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                   `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                   `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                   `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                   `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                   `Revision` int NOT NULL COMMENT '乐观锁',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_RoleMenuWeb
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_Scene
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Scene`;
CREATE TABLE `Sa_Scene`  (
                             `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                             `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                             `SceneName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '场景名称',
                             `SceneData` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '场景数据',
                             `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                             `EnabledMark` int NOT NULL COMMENT '有效标识',
                             `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                             `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者',
                             `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                             `CreateDate` datetime NOT NULL COMMENT '新建日期',
                             `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                             `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                             `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                             `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                             `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                             `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                             `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                             `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                             `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                             `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                             `Revision` int NOT NULL COMMENT '乐观锁',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '场景管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_Scene
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_SceneField
-- ----------------------------
DROP TABLE IF EXISTS `Sa_SceneField`;
CREATE TABLE `Sa_SceneField`  (
                                  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                  `GenGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通用分组',
                                  `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                                  `FieldCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
                                  `FieldName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
                                  `FieldType` int NOT NULL COMMENT '0文本1为数字',
                                  `SearchMark` int NULL DEFAULT NULL COMMENT '搜索中应用',
                                  `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                                  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                  `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者',
                                  `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                                  `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                  `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                  `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                                  `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                  `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                  `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                  `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                  `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                  `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                  `Revision` int NOT NULL COMMENT '乐观锁',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '场景字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_SceneField
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_User
-- ----------------------------
DROP TABLE IF EXISTS `Sa_User`;
CREATE TABLE `Sa_User`  (
                            `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                            `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户名',
                            `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '真实姓名',
                            `Password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '密码',
                            `Phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
                            `Email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电子邮箱',
                            `EmailAuthCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '阿里邮箱授权码',
                            `Sex` int NULL DEFAULT NULL COMMENT '性别',
                            `Avatar` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
                            `DirName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '目录名',
                            `FileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'minio文件名',
                            `RoleType` int NOT NULL COMMENT '0新注册1技术员',
                            `AdminMark` int NOT NULL COMMENT '是否管理员',
                            `UserState` int NOT NULL COMMENT '用户状态',
                            `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                            `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                            `CreateDate` datetime NOT NULL COMMENT '创建时间',
                            `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表人',
                            `ModifyDate` datetime NOT NULL COMMENT '修改时间',
                            `Revision` int NOT NULL COMMENT '锁',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_User
-- ----------------------------
INSERT INTO `Sa_User` VALUES ('1', 'admin', 'admin', '14bEvA7CeyIoopYkYqtNWw==', '', '', '', 0, 'picture/20230225/d3a0a08f2ea447ba9f1a.jpg', 'picture/20230130', '874127ff1044cd5bad3a.jpg', 1, 2, 0, '默认超级管理员', '', '2022-12-05 17:12:29', 'admin', '2024-02-20 10:48:43', 9);

-- ----------------------------
-- Table structure for Sa_UserRole
-- ----------------------------
DROP TABLE IF EXISTS `Sa_UserRole`;
CREATE TABLE `Sa_UserRole`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `Roleid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
                                `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
                                `RowNum` int NOT NULL COMMENT '排列序号',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户角色关联' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_UserRole
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_Validator
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Validator`;
CREATE TABLE `Sa_Validator`  (
                                 `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                 `ValiCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '验证编码',
                                 `ValiTitle` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
                                 `SqlMark` int NOT NULL COMMENT '是否SQL',
                                 `SqlStr` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'SQL语句',
                                 `Expression` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '表达式',
                                 `TipMsg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '提示语',
                                 `TipMsgEn` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '提示语(英文)',
                                 `RequiredMark` int NOT NULL COMMENT '是否必要',
                                 `ItemLoopMark` int NOT NULL COMMENT '是否item循环',
                                 `EnabledMark` int NULL DEFAULT NULL COMMENT '有效标识',
                                 `RowNum` int NOT NULL COMMENT '行号',
                                 `Remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                 `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                 `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                 `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                 `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                 `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                 `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                 `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                 `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                 `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                 `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                 `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                 `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                 `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                 `Revision` int NOT NULL COMMENT '乐观锁',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据验证' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_Validator
-- ----------------------------
INSERT INTO `Sa_Validator` VALUES ('1859778822304468992', 'D01M06B1', '订单发货必须销售订单生产入库', 1, 'select ROUND(inquantity,2) AS sqlinquantity, ROUND(finishqty,2) AS sqlfinishqty\nFROM Bus_MachiningItem where id=${machitemid}', '#if($id == \"\")\n    #set($totalQuantity = $quantity )\n#else\n    #set($totalQuantity = $quantity + $sqlobj.sqlfinishqty)\n#end\n\n${totalQuantity} >${sqlobj.sqlinquantity}', '#if($id == \"\")\n    #set($totalQuantity = $quantity)\n#else\n    #set($totalQuantity = $quantity + $sqlobj.sqlfinishqty)\n#end\n发货数：${totalQuantity}大于生产入库数：${sqlobj.sqlinquantity}', '', 1, 0, 1, 1, '', '箱包', '1734795356299853824', '2024-11-22 09:59:59', '箱包', '1734795356299853824', '2024-11-22 11:09:35', '', '', '', '', '', '1734795232534331392', '', 24);

-- ----------------------------
-- Table structure for Sa_Warning
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Warning`;
CREATE TABLE `Sa_Warning`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                               `GenGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通用分组',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                               `WarnCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预警编码',
                               `WarnName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预警名称',
                               `WarnField` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预警字段',
                               `SvcCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务编码',
                               `WarnApi` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预警接口',
                               `WebPath` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'web文件',
                               `ImageCss` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Css图标',
                               `TagTitle` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '标签文本',
                               `PermCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '许可编码',
                               `RowNum` int NOT NULL COMMENT '行号',
                               `EnabledMark` int NOT NULL DEFAULT 1 COMMENT '有效性1',
                               `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '摘要',
                               `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                               `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `Revision` int NOT NULL COMMENT '乐观锁',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '预警' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_Warning
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_WarningUser
-- ----------------------------
DROP TABLE IF EXISTS `Sa_WarningUser`;
CREATE TABLE `Sa_WarningUser`  (
                                   `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                   `Warnid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '预警id',
                                   `DiffNum` int NOT NULL COMMENT '时间差',
                                   `RowNum` int NOT NULL COMMENT '行号',
                                   `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '摘要',
                                   `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                   `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                   `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                   `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                   `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                   `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                   `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户id',
                                   `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '姓名',
                                   `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                   `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                   `Revision` int NOT NULL COMMENT '乐观锁',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '预警用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Sa_WarningUser
-- ----------------------------

-- ----------------------------
-- Table structure for Sa_WebNav
-- ----------------------------
DROP TABLE IF EXISTS `Sa_WebNav`;
CREATE TABLE `Sa_WebNav`  (
                              `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Navid',
                              `NavCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航编码',
                              `NavName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航名称',
                              `NavContent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航内容',
                              `RowNum` int NOT NULL COMMENT '排列序号',
                              `EnabledMark` int NOT NULL COMMENT '有效标识',
                              `PermissionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '许可编码',
                              `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                              `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                              `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                              `CreateDate` datetime NOT NULL COMMENT '新建日期',
                              `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                              `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                              `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                              `Revision` int NOT NULL COMMENT '乐观锁',
                              PRIMARY KEY (`Navid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'Pc导航' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of Sa_WebNav
-- ----------------------------

-- ----------------------------
-- Table structure for flyway_schema_history
-- ----------------------------
DROP TABLE IF EXISTS `flyway_schema_history`;
CREATE TABLE `flyway_schema_history`  (
                                          `installed_rank` int NOT NULL,
                                          `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                                          `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                          `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                          `script` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                          `checksum` int NULL DEFAULT NULL,
                                          `installed_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                                          `installed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                          `execution_time` int NOT NULL,
                                          `success` tinyint(1) NOT NULL,
                                          PRIMARY KEY (`installed_rank`) USING BTREE,
                                          INDEX `flyway_schema_history_s_idx`(`success` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of flyway_schema_history
-- ----------------------------
INSERT INTO `flyway_schema_history` VALUES (1, '1', '<< Flyway Baseline >>', 'BASELINE', '<< Flyway Baseline >>', NULL, 'root', '2024-07-19 01:29:58', 0, 1);
INSERT INTO `flyway_schema_history` VALUES (2, '1.1', 'init inksfw', 'SQL', 'V1.1__init_inksfw.sql', -780638369, 'root', '2024-07-19 01:30:38', 38092, 1);

SET FOREIGN_KEY_CHECKS = 1;
