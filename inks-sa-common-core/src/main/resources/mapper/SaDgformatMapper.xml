<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SadgformatMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SadgformatPojo">
        select id,
        FormGroupid,
        FormCode,
        FormName,
        RowNum,
        EnabledMark,
        DefMark,
        Summary,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        TenantName,
        Revision
        from Sa_DgFormat
        where Sa_DgFormat.id = #{key}
        and Sa_DgFormat.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               FormGroupid,
               FormCode,
               FormName,
               RowNum,
               EnabledMark,
               DefMark,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from Sa_DgFormat
    </sql>
    <sql id="selectdetailVo">
        select id,
               FormGroupid,
               FormCode,
               FormName,
               RowNum,
               EnabledMark,
               DefMark,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from Sa_DgFormat
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SadgformatitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Sa_DgFormat.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_DgFormat.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.formgroupid != null">
            and Sa_DgFormat.formgroupid like concat('%', #{SearchPojo.formgroupid}, '%')
        </if>
        <if test="SearchPojo.formcode != null">
            and Sa_DgFormat.formcode like concat('%', #{SearchPojo.formcode}, '%')
        </if>
        <if test="SearchPojo.formname != null">
            and Sa_DgFormat.formname like concat('%', #{SearchPojo.formname}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Sa_DgFormat.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_DgFormat.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_DgFormat.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_DgFormat.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_DgFormat.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_DgFormat.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_DgFormat.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_DgFormat.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_DgFormat.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_DgFormat.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_DgFormat.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.formgroupid != null">
                or Sa_DgFormat.FormGroupid like concat('%', #{SearchPojo.formgroupid}, '%')
            </if>
            <if test="SearchPojo.formcode != null">
                or Sa_DgFormat.FormCode like concat('%', #{SearchPojo.formcode}, '%')
            </if>
            <if test="SearchPojo.formname != null">
                or Sa_DgFormat.FormName like concat('%', #{SearchPojo.formname}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Sa_DgFormat.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_DgFormat.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_DgFormat.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_DgFormat.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_DgFormat.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_DgFormat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_DgFormat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_DgFormat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_DgFormat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_DgFormat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_DgFormat.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SadgformatPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Sa_DgFormat.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_DgFormat.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.formgroupid != null">
            and Sa_DgFormat.FormGroupid like concat('%', #{SearchPojo.formgroupid}, '%')
        </if>
        <if test="SearchPojo.formcode != null">
            and Sa_DgFormat.FormCode like concat('%', #{SearchPojo.formcode}, '%')
        </if>
        <if test="SearchPojo.formname != null">
            and Sa_DgFormat.FormName like concat('%', #{SearchPojo.formname}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Sa_DgFormat.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_DgFormat.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_DgFormat.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_DgFormat.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_DgFormat.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_DgFormat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_DgFormat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_DgFormat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_DgFormat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_DgFormat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_DgFormat.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.formgroupid != null">
                or Sa_DgFormat.FormGroupid like concat('%', #{SearchPojo.formgroupid}, '%')
            </if>
            <if test="SearchPojo.formcode != null">
                or Sa_DgFormat.FormCode like concat('%', #{SearchPojo.formcode}, '%')
            </if>
            <if test="SearchPojo.formname != null">
                or Sa_DgFormat.FormName like concat('%', #{SearchPojo.formname}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Sa_DgFormat.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_DgFormat.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_DgFormat.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_DgFormat.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_DgFormat.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_DgFormat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_DgFormat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_DgFormat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_DgFormat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_DgFormat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_DgFormat.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_DgFormat(id, FormGroupid, FormCode, FormName, RowNum, EnabledMark, DefMark, Summary, CreateBy,
        CreateByid,
        CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
        Tenantid, TenantName, Revision)
        values (#{id}, #{formgroupid}, #{formcode}, #{formname}, #{rownum}, #{enabledmark}, #{defmark}, #{summary},
        #{createby},
        #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3},
        #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DgFormat
        <set>
            <if test="formgroupid != null">
                FormGroupid =#{formgroupid},
            </if>
            <if test="formcode != null">
                FormCode =#{formcode},
            </if>
            <if test="formname != null">
                FormName =#{formname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="defmark != null">
                DefMark =#{defmark},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_DgFormat
        where id = #{key}
        and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.sa.common.core.domain.pojo.SadgformatPojo">
        select
        id
        from Sa_DgFormatItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>


    <!--查询单个-->
    <select id="getEntityByCode" resultType="inks.sa.common.core.domain.pojo.SadgformatPojo">
        select id,
        FormCode,
        FormName,
        RowNum,
        EnabledMark,
        DefMark,
        Summary,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Tenantid,
        Revision
        from Sa_DgFormat
        where Sa_DgFormat.FormCode = #{code}
        and Sa_DgFormat.Tenantid = #{tid}
        ORDER BY CreateDate DESC
        LIMIT 1
    </select>

    <!--查询单个-->
    <select id="getEntityByCodeUser" resultType="inks.sa.common.core.domain.pojo.SadgformatPojo">
        select id,
        FormCode,
        FormName,
        RowNum,
        EnabledMark,
        DefMark,
        Summary,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Tenantid,
        Revision
        from Sa_DgFormat
        where Sa_DgFormat.FormCode = #{code}
        and Sa_DgFormat.Tenantid = #{tid}
        and Sa_DgFormat.Listerid = #{userid}
        ORDER BY CreateDate DESC
        LIMIT 1
    </select>
    <select id="getTenBillEntityByCode" resultType="inks.sa.common.core.domain.pojo.SadgformatPojo">
        select * from Sa_DgFormat
        where Sa_DgFormat.Tenantid = #{tid}
        and Sa_DgFormat.FormCode = #{code}
        and Sa_DgFormat.DefMark = 1
        LIMIT 1
    </select>
</mapper>

