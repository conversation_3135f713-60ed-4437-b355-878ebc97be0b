<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaRedisMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaRedisPojo">
        select RedisKey,
        RedisValue,
        ExpireTime,
        CreateTime,
        Hkey
        from Sa_Redis
        where Sa_Redis.RedisKey = #{key}
    </select>

    <sql id="selectSaRedisVo">
        select RedisKey,
               RedisValue,
               ExpireTime,
               CreateTime,
               Hkey
        from Sa_Redis
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaRedisPojo">
        <include refid="selectSaRedisVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Redis.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.redisvalue != null">
            and Sa_Redis.RedisValue like concat('%', #{SearchPojo.redisvalue}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.redisvalue != null">
                or Sa_Redis.RedisValue like concat('%', #{SearchPojo.redisvalue}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Redis(RedisKey, RedisValue, ExpireTime ,CreateTime,Hkey)
        values (#{rediskey}, #{redisvalue}, #{expiretime}, #{createtime},#{hkey})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Redis
        <set>
            <if test="redisvalue != null">
                RedisValue =#{redisvalue},
            </if>
            <if test="expiretime != null">
                Expiretime =#{expiretime},
            </if>
            <if test="createtime != null">
                CreateTime =#{createtime},
            </if>
            <if test="hkey != null">
                Hkey =#{hkey},
            </if>
        </set>
        where RedisKey = #{rediskey}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Redis
        where RedisKey = #{key}
    </delete>

    <select id="getValue" resultType="java.lang.String">
        select RedisValue
        from Sa_Redis
        where Sa_Redis.RedisKey = #{key}
    </select>

    <delete id="cleanSa_Redis">
        DELETE
        FROM Sa_Redis
        WHERE UNIX_TIMESTAMP(NOW()) * 1000 > ExpireTime
          AND ExpireTime != -1
    </delete>

    <select id="getEntityByRedisKeyAndHKey" resultType="inks.sa.common.core.domain.pojo.SaRedisPojo">
        select RedisKey,
        RedisValue,
        ExpireTime,
        CreateTime,
        Hkey
        from Sa_Redis
        where Sa_Redis.RedisKey = #{redisKey} and Sa_Redis.Hkey = #{hKey}
    </select>
</mapper>

