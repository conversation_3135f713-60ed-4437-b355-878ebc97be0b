server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  datasource:
    #MYsql连接字符串
    url: *************************************************************************************************************************************************
    username: root
    password: asd@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
  # Flyway配置已移至DataSourceHelper代码中实现
  flyway:
    enabled: false   # 禁用Spring Boot自动Flyway配置，使用自定义DataSourceHelper
    initsql: http://dev.inksyun.com:31080/utils/File/proxy/appsetup/inksedb_data.sql
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）\
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
  web: #配置静态资源访问路径
    resources:
      static-locations: classpath:/static/, classpath:/h5/
  mvc:
    view:
      suffix: .html

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.lbl.**.domain
  #配置打印SQL语句到控制台


#oss:
#  type: minio
#  minio:
#    bucket: sa-common
#    access-key: lnkmarkdown
#    secret-key: lnkmarkdown
#    endpoint: http://dev.inksyun.com:9080
#    urlprefix: http://dev.inksyun.com:9080/
#  aliyun:
#    bucket: inkstable
#    access-key-id: LTAI5t7gvbML44MA1pxPbr21
#    access-key-secret: ******************************
#    endpoint: https://oss-cn-qingdao.aliyuncs.com
#    urlprefix: https://inkstable.oss-cn-qingdao.aliyuncs.com/
#backup:
#  sqlserver:
#    dbname: inkscmr
#    scheduling:
#      cron: 0 0 1 * * *   # 每天凌晨1点执行备份
#    password: 123456      # 下载后的备份文件解压密码
#    directory: D:\backup\sqlserver  # 临时备份文件存放目录()
##      cron: 0 1 * * * *  # 在线Cron表达式生成器:https://cron.qqe2.com/

#oss:
#  bucket: inksfw
#  minio:
#    access-key: minioadmin
#    secret-key: minioadmin
#    endpoint: http://inks.tpddns.net:9080

#雪花算法:  数据中心id,工作机器id
snowflake:
  dataCenterId: 1
  workerId: 1

inks:
#  解密sn: init222
  license: Pwx4L16D1TNkS/PowyEJIyigGR6iniaSLOUvjOAwAG+wK/11mDdupDQMjSwypdWp7HCtewGkPvpTuT1vPknc3msYxr4c/gRQHTbxxZMzjXr3OCOu++vHBawihScogF5ODfrtsKEXpL0gLDiRNjjoeF3LYcVzHP5+eUG38LfVBxypC+sJXIrLMiX/xWiB9rHAMEZah/Rf3H1mnZREqBDUvKPmcsHYlDucmnphgsoE+Ay4kkLN6f9IM5EMmDEpXRLZ0kYn71OBxvpIB3DINNX458sYw3UehMV+fJIhac5lkQXQ5lNQvuQokE1C+wzi+X3axhiDBwFmB34OsqZc/5L00Q==
#  redisType: mysql
  redisType: mysql
  user:
    wxscan-create: true    #微信公众号扫码可直接创建admin权限用户，默认false
  feign:
    GrfUrl: dev.inksyun.com:18801 # 公网:http://vip.inkstech.com:18801
    UtsUrl: http://*************:10684
  # 调用oam公众号接口获取openid #内网测试号:http://*************:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce
  tid: tid-inks-fw
  svcFeign: http://*************:10684