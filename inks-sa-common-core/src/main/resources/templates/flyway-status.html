<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库状态 - Flyway</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-success {
            border-left: 5px solid #28a745;
            background: #d4edda;
        }
        .status-error {
            border-left: 5px solid #dc3545;
            background: #f8d7da;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-item {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .info-item h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 1.1em;
        }
        .info-item .value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
        .loading {
            text-align: center;
            color: #666;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 数据库状态监控</h1>
            <p>Spring Boot 应用数据库信息</p>
        </div>

        <div id="status-content" class="loading">
            <p>正在加载数据库状态信息...</p>
        </div>

        <div class="footer">
            <button class="refresh-btn" onclick="loadStatus()">刷新状态</button>
            <p>页面每30秒自动刷新 | 最后更新: <span id="last-update">--</span></p>
        </div>
    </div>

    <script>
        let autoRefreshInterval;

        function loadStatus() {
            fetch('/flyway/api/status')
                .then(response => response.json())
                .then(data => {
                    updateStatusDisplay(data);
                    document.getElementById('last-update').textContent = new Date().toLocaleString();
                })
                .catch(error => {
                    console.error('Error loading status:', error);
                    document.getElementById('status-content').innerHTML = `
                        <div class="status-card status-error">
                            <h3>❌ 加载失败</h3>
                            <p>无法获取数据库状态信息: ${error.message}</p>
                        </div>
                    `;
                });
        }

        function updateStatusDisplay(data) {
            // 根据初始化状态选择不同的图标和样式
            let initStatusIcon = '📊';
            let initStatusClass = 'status-success';
            
            if (data.initializationStatus === '首次建库') {
                initStatusIcon = '🎉';
                initStatusClass = 'status-success';
            } else if (data.initializationStatus === '已有数据库') {
                initStatusIcon = '📂';
                initStatusClass = 'status-success';
            }
            
            // 格式化运行时间
            function formatUptime(seconds) {
                if (!seconds) return 'N/A';
                const days = Math.floor(seconds / 86400);
                const hours = Math.floor((seconds % 86400) / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return `${days}天 ${hours}小时 ${minutes}分钟`;
            }
            
            // 格式化数字
            function formatNumber(num) {
                if (!num && num !== 0) return 'N/A';
                return num.toLocaleString();
            }
            
            document.getElementById('status-content').innerHTML = `
                <div class="status-card ${initStatusClass}">
                    <h3>${initStatusIcon} 初始化状态: ${data.initializationStatus || '未知'}</h3>
                    <p>${data.initializationStatus === '首次建库' ? 
                        `首次创建数据库，初始化耗时: ${(data.initializationTime / 1000).toFixed(2)} 秒` : 
                        data.initializationStatus === '已有数据库' ? 
                        '使用已存在的数据库，跳过了初始化过程' : 
                        '数据库状态信息'}</p>
                </div>

                <div class="info-grid">
                    <div class="info-item">
                        <h3>📊 数据库名称</h3>
                        <div class="value">${data.databaseName || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <h3>🗃️ 数据表数量</h3>
                        <div class="value">${formatNumber(data.tableCount)}</div>
                    </div>
                    <div class="info-item">
                        <h3>🏷️ 初始化类型</h3>
                        <div class="value" style="font-size: 1.4em;">${data.initializationStatus || '未知'}</div>
                    </div>
                    <div class="info-item">
                        <h3>💾 数据库大小</h3>
                        <div class="value" style="font-size: 1.4em;">${data.databaseSize ? data.databaseSize.toFixed(2) + ' MB' : 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <h3>🔗 活跃连接</h3>
                        <div class="value" style="color: ${data.activeConnections > (data.maxConnections * 0.8) ? '#dc3545' : '#28a745'};">
                            ${data.activeConnections || 'N/A'}${data.maxConnections ? '/' + data.maxConnections : ''}
                        </div>
                    </div>
                    <div class="info-item">
                        <h3>⏱️ 运行时间</h3>
                        <div class="value" style="font-size: 1.2em;">${formatUptime(data.uptime)}</div>
                    </div>
                    <div class="info-item">
                        <h3>📈 总查询数</h3>
                        <div class="value">${formatNumber(data.totalQueries)}</div>
                    </div>
                    <div class="info-item">
                        <h3>🐌 慢查询数</h3>
                        <div class="value" style="color: ${data.slowQueries > 0 ? '#ffc107' : '#28a745'};">
                            ${formatNumber(data.slowQueries)}
                        </div>
                    </div>
                    <div class="info-item">
                        <h3>👁️ 视图数量</h3>
                        <div class="value">${formatNumber(data.viewCount)}</div>
                    </div>
                    <div class="info-item">
                        <h3>🗄️ 数据库版本</h3>
                        <div class="value" style="font-size: 1.2em;">${data.databaseProduct || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <h3>🔌 驱动版本</h3>
                        <div class="value" style="font-size: 1.1em;">${data.driverName || 'N/A'}</div>
                    </div>
                    <div class="info-item">
                        <h3>🌐 连接地址</h3>
                        <div class="value" style="font-size: 0.9em; word-break: break-all;">${data.databaseUrl || 'N/A'}</div>
                    </div>
                </div>
            `;
        }

        // 页面加载时获取状态
        document.addEventListener('DOMContentLoaded', function() {
            loadStatus();
            
            // 设置自动刷新（每30秒）
            autoRefreshInterval = setInterval(loadStatus, 30000);
        });

        // 页面失去焦点时停止自动刷新，获得焦点时恢复
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                clearInterval(autoRefreshInterval);
            } else {
                loadStatus();
                autoRefreshInterval = setInterval(loadStatus, 30000);
            }
        });
    </script>
</body>
</html>
