package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaDictPojo;
import inks.sa.common.core.domain.pojo.SaDictitemPojo;
import inks.sa.common.core.domain.pojo.SaDictitemdetailPojo;
import inks.sa.common.core.service.SaDictService;
import inks.sa.common.core.service.SaDictitemService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 数据字典(SaDict)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-03 16:10:09
 */


@RestController
@RequestMapping("SaDict")
@Api(tags = "通用:数据字典")
public class A_SaDictController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaDictController.class);

    @Resource
    private SaDictService saDictService;

    /**
     * 服务对象Item
     */
    @Resource
    private SaDictitemService saDictitemService;


    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取数据字典详细信息", notes = "获取数据字典详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByDictCode", method = RequestMethod.GET)
    public R<SaDictPojo> getBillEntityByDictCode(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            SaDictPojo cidictPojo = this.saDictService.getBillEntityByDictCode(key);
            if (cidictPojo == null) {
                cidictPojo = new SaDictPojo();
                cidictPojo.setDictcode(key);
                cidictPojo.setEnabledmark(1);
                cidictPojo.setCreateby(loginUser.getRealName());   // 创建者
                cidictPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                cidictPojo.setCreatedate(new Date());   // 创建时间
                cidictPojo.setLister(loginUser.getRealname());   // 制表
                cidictPojo.setListerid(loginUser.getUserid());    // 制表id
                cidictPojo.setModifydate(new Date());   //修改时间
                cidictPojo = this.saDictService.insert(cidictPojo);
            }
            return R.ok(cidictPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取数据字典详细信息", notes = "获取数据字典详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.List")
    public R<SaDictPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saDictService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.List")
    public R<PageInfo<SaDictitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Dict.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDictService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取数据字典详细信息", notes = "获取数据字典详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.List")
    public R<SaDictPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saDictService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.List")
    public R<PageInfo<SaDictPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Dict.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saDictService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.List")
    public R<PageInfo<SaDictPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Dict.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDictService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增数据字典", notes = "新增数据字典", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.Add")
    public R<SaDictPojo> create(@RequestBody String json) {
        try {
            SaDictPojo saDictPojo = JSONArray.parseObject(json, SaDictPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saDictPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDictPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDictPojo.setCreatedate(new Date());   // 创建时间
            saDictPojo.setLister(loginUser.getRealname());   // 制表
            saDictPojo.setListerid(loginUser.getUserid());    // 制表id            
            saDictPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDictService.insert(saDictPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改数据字典", notes = "修改数据字典", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.Edit")
    public R<SaDictPojo> update(@RequestBody String json) {
        try {
            SaDictPojo saDictPojo = JSONArray.parseObject(json, SaDictPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saDictPojo.setLister(loginUser.getRealname());   // 制表
            saDictPojo.setListerid(loginUser.getUserid());    // 制表id   
            saDictPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDictService.update(saDictPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除数据字典", notes = "删除数据字典", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saDictService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增数据字典Item", notes = "新增数据字典Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.Add")
    public R<SaDictitemPojo> createItem(@RequestBody String json) {
        try {
            SaDictitemPojo saDictitemPojo = JSONArray.parseObject(json, SaDictitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saDictitemService.insert(saDictitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除数据字典Item", notes = "删除数据字典Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saDictitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Dict.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaDictPojo saDictPojo = this.saDictService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDictPojo);
        // 加入公司信息
        if (loginUser.getTenantinfo() != null) inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从Sa_Redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saDictPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaDictitemPojo saDictitemPojo = new SaDictitemPojo();
                    saDictPojo.getItem().add(saDictitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saDictPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

