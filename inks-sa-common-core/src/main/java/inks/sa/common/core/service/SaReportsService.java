package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaReportsPojo;

import java.util.List;

/**
 * 报表中心(SaReports)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:49
 */
public interface SaReportsService {


    SaReportsPojo getEntity(String key);

    PageInfo<SaReportsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saReportsPojo 实例对象
     * @return 实例对象
     */
    SaReportsPojo insert(SaReportsPojo saReportsPojo);

    /**
     * 修改数据
     *
     * @param saReportspojo 实例对象
     * @return 实例对象
     */
    SaReportsPojo update(SaReportsPojo saReportspojo);

    int delete(String key);

    List<SaReportsPojo> pullDefault(String code, LoginUser loginUser);

    PageInfo<SaReportsPojo> getPageListAll(QueryParam queryParam);

    List<SaReportsPojo> getListByModuleCode(String moduleCode);
}
