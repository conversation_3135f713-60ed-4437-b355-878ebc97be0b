package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaformcustomEntity;
import inks.sa.common.core.domain.pojo.SaformcustomPojo;
import inks.sa.common.core.mapper.SaformcustomMapper;
import inks.sa.common.core.service.SaformcustomService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 自定义界面(Saformcustom)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-09 10:48:38
 */
@Service("saformcustomService")
public class SaformcustomServiceImpl implements SaformcustomService {
    @Resource
    private SaformcustomMapper saformcustomMapper;


    @Override
    public SaformcustomPojo getEntity(String key) {
        return this.saformcustomMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaformcustomPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaformcustomPojo> lst = saformcustomMapper.getPageList(queryParam);
            PageInfo<SaformcustomPojo> pageInfo = new PageInfo<SaformcustomPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saformcustomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaformcustomPojo insert(SaformcustomPojo saformcustomPojo) {
        //初始化NULL字段
        if (saformcustomPojo.getGengroupid() == null) saformcustomPojo.setGengroupid("");
        if (saformcustomPojo.getModulecode() == null) saformcustomPojo.setModulecode("");
        if (saformcustomPojo.getFrmcode() == null) saformcustomPojo.setFrmcode("");
        if (saformcustomPojo.getFrmname() == null) saformcustomPojo.setFrmname("");
        if (saformcustomPojo.getFrmcontent() == null) saformcustomPojo.setFrmcontent("");
        if (saformcustomPojo.getRownum() == null) saformcustomPojo.setRownum(0);
        if (saformcustomPojo.getEnabledmark() == null) saformcustomPojo.setEnabledmark(0);
        if (saformcustomPojo.getRemark() == null) saformcustomPojo.setRemark("");
        if (saformcustomPojo.getCreateby() == null) saformcustomPojo.setCreateby("");
        if (saformcustomPojo.getCreatebyid() == null) saformcustomPojo.setCreatebyid("");
        if (saformcustomPojo.getCreatedate() == null) saformcustomPojo.setCreatedate(new Date());
        if (saformcustomPojo.getLister() == null) saformcustomPojo.setLister("");
        if (saformcustomPojo.getListerid() == null) saformcustomPojo.setListerid("");
        if (saformcustomPojo.getModifydate() == null) saformcustomPojo.setModifydate(new Date());
        if (saformcustomPojo.getCustom1() == null) saformcustomPojo.setCustom1("");
        if (saformcustomPojo.getCustom2() == null) saformcustomPojo.setCustom2("");
        if (saformcustomPojo.getCustom3() == null) saformcustomPojo.setCustom3("");
        if (saformcustomPojo.getCustom4() == null) saformcustomPojo.setCustom4("");
        if (saformcustomPojo.getCustom5() == null) saformcustomPojo.setCustom5("");
        if (saformcustomPojo.getTenantid() == null) saformcustomPojo.setTenantid("");
        if (saformcustomPojo.getTenantname() == null) saformcustomPojo.setTenantname("");
        if (saformcustomPojo.getRevision() == null) saformcustomPojo.setRevision(0);
        SaformcustomEntity saformcustomEntity = new SaformcustomEntity();
        BeanUtils.copyProperties(saformcustomPojo, saformcustomEntity);
        //生成雪花id
        saformcustomEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saformcustomEntity.setRevision(1);  //乐观锁
        this.saformcustomMapper.insert(saformcustomEntity);
        return this.getEntity(saformcustomEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saformcustomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaformcustomPojo update(SaformcustomPojo saformcustomPojo) {
        SaformcustomEntity saformcustomEntity = new SaformcustomEntity();
        BeanUtils.copyProperties(saformcustomPojo, saformcustomEntity);
        this.saformcustomMapper.update(saformcustomEntity);
        return this.getEntity(saformcustomEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saformcustomMapper.delete(key);
    }

    @Override
    public SaformcustomPojo getEntityByCode(String code) {
        return this.saformcustomMapper.getEntityByCode(code);
    }
}
