package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaWarninguserPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaWarninguserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 预警用户(Sa_WarningUser)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:36
 */
@RestController
@RequestMapping("SaWarningUser")
@Api(tags = "通用:预警用户")
public class A_SaWarninguserController {
    private final static Logger logger = LoggerFactory.getLogger(A_SaWarninguserController.class);
    @Resource
    private SaWarninguserService saWarninguserService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取预警用户详细信息", notes = "获取预警用户详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_WarningUser.List")
    public R<SaWarninguserPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWarninguserService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_WarningUser.List")
    public R<PageInfo<SaWarninguserPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_WarningUser.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saWarninguserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增预警用户", notes = "新增预警用户", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_WarningUser.Add")
    public R<SaWarninguserPojo> create(@RequestBody String json) {
        try {
            SaWarninguserPojo saWarninguserPojo = JSONArray.parseObject(json, SaWarninguserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saWarninguserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saWarninguserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saWarninguserPojo.setCreatedate(new Date());   // 创建时间
            saWarninguserPojo.setLister(loginUser.getRealname());   // 制表
            saWarninguserPojo.setListerid(loginUser.getUserid());    // 制表id
            saWarninguserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saWarninguserService.insert(saWarninguserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改预警用户", notes = "修改预警用户", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_WarningUser.Edit")
    public R<SaWarninguserPojo> update(@RequestBody String json) {
        try {
            SaWarninguserPojo saWarninguserPojo = JSONArray.parseObject(json, SaWarninguserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saWarninguserPojo.setLister(loginUser.getRealname());   // 制表
            saWarninguserPojo.setListerid(loginUser.getUserid());    // 制表id
            saWarninguserPojo.setModifydate(new Date());   //修改时间
            //            saWarninguserPojo.setAssessor(""); // 审核员
            //            saWarninguserPojo.setAssessorid(""); // 审核员id
            //            saWarninguserPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saWarninguserService.update(saWarninguserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除预警用户", notes = "删除预警用户", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_WarningUser.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWarninguserService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_WarningUser.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaWarninguserPojo saWarninguserPojo = this.saWarninguserService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saWarninguserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

