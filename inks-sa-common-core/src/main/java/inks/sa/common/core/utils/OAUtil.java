package inks.sa.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
// 用于OA审批将单据主子表的Date类型时间全部转为"yyyy-MM-dd HH:mm:ss"
// 将子表的attributejson、costgroupjson、custjson展开
public class OAUtil {
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    // 总入口：主表带子表一起转Map
    public static Map<String, Object> billToOAMap(Object bill) {
        Map<String, Object> map = beanToMap(bill);
        try {
            // 通过反射检测是否有getItem()方法
            Field[] fields = bill.getClass().getDeclaredFields();
            for (Field field : fields) {
                if ("item".equalsIgnoreCase(field.getName())) {
                    field.setAccessible(true);
                    Object itemObj = field.get(bill);
                    if (itemObj instanceof List) {
                        map.put("item", itemListToMaps((List<?>) itemObj));
                    }
                }
            }
        } catch (Exception ignored) {}
        return map;
    }

    // JavaBean转Map并格式化Date字段
    public static Map<String, Object> beanToMap(Object bean) {
        Map<String, Object> map = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        for (Field field : bean.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object val = field.get(bean);
                if (val instanceof Date) {
                    map.put(field.getName(), sdf.format((Date) val));
                } else {
                    map.put(field.getName(), val);
                }
            } catch (Exception ignored) {}
        }
        return map;
    }

    // 子表列表处理
    public static List<Map<String, Object>> itemListToMaps(List<?> objList) {
        List<Map<String, Object>> list = Lists.newArrayList();
        if (objList == null || objList.isEmpty()) return list;

        for (Object bean : objList) {
            Map<String, Object> map = beanToMap(bean);

            // 解析 attributejson
            if (map.get("attributejson") != null && !map.get("attributejson").toString().isEmpty()) {
                try {
                    List<Map<String, Object>> attrs = JSON.parseObject(map.get("attributejson").toString(), List.class);
                    for (Map<String, Object> m : attrs) {
                        if (m.get("key") != null && m.get("value") != null)
                            map.put(m.get("key").toString(), m.get("value").toString());
                    }
                } catch (Exception ignored) {}
            }

            // 解析 costgroupjson
            if (map.get("costgroupjson") != null && !map.get("costgroupjson").toString().isEmpty()) {
                try {
                    List<Map<String, Object>> attrs = JSON.parseObject(map.get("costgroupjson").toString(), List.class);
                    for (Map<String, Object> m : attrs) {
                        if (m.get("key") != null && m.get("value") != null)
                            map.put(m.get("key").toString(), m.get("value").toString());
                    }
                } catch (Exception ignored) {}
            }

            // 解析 custjson
            if (map.get("custjson") != null && !map.get("custjson").toString().isEmpty()) {
                try {
                    List<Map<String, Object>> attrs = JSON.parseObject(map.get("custjson").toString(), List.class);
                    for (Map<String, Object> m : attrs) {
                        if (m.get("key") != null && m.get("value") != null)
                            map.put(m.get("key").toString(), m.get("value").toString());
                    }
                } catch (Exception ignored) {}
            }

            list.add(map);
        }
        return list;
    }



}
