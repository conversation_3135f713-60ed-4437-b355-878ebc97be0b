package inks.sa.common.core.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 公司信息表(SaCompany)实体类
 *
 * <AUTHOR>
 * @since 2024-02-22 12:48:49
 */
public class SaCompanyEntity implements Serializable {
    private static final long serialVersionUID = -76406127489023128L;
    // ID
    private String id;
    // 名称
    private String name;
    // 英文名
    private String englishname;
    // 信用代码
    private String creditcode;
    // 地址
    private String address;
    // 银行账号
    private String bankaccount;
    // 开户银行
    private String bankofdeposit;
    // 联系人
    private String contactperson;
    // 电话
    private String tel;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

// ID

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 名称

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
// 英文名

    public String getEnglishname() {
        return englishname;
    }

    public void setEnglishname(String englishname) {
        this.englishname = englishname;
    }
// 信用代码

    public String getCreditcode() {
        return creditcode;
    }

    public void setCreditcode(String creditcode) {
        this.creditcode = creditcode;
    }
// 地址

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
// 银行账号

    public String getBankaccount() {
        return bankaccount;
    }

    public void setBankaccount(String bankaccount) {
        this.bankaccount = bankaccount;
    }
// 开户银行

    public String getBankofdeposit() {
        return bankofdeposit;
    }

    public void setBankofdeposit(String bankofdeposit) {
        this.bankofdeposit = bankofdeposit;
    }
// 联系人

    public String getContactperson() {
        return contactperson;
    }

    public void setContactperson(String contactperson) {
        this.contactperson = contactperson;
    }
// 电话

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 租户名称

    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

