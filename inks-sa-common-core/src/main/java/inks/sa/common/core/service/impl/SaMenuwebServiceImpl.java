package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaMenuwebEntity;
import inks.sa.common.core.domain.pojo.SaMenuwebPojo;
import inks.sa.common.core.mapper.SaMenuwebMapper;
import inks.sa.common.core.service.SaMenuwebService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 后台导航(SaMenuweb)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@Service("saMenuwebService")
public class SaMenuwebServiceImpl implements SaMenuwebService {
    @Resource
    private SaMenuwebMapper saMenuwebMapper;

    @Override
    public SaMenuwebPojo getEntity(String key) {
        return this.saMenuwebMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaMenuwebPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMenuwebPojo> lst = saMenuwebMapper.getPageList(queryParam);
            PageInfo<SaMenuwebPojo> pageInfo = new PageInfo<SaMenuwebPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaMenuwebPojo insert(SaMenuwebPojo saMenuwebPojo) {
        //初始化NULL字段
        cleanNull(saMenuwebPojo);
        SaMenuwebEntity saMenuwebEntity = new SaMenuwebEntity(); 
        BeanUtils.copyProperties(saMenuwebPojo,saMenuwebEntity);
        //生成雪花id
          saMenuwebEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
          this.saMenuwebMapper.insert(saMenuwebEntity);
        return this.getEntity(saMenuwebEntity.getNavid());
  
    }

    /**
     * 修改数据
     *
     * @param saMenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMenuwebPojo update(SaMenuwebPojo saMenuwebPojo) {
        SaMenuwebEntity saMenuwebEntity = new SaMenuwebEntity(); 
        BeanUtils.copyProperties(saMenuwebPojo,saMenuwebEntity);
        this.saMenuwebMapper.update(saMenuwebEntity);
        return this.getEntity(saMenuwebEntity.getNavid());
    }


    @Override
    public int delete(String key) {
        return this.saMenuwebMapper.delete(key) ;
    }

    @Override
    public List<SaMenuwebPojo> getListByPid(String key) {
        return  this.saMenuwebMapper.getListByPid(key);
    }

    @Override
    public List<SaMenuwebPojo> getListAll() {
        return this.saMenuwebMapper.getListAll();
    }

    @Override
    public List<SaMenuwebPojo> getListByNavids(List<String> navids) {
        return this.saMenuwebMapper.getListByNavids(navids);
    }

    private static void cleanNull(SaMenuwebPojo saMenuwebPojo) {
        if(saMenuwebPojo.getNavpid()==null) saMenuwebPojo.setNavpid("");
        if(saMenuwebPojo.getNavtype()==null) saMenuwebPojo.setNavtype("");
        if(saMenuwebPojo.getNavcode()==null) saMenuwebPojo.setNavcode("");
        if(saMenuwebPojo.getNavname()==null) saMenuwebPojo.setNavname("");
        if(saMenuwebPojo.getNavgroup()==null) saMenuwebPojo.setNavgroup("");
        if(saMenuwebPojo.getRownum()==null) saMenuwebPojo.setRownum(0);
        if(saMenuwebPojo.getImagecss()==null) saMenuwebPojo.setImagecss("");
        if(saMenuwebPojo.getIconurl()==null) saMenuwebPojo.setIconurl("");
        if(saMenuwebPojo.getNavigateurl()==null) saMenuwebPojo.setNavigateurl("");
        if(saMenuwebPojo.getMvcurl()==null) saMenuwebPojo.setMvcurl("");
        if(saMenuwebPojo.getModuletype()==null) saMenuwebPojo.setModuletype("");
        if(saMenuwebPojo.getModulecode()==null) saMenuwebPojo.setModulecode("");
        if(saMenuwebPojo.getRolecode()==null) saMenuwebPojo.setRolecode("");
        if(saMenuwebPojo.getImageindex()==null) saMenuwebPojo.setImageindex("");
        if(saMenuwebPojo.getImagestyle()==null) saMenuwebPojo.setImagestyle("");
        if(saMenuwebPojo.getEnabledmark()==null) saMenuwebPojo.setEnabledmark(0);
        if(saMenuwebPojo.getRemark()==null) saMenuwebPojo.setRemark("");
        if(saMenuwebPojo.getPermissioncode()==null) saMenuwebPojo.setPermissioncode("");
        if(saMenuwebPojo.getFunctionid()==null) saMenuwebPojo.setFunctionid("");
        if(saMenuwebPojo.getFunctioncode()==null) saMenuwebPojo.setFunctioncode("");
        if(saMenuwebPojo.getFunctionname()==null) saMenuwebPojo.setFunctionname("");
        if(saMenuwebPojo.getLister()==null) saMenuwebPojo.setLister("");
        if(saMenuwebPojo.getCreatedate()==null) saMenuwebPojo.setCreatedate(new Date());
        if(saMenuwebPojo.getModifydate()==null) saMenuwebPojo.setModifydate(new Date());
        if(saMenuwebPojo.getDeletemark()==null) saMenuwebPojo.setDeletemark(0);
        if(saMenuwebPojo.getDeletelister()==null) saMenuwebPojo.setDeletelister("");
        if(saMenuwebPojo.getDeletedate()==null) saMenuwebPojo.setDeletedate(new Date());
   }

}
