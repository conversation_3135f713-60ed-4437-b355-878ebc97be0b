package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaDeptuserEntity;
import inks.sa.common.core.domain.pojo.SaDeptuserPojo;
import inks.sa.common.core.mapper.SaDeptuserMapper;
import inks.sa.common.core.service.SaDeptuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 组织用户表(SaDeptuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-05 13:00:44
 */
@Service("saDeptuserService")
public class SaDeptuserServiceImpl implements SaDeptuserService {
    @Resource
    private SaDeptuserMapper saDeptuserMapper;

    @Override
    public SaDeptuserPojo getEntity(String key) {
        return this.saDeptuserMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDeptuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDeptuserPojo> lst = saDeptuserMapper.getPageList(queryParam);
            PageInfo<SaDeptuserPojo> pageInfo = new PageInfo<SaDeptuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaDeptuserPojo insert(SaDeptuserPojo saDeptuserPojo) {
        // 检查user只能属于一个部门
        if (this.saDeptuserMapper.countByUserid(saDeptuserPojo.getUserid(), null) > 0) {
            throw new BaseBusinessException("用户只能属于一个部门");
        }
        //初始化NULL字段
        cleanNull(saDeptuserPojo);
        SaDeptuserEntity saDeptuserEntity = new SaDeptuserEntity();
        BeanUtils.copyProperties(saDeptuserPojo, saDeptuserEntity);
        //生成雪花id
        saDeptuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDeptuserEntity.setRevision(1);  //乐观锁
        this.saDeptuserMapper.insert(saDeptuserEntity);
        return this.getEntity(saDeptuserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDeptuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDeptuserPojo update(SaDeptuserPojo saDeptuserPojo) {
        // 检查user只能属于一个部门
        if (this.saDeptuserMapper.countByUserid(saDeptuserPojo.getUserid(), saDeptuserPojo.getId()) > 0) {
            throw new BaseBusinessException("用户只能属于一个部门");
        }
        SaDeptuserEntity saDeptuserEntity = new SaDeptuserEntity();
        BeanUtils.copyProperties(saDeptuserPojo, saDeptuserEntity);
        this.saDeptuserMapper.update(saDeptuserEntity);
        return this.getEntity(saDeptuserEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDeptuserMapper.delete(key);
    }


    private static void cleanNull(SaDeptuserPojo saDeptuserPojo) {
        if (saDeptuserPojo.getDeptid() == null) saDeptuserPojo.setDeptid("");
        if (saDeptuserPojo.getDeptcode() == null) saDeptuserPojo.setDeptcode("");
        if (saDeptuserPojo.getDeptname() == null) saDeptuserPojo.setDeptname("");
        if (saDeptuserPojo.getUserid() == null) saDeptuserPojo.setUserid("");
        if (saDeptuserPojo.getUsername() == null) saDeptuserPojo.setUsername("");
        if (saDeptuserPojo.getRealname() == null) saDeptuserPojo.setRealname("");
        if (saDeptuserPojo.getIsadmin() == null) saDeptuserPojo.setIsadmin(0);
        if (saDeptuserPojo.getRownum() == null) saDeptuserPojo.setRownum(0);
        if (saDeptuserPojo.getCreateby() == null) saDeptuserPojo.setCreateby("");
        if (saDeptuserPojo.getCreatebyid() == null) saDeptuserPojo.setCreatebyid("");
        if (saDeptuserPojo.getCreatedate() == null) saDeptuserPojo.setCreatedate(new Date());
        if (saDeptuserPojo.getLister() == null) saDeptuserPojo.setLister("");
        if (saDeptuserPojo.getListerid() == null) saDeptuserPojo.setListerid("");
        if (saDeptuserPojo.getModifydate() == null) saDeptuserPojo.setModifydate(new Date());
        if (saDeptuserPojo.getCustom1() == null) saDeptuserPojo.setCustom1("");
        if (saDeptuserPojo.getCustom2() == null) saDeptuserPojo.setCustom2("");
        if (saDeptuserPojo.getCustom3() == null) saDeptuserPojo.setCustom3("");
        if (saDeptuserPojo.getCustom4() == null) saDeptuserPojo.setCustom4("");
        if (saDeptuserPojo.getCustom5() == null) saDeptuserPojo.setCustom5("");
        if (saDeptuserPojo.getTenantid() == null) saDeptuserPojo.setTenantid("");
        if (saDeptuserPojo.getTenantname() == null) saDeptuserPojo.setTenantname("");
        if (saDeptuserPojo.getRevision() == null) saDeptuserPojo.setRevision(0);
    }

}
