package inks.sa.common.core.utils;

import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.LoginUser;
import inks.sa.common.core.config.InksConfigThreadLocal_Sa;
import inks.sa.common.core.mapper.SqlUtilMapper;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class SqlUtil {

    @Resource
    private JdbcTemplate jdbcTemplate;
    /**
     * 部门权限过滤，拼接指定字段的 IN 子句
     *
     * @param qpfilter   原SQL过滤条件
     * @param loginUser  当前登录用户对象
     * @param columnName 需要过滤的表字段名（如 Bus_Machining.Deptid）
     * @return 拼接好部门权限后的过滤条件
     */
    public static String filterDeptid(String qpfilter, LoginUser loginUser, String columnName, String configkey) {//"module.sale.deptpartition"
        // 部门权限过滤：当不是(超级)管理员，且配置了部门权限过滤参数时，进行部门权限过滤
        boolean isAdmin = Objects.equals(loginUser.getIsadmin(), 1) || Objects.equals(loginUser.getIsadmin(), 2);
        if (isAdmin || !"true".equals(InksConfigThreadLocal_Sa.getConfig(configkey))) {
            return qpfilter;
        }
        // 收集当前部门 ID 和所有子部门 ID
        Set<String> deptIds = Stream.concat(
                Optional.ofNullable(loginUser.getTenantinfo().getDeptid())
                        .filter(StringUtils::isNotBlank)
                        .map(Stream::of)
                        .orElseGet(Stream::empty),
                Optional.ofNullable(loginUser.getTenantinfo().getLstdept())
                        .orElseGet(Collections::emptyList)
                        .stream()
                        .map(DeptinfoPojo::getDeptid)
                        .filter(StringUtils::isNotBlank)
        ).collect(Collectors.toSet());
        // 如果非空，则拼 IN 子句
        if (!deptIds.isEmpty()) {
            String inClause = deptIds.stream()
                    .map(id -> "'" + id + "'")
                    .collect(Collectors.joining(","));
            qpfilter += " AND " + columnName + " IN (" + inClause + ")";
        }
        return qpfilter;
    }

    /**
     * 将指定表的指定字段设置为null
     *
     * @param tableName 表名
     * @param fieldName 字段名
     * @param id        id
     * @return 影响的行数
     */
    @ApiOperation(value = "将指定表的指定字段设置为null", produces = "application/json")
    @GetMapping("/updateFieldToNull")
    public int updateFieldToNull(String tableName, String fieldName, String id) {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(tableName) || StringUtils.isBlank(fieldName)) {
            throw new IllegalArgumentException("id, tableName, and fieldName must not be blank");
        }
        // 拼接表名和字段名，值参数化防注入
        String sql = String.format("UPDATE `%s` SET `%s` = NULL WHERE id = ?", tableName, fieldName);
        return jdbcTemplate.update(sql, id);
    }

    @PostMapping(value = "/sql", consumes = MediaType.TEXT_PLAIN_VALUE)
    public int sql(@RequestBody String sql) {
        if (StringUtils.isBlank(sql)) {
            throw new IllegalArgumentException("SQL语句不能为空");
        }
        return jdbcTemplate.update(sql);
    }


}
