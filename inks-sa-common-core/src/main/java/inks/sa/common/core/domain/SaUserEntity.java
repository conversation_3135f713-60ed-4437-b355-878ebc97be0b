package inks.sa.common.core.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 用户(SaUser)实体类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:04:20
 */
@Data
public class SaUserEntity implements Serializable {
    private static final long serialVersionUID = -41730199026695958L;
     // ID
    private String id;
     // 用户名
    private String username;
     // 真实姓名
    private String realname;
     // 密码
    private String password;
     // 手机号
    private String phone;
     // 电子邮箱
    private String email;
     // 阿里邮箱授权码
    private String emailauthcode;
     // 性别
    private Integer sex;
     // 邮箱
    private String avatar;
     // 目录名
    private String dirname;
     // minio文件名
    private String filename;
     // 0新注册1技术员
    private Integer roletype;
     // 是否管理员
    private Integer adminmark;
     // 用户状态
    private Integer userstate;
     // 备注
    private String remark;
     // 创建人
    private String createby;
     // 创建时间
    private Date createdate;
     // 制表人
    private String lister;
     // 修改时间
    private Date modifydate;
     // 锁
    private Integer revision;



}

