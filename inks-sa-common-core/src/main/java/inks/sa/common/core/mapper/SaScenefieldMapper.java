package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaScenefieldEntity;
import inks.sa.common.core.domain.pojo.SaScenefieldPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 场景字段(Sascenefield)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-24 11:35:06
 */
@Mapper
public interface SaScenefieldMapper {


    SaScenefieldPojo getEntity(@Param("key") String key);


    List<SaScenefieldPojo> getPageList(QueryParam queryParam);


    int insert(SaScenefieldEntity sascenefieldEntity);


    int update(SaScenefieldEntity sascenefieldEntity);


    int delete(@Param("key") String key);

    List<SaScenefieldPojo> getListByCode(@Param("code") String code);

}

