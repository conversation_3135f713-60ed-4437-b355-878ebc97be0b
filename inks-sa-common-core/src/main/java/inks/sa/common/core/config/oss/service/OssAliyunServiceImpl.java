package inks.sa.common.core.config.oss.service;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.IdUtil;
import com.google.common.base.Strings;
import inks.common.core.domain.FileInfo;
import inks.sa.common.core.config.oss.Storage;
import inks.sa.common.core.domain.pojo.SaFilelogPojo;
import inks.sa.common.core.service.SaFilelogService;
import inks.sa.common.core.service.impl.SaFilelogServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * 阿里云对象存储服务实现类
 * 基于阿里云OSS实现文件的存储、获取与管理
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2020-11-04
 */
@Service
public class OssAliyunServiceImpl implements OssService {

    @Resource
    @Qualifier("aliyunStorage")
    private Storage storage;

    @Autowired
    private OSSConfigManager configManager;

    /**
     * 默认存储桶名
     */
    private String BUCKET;

    /**
     * 访问URL前缀
     */
    private String URL_PREFIX;
    @Autowired
    private SaFilelogServiceImpl saFilelogService;

    /**
     * 初始化阿里云OSS配置
     */
    @PostConstruct
    public void init() {
        this.BUCKET = configManager.getAliyunBucket();
        this.URL_PREFIX = configManager.getAliyunUrlprefix();
    }

    @Value("aliyun")
    private String storageType;
    @Resource
    private SaFilelogService filelogService;
    /**
     * 上传文件到阿里云OSS
     *
     * @param file       上传的文件
     * @param bucket 存储对象名称
     * @return 文件信息对象
     */
    private FileInfo of(MultipartFile file, String filePath, String bucket, String dirname, String objectName, String module) {
        // 1. 获取原始文件名、大小、类型
        String oriFileName;
        long fileSize;
        String contentType;

        if (file != null) {
            oriFileName = file.getOriginalFilename();
            fileSize = file.getSize();
            contentType = file.getContentType();
        } else if (!org.apache.commons.lang3.StringUtils.isBlank(filePath)) {
            File inputFile = new File(filePath);
            oriFileName = inputFile.getName();
            fileSize = inputFile.length();
            String fileSuffix = org.springframework.util.StringUtils.getFilenameExtension(oriFileName);
            contentType = getContentType(fileSuffix);
        } else {
            throw new IllegalArgumentException("file 和 filePath 不能同时为空");
        }

        // 2. objectName 处理
        if (org.apache.commons.lang3.StringUtils.isBlank(objectName)) {
            objectName = IdUtil.simpleUUID().substring(1, 21);
        }

        // 3. 文件扩展名与最终文件名
        String fileSuffix = org.springframework.util.StringUtils.getFilenameExtension(oriFileName);
        String fileName = objectName;
        if (!org.apache.commons.lang3.StringUtils.isBlank(fileSuffix) && !objectName.endsWith("." + fileSuffix)) {
            fileName = objectName.concat(".").concat(fileSuffix);
        }

        // 4. 组装 FileInfo
        FileInfo fileInfo = new FileInfo();
        fileInfo.setBucketname(bucket.toLowerCase());
        fileInfo.setFilename(fileName);
        fileInfo.setFileoriname(oriFileName);
        fileInfo.setFilesize(fileSize);
        fileInfo.setContenttype(contentType);
        fileInfo.setDirname(dirname);
        fileInfo.setFilesuffix(fileSuffix);
        fileInfo.setStorage(storageType);
        fileInfo.setFileurl(URL_PREFIX + dirname + "/" + fileName);

        // 20250616 文件上传日志
        SaFilelogPojo utsFilelogPojo = new SaFilelogPojo();
        utsFilelogPojo.setOptype(0);
        utsFilelogPojo.setUsedmark(0);
        utsFilelogPojo.setModule(module);
        utsFilelogPojo.setFileoriname(fileInfo.getFileoriname());
        utsFilelogPojo.setBucketname(fileInfo.getBucketname());
        utsFilelogPojo.setDirname(fileInfo.getDirname());
        utsFilelogPojo.setFilename(fileInfo.getFilename());
        utsFilelogPojo.setFileurl(fileInfo.getFileurl());
        utsFilelogPojo.setFilesize(fileInfo.getFilesize());
        utsFilelogPojo.setContenttype(fileInfo.getContenttype());
        utsFilelogPojo.setFilesuffix(fileInfo.getFilesuffix());
        utsFilelogPojo.setStorage(fileInfo.getStorage());
        saFilelogService.insert(utsFilelogPojo);

        return fileInfo;
    }

    @Override
    public FileInfo of(MultipartFile file, String dirname, String module) {
        return this.of(file, BUCKET, dirname, module);
    }

    // 保持老接口兼容
    private FileInfo of(MultipartFile file, String bucket, String dirname, String module) {
        return of(file, null, bucket, dirname, null, module);
    }

    private FileInfo of(String filePath, String bucket, String dirname, String objectName, String module) {
        return of(null, filePath, bucket, dirname, objectName, module);
    }


    // aliyun返回连接没有桶名，但bucket必须已经存在才能上传
    @Transactional
    public FileInfo putFile(MultipartFile file, String bucket, String dirname) {
        FileInfo fileInfo = of(file, bucket, dirname);
        // 上传文件
        try {
            storage.putObject(fileInfo.getBucketname(), fileInfo.getDirname(), fileInfo.getFilename(), file.getInputStream(), fileInfo.getContenttype(), fileInfo.getFileoriname());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fileInfo;
    }

    @Transactional
    public FileInfo putFile(String filePath, String bucket, String dirname, String objectname, String module) {
        FileInfo fileInfo = null;
        try {
            File file = new File(filePath);
            String fileName = file.getName();
            String contentType = getContentType(fileName); // 根据文件名获取内容类型，自行实现 getContentType() 方法
            fileInfo = of(filePath, bucket, dirname, objectname, module);
            FileInputStream inputStream = new FileInputStream(file);
            storage.putObject(fileInfo.getBucketname(), fileInfo.getDirname(), fileInfo.getFilename(), inputStream, contentType, fileInfo.getFileoriname());
            inputStream.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return fileInfo;
    }


    @Override
    public FileInfo putFile(MultipartFile file, String bucket, String dir, String filename, String module) {
        return null;
    }

    // 获取文件路径下文件的类型
    private String getContentType(String fileExtension) {
        String contentType = "";

        if (fileExtension.endsWith(".txt")) {
            contentType = "text/plain";
        } else if (fileExtension.endsWith(".jpg") || fileExtension.endsWith(".jpeg")) {
            contentType = "image/jpeg";
        } else if (fileExtension.endsWith(".png")) {
            contentType = "image/png";
        } else if (fileExtension.endsWith(".pdf")) {
            contentType = "application/pdf";
        } else if (fileExtension.endsWith(".zip")) {
            contentType = "application/zip";
        } else if (fileExtension.endsWith(".sql")) {
            contentType = "application/sql";
        } else {
            contentType = "application/octet-stream"; // 默认的内容类型
        }
        // 可else if添加其他文件类型的判断条件
        return contentType;
    }


    @Override
    public FileInfo upload(MultipartFile multipartFile, String dirname, String module) {
        return this.putFile(multipartFile, BUCKET, dirname);
    }


    @Override
    public FileInfo putFile(String filePath, String objectName, String module) {
        return null;
    }

    @Override
    public boolean remove(String bucketName, String objectname) {
        return false;
    }

    @Override
    public FileInfo uploadStream(InputStream inputStream, String bucketName, String objectName, long size, String contentType,String module) {
        return null;
    }


    @Override
    public void updateStorage(String endpoint, String accessKeyId, String accessKeySecret) {

    }
}
