package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaWebnavEntity;
import inks.sa.common.core.domain.pojo.SaWebnavPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Pc导航(SaWebnav)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-02 13:42:49
 */
@Mapper
public interface SaWebnavMapper {


    SaWebnavPojo getEntity(@Param("key") String key);


    List<SaWebnavPojo> getPageList(QueryParam queryParam);

    int insert(SaWebnavEntity saWebnavEntity);

    int update(SaWebnavEntity saWebnavEntity);


    int delete(@Param("key") String key);

}

