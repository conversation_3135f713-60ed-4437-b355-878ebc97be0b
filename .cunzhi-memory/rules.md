# 开发规范和规则

- 数据库启动问题修复：通过@DependsOn("flywayMigrationCompleted")确保InksConfigThreadLocal_Sa和OSSConfigManager在Flyway创建数据库后再初始化，避免"Unknown database"错误
- 数据库启动问题最终修复：禁用Spring Boot自动Flyway配置(spring.flyway.enabled=false)，使用自定义DataSourceHelper完全接管数据库创建和Flyway迁移，避免与Spring Boot自动配置冲突
- Flyway配置完全代码化：移除yml中所有flyway配置，在DataSourceHelper中实现智能数据库检测（只有数据库不存在时才创建并执行迁移），通过.placeholderReplacement(false)禁用占位符替换解决${...}解析错误
- Flyway远程SQL支持：修改DataSourceHelper支持从远程URL下载SQL文件执行，通过spring.flyway.initsql配置远程SQL文件地址，自动下载到临时目录并按Flyway命名规范(V1.0__remote_init.sql)执行迁移
- Flyway进度可视化：实现自动弹出网页显示数据库初始化进度，通过WebSocket实时推送进度信息，包含进度条、日志显示、自动打开浏览器等功能，解决客户部署时看不到控制台日志的问题
- Flyway进度显示优化：改为创建本地HTML文件显示进度，解决Web服务器未启动时无法访问的问题。初始化开始时创建进度页面，完成后创建结果页面，通过Desktop.open()直接打开本地HTML文件
- Flyway实时进度显示：创建ProgressPageManager类实现真正的实时进度更新，通过定时刷新HTML文件显示真实执行状态，解决了假进度问题，包含实时日志、进度条更新、自动打开浏览器等功能
- Flyway执行日志实时显示：创建FlywayProgressLogger和FlywayProgressLogFactory捕获Flyway执行过程中的详细日志，包括SQL语句执行进度、表创建状态、警告信息等，实时显示到HTML进度页面，解决建表过程中页面卡住的问题
- Flyway进度监控修复：由于Flyway 7.7.3版本API限制，改用FlywayProgressMonitor通过定时任务模拟和监控Flyway执行进度，提供SQL执行批次、迁移状态等信息的实时显示，解决LogFactory API兼容性问题
- Flyway进度显示优化：改进FlywayProgressMonitor，移除误导性的"批次"概念，改为基于时间的真实进度反馈，显示执行时间而非虚假的SQL批次数量，提供更诚实和有用的用户反馈
- Flyway表创建进度显示：通过分析SQL文件提取CREATE TABLE语句，实现真实的表创建进度显示，包括当前正在创建的表名、已完成表数量/总表数量、百分比进度等，提供更精确的建表进度反馈
- Flyway真实进度监控：实现FlywayProgressCallback监听Flyway事件和FlywayLogInterceptor拦截控制台输出，获取真实的表创建进度和当前操作的表名，完全同步Flyway的实际执行状态，解决模拟进度不准确的问题
- 修复HTML进度页面卡住问题：增强日志拦截器错误处理，提高页面刷新频率(800ms)，每次添加日志都更新文件，添加备用进度更新线程，确保即使日志拦截失效也能显示进度
- 修复HTML页面白屏问题：使用临时文件原子性替换避免文件竞争，调整刷新间隔(2-5秒)避免过于频繁，限制文件更新频率(最多每秒一次)，添加HTML转义和异常处理确保页面稳定性
- 实现表进度显示功能：创建SqlFileAnalyzer分析SQL文件获取总表数，修改ProgressPageManager支持表计数显示，在HTML页面显示"处理表: XXX (正常操作) 1/800"格式的进度信息，提供更精确的建表进度反馈
- 修复表进度显示问题：纠正表数量统计逻辑(只统计CREATE/DROP TABLE，不包括INSERT/UPDATE)，简化HTML日志显示(只显示表处理进度，移除冗余信息)，确保显示格式为"处理表: XXX (正常操作) 1/800"
- 移除冗余状态显示：修改addTableLog方法，只更新进度百分比不更新状态文本，避免显示重复的"正在处理数据表...(158/813)"信息，只保留"处理表: XXX (正常操作) 158/813"格式
- 用户要求添加查询实际数据库表数量的功能，在HTML页面显示"经过数据库查询，本次实际生成了x张表"
- UtsProxyController.proxyAll方法已完成K8s环境502 Bad Gateway和chunked编码解析错误的修复。核心修改：1)强制移除Transfer-Encoding头部统一使用Content-Length模式；2)添加CORS支持(Access-Control-Allow-Origin: *)；3)智能响应头冲突检测和处理；4)中文化日志信息。修复方案已通过寸止协议确认并实施完成。
- HTTP配置冲突修复：删除RestTemplateConfig.java，修改HttpClientConfig中的Bean名称为默认名称(restTemplate)，统一使用带连接池的企业级HTTP配置，避免Bean注入冲突
