# 常用模式和最佳实践

- S18M10B1题库排序接口实现模式：1.创建SortQuestionItemRequest请求类 2.创建OperateQuestionItemVO返回类 3.扩展SaQuestionbankitemMapper添加getByQuestionItemId方法 4.扩展SortUtils添加题库排序相关方法 5.在S18M10B1Controller添加/sort接口，参考S18M01B1Controller的sortFormItem实现
- S18M10B1题库details接口实现模式：1.创建QuestionBankDetailVO返回类包含题库信息和题目列表 2.在SaQuestionbankService接口添加details方法 3.在SaQuestionbankServiceImpl实现details方法，获取题库基础信息和题目列表 4.在S18M10B1Controller添加/details接口，参考S18M01B1Controller的details实现，返回题库详情包含题型JSON配置
