# 现有表单拖拽系统架构分析文档

## 1. 主子表操作流程图

### 1.1 Sa_Form主表 + Sa_FormItem子表架构

```
Sa_Form (主表)
├── id (主键，雪花ID)
├── name (表单名称)
├── description (表单描述)
├── formtype (表单类型，考试类型为4)
├── status (状态：1未发布 2收集中 3停止发布)
├── custom1-5 (自定义字段，可用于考试配置)
└── item[] (子表集合)

Sa_FormItem (子表)
├── id (主键，雪花ID)
├── pid (关联主表ID)
├── formitemid (表单项唯一标识)
├── type (控件类型：INPUT、RADIO、CHECKBOX等)
├── label (控件标题)
├── scheme (JSON配置，核心字段)
├── sort (排序字段，支持拖拽)
└── custom1-5 (扩展字段)
```

### 1.2 主子表操作流程

#### 新增流程 (SaFormServiceImpl.insert)
```
1. 初始化主表NULL字段 → 设置默认值
2. 生成雪花ID → 设置主表ID和乐观锁
3. 插入主表 → saFormMapper.insert(saFormEntity)
4. 循环处理子表 → 
   ├── 初始化子表NULL字段
   ├── 生成子表雪花ID
   ├── 设置pid关联主表
   └── 插入子表记录
5. 返回完整实体 → getBillEntity(id)
```

#### 更新流程 (SaFormServiceImpl.update)
```
1. 更新主表 → saFormMapper.update(saFormEntity)
2. 处理删除的子表项 → getDelItemIds + delete
3. 循环处理子表 →
   ├── 新增：id为空 → 生成ID并插入
   └── 更新：id存在 → 直接更新
4. 返回完整实体
```

## 2. Scheme字段结构规范

### 2.1 现有Scheme基础结构
```json
{
  "id": "1778972604204355584",
  "key": "input171297509480517129751226051778972604204355584", 
  "sort": 1507329,
  "style": {"width": "100%"},
  "config": {
    "tag": "t-input",
    "span": 24,
    "label": "<p>监控基础地址</p>",
    "formId": "input1712975094805",
    "required": false,
    "showLabel": true,
    "options": [
      {"label": "选项A", "value": 1, "type": "option"},
      {"label": "选项B", "value": 2, "type": "option"}
    ]
  },
  "typeId": "INPUT",
  "vModel": "input1712975094805",
  "regList": [],
  "disabled": false,
  "placeholder": "请输入监控基础地址"
}
```

### 2.2 支持的控件类型
- **INPUT**: 单行文本输入
- **TEXTAREA**: 多行文本输入  
- **NUMBER**: 数字输入
- **RADIO**: 单选框
- **CHECKBOX**: 多选框
- **SELECT**: 下拉选择
- **DATE**: 日期选择
- **IMAGE_UPLOAD**: 图片上传
- **INPUT_MAP**: 地图定位

### 2.3 ExamConfig扩展结构设计
```json
{
  // 原有scheme字段保持不变
  "examConfig": {
    "score": 10,                    // 题目分值
    "answer": [2],                  // 正确答案数组
    "enableScore": true,            // 是否启用评分
    "scoringType": 1,              // 评分类型：0不评分 1自动 2手动 5横向填空
    "answerAnalysis": "答案解析",    // 答案解析文本
    "showAnswer": false,           // 是否显示答案
    "scoreList": [5, 3, 2]         // 横向填空多点评分
  }
}
```

## 3. 排序算法工作原理

### 3.1 SortUtils核心算法
```java
// 基础参数
SORT_DEFAULT_INCR_FACT = 65536L  // 默认增量因子

// 三种排序场景
1. 移到最前面：newPos = afterPosition / 2
2. 移到中间：newPos = (beforePosition + afterPosition) / 2  
3. 移到最后面：newPos = beforePosition + 65536

// 极端情况处理
if (beforePos = 1, afterPos = 2) {
  // 无法找到中间整数，触发全量重排
  sortAllList() → 重新分配所有位置
}
```

### 3.2 Redis缓存机制
```java
// 缓存Key格式
String redisKey = "form:item:pos:{formId}"

// 初始化排序位置
getInitialSortPosition() → 
├── 检查Redis缓存
├── 获取最后一个排序值
└── 自增65536返回新位置

// 排序计算
calcSortPosition() → 
├── 判断排序场景
├── 计算新位置
└── 更新Redis缓存
```

## 4. API设计模式总结

### 4.1 控制器命名规范
```
S18M01B1Controller → 表单管理控制器
S18M10B1Controller → 题库管理控制器  
S18M05B1Controller → 表单设置控制器

命名模式：S18M{模块编号}B{业务编号}Controller
```

### 4.2 标准API接口
```java
// CRUD基础接口
@PostMapping("/getBillList")    // 分页查询
@GetMapping("/getBillEntity")   // 单条查询  
@PostMapping("/create")         // 新增
@PostMapping("/update")         // 更新
@GetMapping("/delete")          // 删除

// 子表操作接口
@PostMapping("/createItem")     // 新增子表项
@PostMapping("/updateItem")     // 更新子表项
@GetMapping("/deleteItem")      // 删除子表项
@PostMapping("/sort")           // 排序接口

// 统一返回格式
R.ok(data)    // 成功
R.fail(msg)   // 失败
```

### 4.3 权限控制模式
```java
// 用户权限检查
LoginUser loginUser = saRedisService.getLoginUser(request);
if (loginUser.getIsadmin() == 0) {
    throw new BaseBusinessException("非管理员无权限");
}

// 部门过滤
FilterSqlUtils.filterDept(queryParam, loginUser);
saFormPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
```

## 5. 前端组件复用策略

### 5.1 Vue组件架构
```javascript
// 基础技术栈
Vue 2.6.14 + Element UI 2.15.13 + Axios 0.24.0

// 组件结构模式
new Vue({
  el: '#app',
  data() { return { /* 响应式数据 */ }; },
  computed: { /* 计算属性 */ },
  mounted() { /* 初始化逻辑 */ },
  methods: { /* 业务方法 */ }
});
```

### 5.2 现有可复用组件
```javascript
// 表单验证组件
el-form + el-form-item + rules验证

// 数据展示组件  
el-table + 分页 + 搜索过滤

// 弹窗组件
el-dialog + 表单操作

// 文件上传组件
el-upload + 图片预览

// 选择器组件
el-select + el-option
el-radio-group + el-radio
el-checkbox-group + el-checkbox
```

### 5.3 Scheme生成机制
```javascript
// 现有的buildQuestionData方法
buildQuestionData() {
  const scheme = {
    typeId: this.questionForm.itemtype,
    config: {
      label: this.questionForm.label,
      options: this.needOptions ? this.questionForm.options : undefined
    },
    examConfig: {
      scoringType: this.questionForm.scoringType,
      score: this.questionForm.score,
      enableScore: true,
      answer: this.getCorrectAnswer(),
      answerAnalysis: this.questionForm.answerAnalysis
    }
  };
  
  return {
    pid: this.bankId,
    itemtype: this.questionForm.itemtype,
    label: this.questionForm.label,
    scheme: JSON.stringify(scheme)
  };
}
```

## 6. 架构扩展建议

### 6.1 ExamConfig集成策略
1. **向后兼容**：examConfig作为scheme的可选字段
2. **解析增强**：扩展现有ParseScheme方法支持examConfig
3. **验证机制**：添加examConfig字段验证逻辑
4. **默认值**：为考试类型表单项自动添加examConfig

### 6.2 拖拽组件复用
1. **排序逻辑**：直接复用SortUtils.calcSortPosition
2. **API接口**：复用/sort接口进行拖拽排序
3. **前端实现**：基于HTML5 Drag API或Sortable.js
4. **状态管理**：复用现有的Vue响应式数据管理

### 6.3 题库系统集成
1. **表结构复用**：Sa_QuestionBank + Sa_QuestionbankItem
2. **API模式复用**：S18M10B1Controller遵循相同模式
3. **Scheme兼容**：题库和表单使用相同的scheme结构
4. **组件复用**：最大化复用现有Vue组件和Element UI配置

## 7. Scheme字段解析机制分析

### 7.1 现有解析逻辑
```java
// SaFormdataServiceImpl.ParseScheme方法
private Map<Integer, String> ParseScheme(String scheme) {
    Map<Integer, String> parseLabel = new HashMap<>();
    try {
        JSONObject schemeJson = JSON.parseObject(scheme);
        // 解析options字段
        JSONArray options = schemeJson.getJSONObject("config").getJSONArray("options");
        for (int i = 0; i < options.size(); i++) {
            JSONObject option = options.getJSONObject(i);
            int value = option.getInteger("value");
            String label = option.getString("label");
            parseLabel.put(value, label);
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
    return parseLabel;
}
```

### 7.2 ExamConfig解析扩展
```java
// 扩展后的解析方法
public ExamConfig parseExamConfig(String scheme) {
    try {
        JSONObject schemeJson = JSON.parseObject(scheme);
        JSONObject examConfigJson = schemeJson.getJSONObject("examConfig");
        if (examConfigJson != null) {
            ExamConfig examConfig = new ExamConfig();
            examConfig.setScore(examConfigJson.getInteger("score"));
            examConfig.setAnswer(examConfigJson.getJSONArray("answer"));
            examConfig.setEnableScore(examConfigJson.getBoolean("enableScore"));
            examConfig.setScoringType(examConfigJson.getInteger("scoringType"));
            examConfig.setAnswerAnalysis(examConfigJson.getString("answerAnalysis"));
            return examConfig;
        }
    } catch (Exception e) {
        // 向后兼容：如果没有examConfig字段，返回null
        return null;
    }
    return null;
}
```

## 8. 控件类型与TDuck API映射

### 8.1 支持的题型映射表
| 控件类型 | TDuck typeId | 描述 | examConfig支持 |
|---------|-------------|------|----------------|
| INPUT | INPUT | 单行文本输入 | ✓ |
| TEXTAREA | TEXTAREA | 多行文本输入 | ✓ |
| NUMBER | NUMBER | 数字输入 | ✓ |
| RADIO | RADIO | 单选题 | ✓ |
| CHECKBOX | CHECKBOX | 多选题 | ✓ |
| SELECT | SELECT | 下拉选择 | ✓ |
| HORIZONTAL_INPUT | HORIZONTAL_INPUT | 横向填空 | ✓ |
| SIGN_PAD | SIGN_PAD | 手绘签名 | ✓ |

### 8.2 评分类型说明
```javascript
const SCORING_TYPES = {
  0: '不评分',
  1: '自动评分-单选',
  2: '自动评分-多选',
  5: '自动评分-横向填空'
};
```

## 9. 前端组件复用详细分析

### 9.1 Element UI组件使用统计
```javascript
// 表单组件
el-form, el-form-item, el-input, el-select, el-radio-group,
el-checkbox-group, el-date-picker, el-input-number, el-upload

// 布局组件
el-row, el-col, el-card, el-divider, el-container

// 数据展示组件
el-table, el-pagination, el-tag, el-badge, el-progress

// 反馈组件
el-dialog, el-message, el-loading, el-tooltip, el-popconfirm

// 导航组件
el-menu, el-breadcrumb, el-tabs, el-steps
```

### 9.2 可复用的业务组件模式
```javascript
// 1. 数据列表组件模式
const ListComponentMixin = {
  data() {
    return {
      list: [],
      loading: false,
      searchKeyword: '',
      pagination: { current: 1, size: 10, total: 0 }
    };
  },
  methods: {
    async loadData() { /* 通用加载逻辑 */ },
    handleSearch() { /* 通用搜索逻辑 */ },
    handlePageChange() { /* 通用分页逻辑 */ }
  }
};

// 2. 表单操作组件模式
const FormOperationMixin = {
  data() {
    return {
      dialogVisible: false,
      form: {},
      rules: {},
      loading: false
    };
  },
  methods: {
    showDialog() { /* 显示弹窗 */ },
    async saveForm() { /* 保存表单 */ },
    resetForm() { /* 重置表单 */ }
  }
};

// 3. 拖拽排序组件模式
const DragSortMixin = {
  methods: {
    async handleSort(draggedItem, targetItem, position) {
      const sortData = {
        formItemId: draggedItem.id,
        formKey: this.formId,
        beforePosition: position === 'before' ? targetItem.sort : null,
        afterPosition: position === 'after' ? targetItem.sort : null
      };
      await axios.post(`${API_BASE_URL}/S18M01B1/sort`, sortData);
      this.loadItems();
    }
  }
};
```

## 总结

现有架构具有良好的扩展性和复用性：
- **主子表模式**成熟稳定，支持复杂的表单结构
- **Scheme字段**设计灵活，可无缝集成examConfig
- **排序算法**完善，支持高效的拖拽操作
- **API设计**规范统一，便于扩展新功能
- **前端组件**模块化程度高，复用性强
- **解析机制**健壮，支持向后兼容的扩展

基于此架构扩展考试功能，可以实现最大化的代码复用和最小化的破坏性变更。
