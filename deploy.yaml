apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: svc-sa-table
  name: svc-sa-table
  namespace: inksoms   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: svc-sa-table
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: svc-sa-table
    spec:
      volumes:
        - name: volume-bfkecd
          configMap:
            name: svc-sa-table-yaml
            items:
              - key: application-prod.yml
                path: application-prod.yml
            defaultMode: 420
      imagePullSecrets:
        - name: aliyun-docker-hub  #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/svc-sa-table:SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: svc-sa-table
          ports:
            - containerPort: 8080
              protocol: TCP
          volumeMounts:
            - name: volume-bfkecd
              readOnly: true
              mountPath: /home/<USER>/application-prod.yml
              subPath: application-prod.yml    
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: svc-sa-table
  name: svc-sa-table
  namespace: inksoms
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
      nodePort: 30468
  selector:
    app: svc-sa-table
  sessionAffinity: None
  type: NodePort