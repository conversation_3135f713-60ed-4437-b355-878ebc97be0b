<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目管理 - 仓库管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .breadcrumb {
            margin-bottom: 10px;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .question-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #409EFF;
        }
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .question-type-tag {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        .type-radio { background: #E1F3D8; color: #67C23A; }
        .type-checkbox { background: #FDF6EC; color: #E6A23C; }
        .type-input { background: #F0F9FF; color: #409EFF; }
        .type-textarea { background: #F5F7FA; color: #909399; }
        .question-content {
            margin-bottom: 16px;
        }
        .question-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #303133;
        }
        .question-options {
            margin-left: 20px;
        }
        .option-item {
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
        .option-correct {
            background: #E1F3D8;
            color: #67C23A;
            font-weight: 500;
        }
        .question-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 16px;
            border-top: 1px solid #EBEEF5;
            font-size: 14px;
            color: #909399;
        }
        .score-info {
            font-weight: 600;
            color: #E6A23C;
        }
        .question-actions {
            display: flex;
            gap: 8px;
        }
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #409EFF; color: white; }
        .btn-warning { background: #E6A23C; color: white; }
        .btn-danger { background: #F56C6C; color: white; }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #909399;
        }
        .form-section {
            margin-bottom: 24px;
        }
        .form-section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #303133;
        }
        .option-input-group {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .option-input {
            flex: 1;
            margin-right: 12px;
        }

        /* 拖拽控件库样式 */
        .drag-builder-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .widget-library {
            width: 250px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }

        .widget-library-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #303133;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 8px;
        }

        .widget-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: grab;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .widget-item:hover {
            background: #e3f2fd;
            border-color: #409EFF;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }

        .widget-item:active {
            cursor: grabbing;
        }

        .widget-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: #409EFF;
        }

        .widget-name {
            font-weight: 500;
            color: #303133;
        }

        .drag-area {
            flex: 1;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            min-height: 500px;
            position: relative;
        }

        .drag-area-header {
            padding: 20px;
            border-bottom: 1px solid #EBEEF5;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }

        .drag-area-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin: 0;
        }

        .drag-drop-zone {
            padding: 20px;
            min-height: 400px;
            position: relative;
        }

        .drop-placeholder {
            text-align: center;
            padding: 60px 20px;
            color: #909399;
            border: 2px dashed #DCDFE6;
            border-radius: 8px;
            background: #fafafa;
        }

        .drop-placeholder.drag-over {
            border-color: #409EFF;
            background: #f0f9ff;
            color: #409EFF;
        }

        .question-builder-item {
            background: white;
            border: 1px solid #EBEEF5;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            position: relative;
            transition: all 0.3s ease;
        }

        .question-builder-item:hover {
            border-color: #409EFF;
            box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
        }

        .question-builder-item.selected {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .question-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .question-type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            background: #409EFF;
            color: white;
        }

        .question-item-actions {
            display: flex;
            gap: 8px;
        }

        .drag-handle {
            cursor: move;
            color: #909399;
            font-size: 16px;
        }

        .drag-handle:hover {
            color: #409EFF;
        }

        .config-panel {
            width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }

        .config-panel-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #303133;
            border-bottom: 2px solid #67C23A;
            padding-bottom: 8px;
        }

        .sortable-ghost {
            opacity: 0.5;
        }

        .sortable-chosen {
            transform: rotate(5deg);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <div class="breadcrumb">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><a href="question-bank-management-v2.html">题库管理</a></el-breadcrumb-item>
                    <el-breadcrumb-item>{{ bankInfo.name || '题目管理' }}</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
            <h1>题目管理</h1>
            <p v-if="bankInfo.description">{{ bankInfo.description }}</p>
        </div>

        <div class="container">
            <!-- 工具栏 -->
            <div class="toolbar">
                <el-row :gutter="20" type="flex" justify="space-between" align="middle">
                    <el-col :span="12">
                        <el-radio-group v-model="viewMode" @change="handleViewModeChange">
                            <el-radio-button label="list">题目列表</el-radio-button>
                            <el-radio-button label="builder">拖拽创建</el-radio-button>
                        </el-radio-group>
                    </el-col>
                    <el-col :span="12" style="text-align: right;">
                        <el-input
                            v-if="viewMode === 'list'"
                            v-model="searchKeyword"
                            placeholder="搜索题目内容"
                            prefix-icon="el-icon-search"
                            style="width: 200px; margin-right: 10px;"
                            @input="handleSearch">
                        </el-input>
                        <el-button v-if="viewMode === 'list'" type="primary" icon="el-icon-plus" @click="showCreateDialog">
                            添加题目
                        </el-button>
                        <el-button v-if="viewMode === 'builder'" type="success" icon="el-icon-check" @click="saveAllQuestions">
                            保存题目
                        </el-button>
                        <el-button type="info" icon="el-icon-upload">
                            批量导入
                        </el-button>
                    </el-col>
                </el-row>
            </div>

            <!-- 拖拽创建模式 -->
            <div v-if="viewMode === 'builder'" class="drag-builder-container">
                <!-- 控件库 -->
                <div class="widget-library">
                    <div class="widget-library-title">
                        <i class="el-icon-menu"></i> 题目控件库
                    </div>
                    <div
                        v-for="widget in widgetLibrary"
                        :key="widget.type"
                        class="widget-item"
                        draggable="true"
                        @dragstart="handleWidgetDragStart($event, widget)"
                        @dragend="handleWidgetDragEnd">
                        <i :class="widget.icon" class="widget-icon"></i>
                        <span class="widget-name">{{ widget.name }}</span>
                    </div>
                </div>

                <!-- 拖拽区域 -->
                <div class="drag-area">
                    <div class="drag-area-header">
                        <h3 class="drag-area-title">题目构建区域</h3>
                        <p style="margin: 8px 0 0 0; color: #909399; font-size: 14px;">
                            从左侧拖拽控件到此区域创建题目
                        </p>
                    </div>
                    <div
                        class="drag-drop-zone"
                        @dragover="handleDragOver"
                        @drop="handleDrop"
                        @dragenter="handleDragEnter"
                        @dragleave="handleDragLeave">

                        <!-- 空状态提示 -->
                        <div v-if="builderQuestions.length === 0" class="drop-placeholder" :class="{ 'drag-over': isDragOver }">
                            <i class="el-icon-upload" style="font-size: 48px; margin-bottom: 16px;"></i>
                            <p style="font-size: 16px; margin: 0;">拖拽控件到此处开始创建题目</p>
                            <p style="font-size: 14px; margin: 8px 0 0 0; color: #C0C4CC;">支持单选、多选、填空、问答等多种题型</p>
                        </div>

                        <!-- 题目构建列表 -->
                        <div v-else id="question-builder-list">
                            <div
                                v-for="(question, index) in builderQuestions"
                                :key="question.id"
                                class="question-builder-item"
                                :class="{ selected: selectedQuestionId === question.id }"
                                @click="selectQuestion(question.id)">

                                <div class="question-item-header">
                                    <span class="question-type-badge">{{ getTypeText(question.type) }}</span>
                                    <div class="question-item-actions">
                                        <i class="el-icon-rank drag-handle"></i>
                                        <el-button type="text" icon="el-icon-edit" @click.stop="editBuilderQuestion(question)"></el-button>
                                        <el-button type="text" icon="el-icon-delete" @click.stop="deleteBuilderQuestion(index)"></el-button>
                                    </div>
                                </div>

                                <div class="question-content">
                                    <div class="question-title">{{ question.label || '请设置题目内容' }}</div>
                                    <div v-if="question.options && question.options.length > 0" class="question-options">
                                        <div v-for="option in question.options" :key="option.value" class="option-item">
                                            {{ option.label || '选项' + option.value }}
                                        </div>
                                    </div>
                                </div>

                                <div class="question-meta">
                                    <span>题目类型: {{ getTypeText(question.type) }}</span>
                                    <span class="score-info">分值: {{ question.examConfig?.score || 10 }}分</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置面板 -->
                <div class="config-panel">
                    <div class="config-panel-title">
                        <i class="el-icon-setting"></i> 题目配置
                    </div>
                    <div v-if="selectedQuestion">
                        <el-form :model="selectedQuestion" label-width="80px" size="small">
                            <el-form-item label="题目类型">
                                <el-select v-model="selectedQuestion.type" @change="handleTypeChange">
                                    <el-option label="单选题" value="RADIO"></el-option>
                                    <el-option label="多选题" value="CHECKBOX"></el-option>
                                    <el-option label="填空题" value="INPUT"></el-option>
                                    <el-option label="问答题" value="TEXTAREA"></el-option>
                                    <el-option label="数字题" value="NUMBER"></el-option>
                                    <el-option label="下拉选择题" value="SELECT"></el-option>
                                    <el-option label="横向填空题" value="HORIZONTAL_INPUT"></el-option>
                                    <el-option label="绘图题" value="SIGN_PAD"></el-option>
                                </el-select>
                            </el-form-item>

                            <el-form-item label="题目内容">
                                <el-input
                                    type="textarea"
                                    v-model="selectedQuestion.label"
                                    :rows="3"
                                    placeholder="请输入题目内容">
                                </el-input>
                            </el-form-item>

                            <!-- 选项配置 -->
                            <div v-if="needOptionsForType(selectedQuestion.type)">
                                <el-form-item label="选项配置">
                                    <div v-for="(option, index) in selectedQuestion.options" :key="index" class="option-input-group">
                                        <el-input
                                            v-model="option.label"
                                            :placeholder="'选项' + (index + 1)"
                                            class="option-input">
                                        </el-input>
                                        <el-checkbox
                                            v-if="selectedQuestion.type === 'CHECKBOX'"
                                            v-model="option.isCorrect">
                                            正确
                                        </el-checkbox>
                                        <el-radio
                                            v-if="selectedQuestion.type === 'RADIO'"
                                            v-model="selectedQuestion.correctAnswer"
                                            :label="option.value">
                                            正确
                                        </el-radio>
                                        <el-button
                                            type="text"
                                            icon="el-icon-delete"
                                            @click="removeOption(index)">
                                        </el-button>
                                    </div>
                                    <el-button type="text" icon="el-icon-plus" @click="addOption">添加选项</el-button>
                                </el-form-item>
                            </div>

                            <!-- 考试配置 -->
                            <el-form-item label="分值">
                                <el-input-number
                                    v-model="selectedQuestion.examConfig.score"
                                    :min="1"
                                    :max="100">
                                </el-input-number>
                            </el-form-item>

                            <el-form-item label="评分类型">
                                <el-select v-model="selectedQuestion.examConfig.scoringType">
                                    <el-option label="自动评分" :value="1"></el-option>
                                    <el-option label="手动评分" :value="2"></el-option>
                                    <el-option label="不评分" :value="0"></el-option>
                                </el-select>
                            </el-form-item>

                            <el-form-item label="答案解析">
                                <el-input
                                    type="textarea"
                                    v-model="selectedQuestion.examConfig.answerAnalysis"
                                    :rows="2"
                                    placeholder="请输入答案解析">
                                </el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div v-else style="text-align: center; padding: 40px 20px; color: #909399;">
                        <i class="el-icon-info" style="font-size: 32px; margin-bottom: 12px;"></i>
                        <p>请选择一个题目进行配置</p>
                    </div>
                </div>
            </div>

            <!-- 题目列表 -->
            <div v-if="questions.length > 0">
                <div v-for="(question, index) in filteredQuestions" :key="question.id" class="question-card">
                    <div class="question-header">
                        <span class="question-type-tag" :class="getTypeClass(question.itemtype)">
                            {{ getTypeText(question.itemtype) }}
                        </span>
                        <div class="question-actions">
                            <button class="btn btn-primary" @click="previewQuestion(question)">预览</button>
                            <button class="btn btn-warning" @click="editQuestion(question)">编辑</button>
                            <button class="btn btn-danger" @click="deleteQuestion(question)">删除</button>
                        </div>
                    </div>
                    
                    <div class="question-content">
                        <div class="question-title" v-html="question.label"></div>
                        <div v-if="question.options && question.options.length > 0" class="question-options">
                            <div v-for="option in question.options" :key="option.value" 
                                 class="option-item" :class="{ 'option-correct': isCorrectOption(question, option.value) }">
                                <span>{{ option.label }}</span>
                                <span v-if="isCorrectOption(question, option.value)" style="margin-left: 8px;">✓</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="question-meta">
                        <span>创建时间: {{ formatDate(question.createdate) }}</span>
                        <span class="score-info">分值: {{ getQuestionScore(question) }}分</span>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-state">
                <div style="font-size: 64px; margin-bottom: 16px;">📝</div>
                <h3>暂无题目</h3>
                <p>点击"添加题目"开始创建题目</p>
                <el-button type="primary" @click="showCreateDialog">添加题目</el-button>
            </div>
        </div>

        <!-- 创建/编辑题目对话框 -->
        <el-dialog :title="isEditing ? '编辑题目' : '创建题目'" :visible.sync="questionDialogVisible" width="800px">
            <el-form :model="questionForm" :rules="questionRules" ref="questionForm" label-width="100px">
                <div class="form-section">
                    <div class="form-section-title">基本信息</div>
                    <el-form-item label="题目类型" prop="itemtype">
                        <el-select v-model="questionForm.itemtype" @change="handleTypeChange" style="width: 100%;">
                            <el-option label="单选题" value="RADIO"></el-option>
                            <el-option label="多选题" value="CHECKBOX"></el-option>
                            <el-option label="填空题" value="INPUT"></el-option>
                            <el-option label="简答题" value="TEXTAREA"></el-option>
                            <el-option label="数字题" value="NUMBER"></el-option>
                            <el-option label="下拉选择题" value="SELECT"></el-option>
                            <el-option label="横向填空题" value="HORIZONTAL_INPUT"></el-option>
                            <el-option label="绘图题" value="SIGN_PAD"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="题目内容" prop="label">
                        <el-input
                            type="textarea"
                            v-model="questionForm.label"
                            placeholder="请输入题目内容"
                            :rows="3">
                        </el-input>
                    </el-form-item>
                </div>

                <div v-if="needOptions" class="form-section">
                    <div class="form-section-title">选项设置</div>
                    <div v-for="(option, index) in questionForm.options" :key="index" class="option-input-group">
                        <el-input v-model="option.label" :placeholder="`选项 ${String.fromCharCode(65 + index)}`" class="option-input"></el-input>
                        <el-checkbox v-model="option.isCorrect" :disabled="isSingleChoice && hasCorrectOption && !option.isCorrect">
                            正确答案
                        </el-checkbox>
                        <el-button type="danger" icon="el-icon-delete" size="mini" @click="removeOption(index)" :disabled="questionForm.options.length <= 2"></el-button>
                    </div>
                    <el-button type="primary" icon="el-icon-plus" size="small" @click="addOption" :disabled="questionForm.options.length >= 8">
                        添加选项
                    </el-button>
                </div>

                <div class="form-section">
                    <div class="form-section-title">评分设置</div>
                    <el-form-item label="分值" prop="score">
                        <el-input-number v-model="questionForm.score" :min="1" :max="100" style="width: 200px;"></el-input-number>
                    </el-form-item>
                    <el-form-item label="评分类型" prop="scoringType">
                        <el-select v-model="questionForm.scoringType" style="width: 200px;">
                            <el-option label="自动评分" :value="1" v-if="isObjectiveType"></el-option>
                            <el-option label="人工评分" :value="2" v-if="isSubjectiveType"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="答案解析">
                        <el-input
                            type="textarea"
                            v-model="questionForm.answerAnalysis"
                            placeholder="请输入答案解析（可选）"
                            :rows="2">
                        </el-input>
                    </el-form-item>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="questionDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveQuestion" :loading="saving">保存</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        // Add a request interceptor to set the Authorization header
        axios.interceptors.request.use(config => {
            config.headers.Authorization = 'admin';
            return config;
        }, error => {
            return Promise.reject(error);
        });

        const API_BASE_URL = 'http://*************:10668';

        new Vue({
            el: '#app',
            data() {
                return {
                    bankId: '',
                    bankInfo: {},
                    questions: [],
                    searchKeyword: '',
                    questionDialogVisible: false,
                    isEditing: false,
                    saving: false,

                    // 视图模式：list(列表模式) 或 builder(拖拽创建模式)
                    viewMode: 'list',

                    // 拖拽相关数据
                    isDragOver: false,
                    builderQuestions: [],
                    selectedQuestionId: null,
                    draggedWidget: null,
                    questionIdCounter: 1,

                    // 控件库定义
                    widgetLibrary: [
                        { type: 'RADIO', name: '单选题', icon: 'el-icon-circle-check' },
                        { type: 'CHECKBOX', name: '多选题', icon: 'el-icon-check' },
                        { type: 'INPUT', name: '填空题', icon: 'el-icon-edit-outline' },
                        { type: 'TEXTAREA', name: '问答题', icon: 'el-icon-document' },
                        { type: 'NUMBER', name: '数字题', icon: 'el-icon-s-data' },
                        // 新增3种TDuck兼容控件
                        { type: 'SELECT', name: '下拉选择题', icon: 'el-icon-arrow-down' },
                        { type: 'HORIZONTAL_INPUT', name: '横向填空题', icon: 'el-icon-minus' },
                        { type: 'SIGN_PAD', name: '绘图题', icon: 'el-icon-edit' }
                    ],

                    questionForm: {
                        itemtype: 'RADIO',
                        label: '',
                        options: [
                            { label: '', value: 1, isCorrect: false },
                            { label: '', value: 2, isCorrect: false }
                        ],
                        score: 10,
                        scoringType: 1,
                        answerAnalysis: ''
                    },
                    questionRules: {
                        itemtype: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
                        label: [{ required: true, message: '请输入题目内容', trigger: 'blur' }],
                        score: [{ required: true, message: '请设置分值', trigger: 'blur' }]
                    }
                }
            },
            computed: {
                filteredQuestions() {
                    if (!this.searchKeyword) return this.questions;
                    return this.questions.filter(q =>
                        q.label.toLowerCase().includes(this.searchKeyword.toLowerCase())
                    );
                },
                needOptions() {
                    return ['RADIO', 'CHECKBOX', 'SELECT'].includes(this.questionForm.itemtype);
                },
                isSingleChoice() {
                    return this.questionForm.itemtype === 'RADIO';
                },
                hasCorrectOption() {
                    return this.questionForm.options.some(opt => opt.isCorrect);
                },
                isObjectiveType() {
                    return ['RADIO', 'CHECKBOX', 'NUMBER'].includes(this.questionForm.itemtype);
                },
                isSubjectiveType() {
                    return ['INPUT', 'TEXTAREA'].includes(this.questionForm.itemtype);
                },
                // 拖拽模式相关计算属性
                selectedQuestion() {
                    return this.builderQuestions.find(q => q.id === this.selectedQuestionId);
                }
            },
            mounted() {
                this.bankId = new URLSearchParams(window.location.search).get('bankId');
                if (this.bankId) {
                    this.loadBankInfo();
                    this.loadQuestions();
                }

                // 初始化拖拽排序
                this.$nextTick(() => {
                    this.initSortable();
                });
            },
            methods: {
                async loadBankInfo() {
                    try {
                        const response = await axios.get(`${API_BASE_URL}/S18M10B1/getBillEntity?key=${this.bankId}`);
                        if (response.data.code === 200) {
                            this.bankInfo = response.data.data;
                        }
                    } catch (error) {
                        console.error('加载题库信息失败', error);
                    }
                },
                
                async loadQuestions() {
                    try {
                        const response = await axios.get(`${API_BASE_URL}/S18M10B1/getBillEntity?key=${this.bankId}`);
                        if (response.data.code === 200 && response.data.data.item) {
                            this.questions = response.data.data.item || [];
                            this.parseQuestions();
                        }
                    } catch (error) {
                        this.$message.error('加载题目列表失败');
                        console.error(error);
                    }
                },
                
                parseQuestions() {
                    this.questions.forEach(question => {
                        try {
                            const scheme = JSON.parse(question.scheme);
                            question.options = scheme.config?.options || [];
                            question.examConfig = scheme.examConfig || {};
                        } catch (e) {
                            question.options = [];
                            question.examConfig = {};
                        }
                    });
                },
                
                showCreateDialog() {
                    this.isEditing = false;
                    this.resetQuestionForm();
                    this.questionDialogVisible = true;
                },
                
                resetQuestionForm() {
                    this.questionForm = {
                        itemtype: 'RADIO',
                        label: '',
                        options: [
                            { label: '', value: 1, isCorrect: false },
                            { label: '', value: 2, isCorrect: false }
                        ],
                        score: 10,
                        scoringType: 1,
                        answerAnalysis: ''
                    };
                },
                
                handleTypeChange() {
                    if (this.isObjectiveType) {
                        this.questionForm.scoringType = 1;
                    } else if (this.isSubjectiveType) {
                        this.questionForm.scoringType = 2;
                    }
                },
                
                addOption() {
                    const nextValue = this.questionForm.options.length + 1;
                    this.questionForm.options.push({
                        label: '',
                        value: nextValue,
                        isCorrect: false
                    });
                },
                
                removeOption(index) {
                    this.questionForm.options.splice(index, 1);
                    // 重新分配value
                    this.questionForm.options.forEach((opt, i) => {
                        opt.value = i + 1;
                    });
                },
                
                async saveQuestion() {
                    try {
                        await this.$refs.questionForm.validate();
                        this.saving = true;
                        
                        const questionData = this.buildQuestionData();
                        const url = this.isEditing ? `${API_BASE_URL}/S18M10B1/updateItem` : `${API_BASE_URL}/S18M10B1/createItem`;
                        
                        const response = await axios.post(url, JSON.stringify(questionData), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        if (response.data.code === 200) {
                            this.$message.success(this.isEditing ? '更新成功' : '创建成功');
                            this.questionDialogVisible = false;
                            this.loadQuestions();
                        } else {
                            this.$message.error(response.data.msg || '保存失败');
                        }
                    } catch (error) {
                        this.$message.error('保存题目失败');
                        console.error(error);
                    } finally {
                        this.saving = false;
                    }
                },
                
                buildQuestionData() {
                    const scheme = {
                        typeId: this.questionForm.itemtype,
                        config: {
                            label: this.questionForm.label,
                            options: this.needOptions ? this.questionForm.options : undefined
                        },
                        examConfig: {
                            scoringType: this.questionForm.scoringType,
                            score: this.questionForm.score,
                            enableScore: true,
                            answer: this.getCorrectAnswer(),
                            answerAnalysis: this.questionForm.answerAnalysis
                        }
                    };
                    
                    return {
                        pid: this.bankId,  // 关键：使用pid字段关联主表
                        itemtype: this.questionForm.itemtype,
                        label: this.questionForm.label,
                        scheme: JSON.stringify(scheme)
                    };
                },
                
                getCorrectAnswer() {
                    if (this.questionForm.itemtype === 'RADIO') {
                        const correct = this.questionForm.options.find(opt => opt.isCorrect);
                        return correct ? [correct.value] : [];
                    } else if (this.questionForm.itemtype === 'CHECKBOX') {
                        return this.questionForm.options.filter(opt => opt.isCorrect).map(opt => opt.value);
                    }
                    return null;
                },
                
                getTypeText(type) {
                    const types = {
                        'RADIO': '单选题',
                        'CHECKBOX': '多选题',
                        'INPUT': '填空题',
                        'TEXTAREA': '简答题',
                        'NUMBER': '数字题',
                        'SELECT': '下拉选择题',
                        'HORIZONTAL_INPUT': '横向填空题',
                        'SIGN_PAD': '绘图题'
                    };
                    return types[type] || type;
                },
                
                getTypeClass(type) {
                    const classes = {
                        'RADIO': 'type-radio',
                        'CHECKBOX': 'type-checkbox',
                        'INPUT': 'type-input',
                        'TEXTAREA': 'type-textarea',
                        'NUMBER': 'type-input',
                        'SELECT': 'type-select',
                        'HORIZONTAL_INPUT': 'type-horizontal-input',
                        'SIGN_PAD': 'type-sign-pad'
                    };
                    return classes[type] || '';
                },
                
                isCorrectOption(question, value) {
                    const answer = question.examConfig?.answer;
                    if (Array.isArray(answer)) {
                        return answer.includes(value);
                    }
                    return false;
                },
                
                getQuestionScore(question) {
                    return question.examConfig?.score || 0;
                },
                
                formatDate(dateStr) {
                    if (!dateStr) return '-';
                    return new Date(dateStr).toLocaleDateString();
                },
                
                previewQuestion(question) {
                    this.$message.info('预览功能开发中...');
                },
                
                editQuestion(question) {
                    this.$message.info('编辑功能开发中...');
                },
                
                async deleteQuestion(question) {
                    try {
                        await this.$confirm('确定要删除这个题目吗？', '确认删除', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        
                        const response = await axios.get(`${API_BASE_URL}/S18M10B1/deleteItem?key=${question.id}`);
                        if (response.data.code === 200) {
                            this.$message.success('删除成功');
                            this.loadQuestions();
                        } else {
                            this.$message.error(response.data.msg || '删除失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('删除失败');
                            console.error(error);
                        }
                    }
                },
                
                handleSearch() {
                    // 搜索逻辑已在computed中实现
                },

                // ========== 拖拽创建模式相关方法 ==========

                handleViewModeChange(mode) {
                    if (mode === 'builder') {
                        this.initBuilderMode();
                    }
                },

                initBuilderMode() {
                    // 初始化拖拽模式
                    this.builderQuestions = [];
                    this.selectedQuestionId = null;
                    this.questionIdCounter = 1;
                },

                // 控件拖拽开始
                handleWidgetDragStart(event, widget) {
                    this.draggedWidget = widget;
                    event.dataTransfer.effectAllowed = 'copy';
                    event.dataTransfer.setData('text/plain', JSON.stringify(widget));
                },

                handleWidgetDragEnd() {
                    this.draggedWidget = null;
                },

                // 拖拽区域事件
                handleDragOver(event) {
                    event.preventDefault();
                    event.dataTransfer.dropEffect = 'copy';
                },

                handleDragEnter(event) {
                    event.preventDefault();
                    this.isDragOver = true;
                },

                handleDragLeave(event) {
                    // 只有当离开整个拖拽区域时才设置为false
                    if (!event.currentTarget.contains(event.relatedTarget)) {
                        this.isDragOver = false;
                    }
                },

                handleDrop(event) {
                    event.preventDefault();
                    this.isDragOver = false;

                    try {
                        const widgetData = JSON.parse(event.dataTransfer.getData('text/plain'));
                        this.createQuestionFromWidget(widgetData);
                    } catch (error) {
                        console.error('拖拽数据解析失败', error);
                    }
                },

                // 从控件创建题目
                createQuestionFromWidget(widget) {
                    const questionId = `question_${this.questionIdCounter++}`;
                    const newQuestion = {
                        id: questionId,
                        type: widget.type,
                        label: '',
                        options: this.needOptionsForType(widget.type) ? [
                            { label: '', value: 1, isCorrect: false },
                            { label: '', value: 2, isCorrect: false }
                        ] : [],
                        examConfig: {
                            score: 10,
                            enableScore: true,
                            scoringType: this.getDefaultScoringType(widget.type),
                            answerAnalysis: '',
                            showAnswer: false
                        },
                        correctAnswer: null
                    };

                    this.builderQuestions.push(newQuestion);
                    this.selectQuestion(questionId);

                    this.$message.success(`已添加${widget.name}`);
                },

                // 选择题目进行配置
                selectQuestion(questionId) {
                    this.selectedQuestionId = questionId;
                },

                // 删除构建中的题目
                deleteBuilderQuestion(index) {
                    const question = this.builderQuestions[index];
                    this.builderQuestions.splice(index, 1);

                    // 如果删除的是当前选中的题目，清除选中状态
                    if (this.selectedQuestionId === question.id) {
                        this.selectedQuestionId = null;
                    }

                    this.$message.success('题目已删除');
                },

                // 编辑构建中的题目
                editBuilderQuestion(question) {
                    this.selectQuestion(question.id);
                },

                // 判断题目类型是否需要选项
                needOptionsForType(type) {
                    return ['RADIO', 'CHECKBOX', 'SELECT'].includes(type);
                },

                // 获取默认评分类型
                getDefaultScoringType(type) {
                    switch (type) {
                        case 'RADIO':
                        case 'CHECKBOX':
                        case 'NUMBER':
                            return 1; // 自动评分
                        case 'INPUT':
                        case 'TEXTAREA':
                            return 2; // 手动评分
                        default:
                            return 1;
                    }
                },

                // 题目类型变更处理
                handleTypeChange() {
                    if (this.selectedQuestion) {
                        // 重置选项
                        if (this.needOptionsForType(this.selectedQuestion.type)) {
                            this.selectedQuestion.options = [
                                { label: '', value: 1, isCorrect: false },
                                { label: '', value: 2, isCorrect: false }
                            ];
                        } else {
                            this.selectedQuestion.options = [];
                        }

                        // 重置评分类型
                        this.selectedQuestion.examConfig.scoringType = this.getDefaultScoringType(this.selectedQuestion.type);
                        this.selectedQuestion.correctAnswer = null;
                    }
                },

                // 添加选项
                addOption() {
                    if (this.selectedQuestion && this.selectedQuestion.options) {
                        const newValue = this.selectedQuestion.options.length + 1;
                        this.selectedQuestion.options.push({
                            label: '',
                            value: newValue,
                            isCorrect: false
                        });
                    }
                },

                // 删除选项
                removeOption(index) {
                    if (this.selectedQuestion && this.selectedQuestion.options && this.selectedQuestion.options.length > 2) {
                        this.selectedQuestion.options.splice(index, 1);
                        // 重新编号
                        this.selectedQuestion.options.forEach((option, idx) => {
                            option.value = idx + 1;
                        });
                    }
                },

                // 保存所有题目
                async saveAllQuestions() {
                    if (this.builderQuestions.length === 0) {
                        this.$message.warning('请先添加题目');
                        return;
                    }

                    // 验证题目完整性
                    for (let question of this.builderQuestions) {
                        if (!question.label.trim()) {
                            this.$message.error('请完善所有题目的内容');
                            return;
                        }

                        if (this.needOptionsForType(question.type)) {
                            const hasValidOptions = question.options.some(opt => opt.label.trim());
                            if (!hasValidOptions) {
                                this.$message.error('请完善选择题的选项');
                                return;
                            }
                        }
                    }

                    try {
                        this.saving = true;

                        // 构建保存数据
                        const saveData = {
                            id: this.bankId,
                            item: this.builderQuestions.map(question => this.buildQuestionSaveData(question))
                        };

                        const response = await axios.post(`${API_BASE_URL}/S18M10B1/update`, saveData);

                        if (response.data.code === 200) {
                            this.$message.success('题目保存成功');
                            this.loadQuestions(); // 重新加载题目列表
                            this.viewMode = 'list'; // 切换回列表模式
                        } else {
                            this.$message.error(response.data.msg || '保存失败');
                        }
                    } catch (error) {
                        this.$message.error('保存失败');
                        console.error(error);
                    } finally {
                        this.saving = false;
                    }
                },

                // 构建题目保存数据
                buildQuestionSaveData(question) {
                    const scheme = {
                        typeId: question.type,
                        config: {
                            tag: this.getTagByType(question.type),
                            label: question.label,
                            options: question.options.length > 0 ? question.options : undefined
                        },
                        examConfig: {
                            score: question.examConfig.score,
                            enableScore: question.examConfig.enableScore,
                            scoringType: question.examConfig.scoringType,
                            answerAnalysis: question.examConfig.answerAnalysis,
                            showAnswer: question.examConfig.showAnswer,
                            answer: this.getCorrectAnswer(question)
                        }
                    };

                    return {
                        pid: this.bankId,
                        itemtype: question.type,
                        label: question.label,
                        scheme: JSON.stringify(scheme)
                    };
                },

                // 根据题目类型获取标签
                getTagByType(type) {
                    const tagMap = {
                        'RADIO': 'el-radio-group',
                        'CHECKBOX': 'el-checkbox-group',
                        'INPUT': 'el-input',
                        'TEXTAREA': 'el-input',
                        'NUMBER': 'el-input-number'
                    };
                    return tagMap[type] || 'el-input';
                },

                // 获取正确答案
                getCorrectAnswer(question) {
                    if (question.type === 'RADIO') {
                        return question.correctAnswer ? [question.correctAnswer] : [];
                    } else if (question.type === 'CHECKBOX') {
                        return question.options.filter(opt => opt.isCorrect).map(opt => opt.value);
                    }
                    return [];
                },

                // 初始化拖拽排序
                initSortable() {
                    this.$nextTick(() => {
                        const questionBuilderList = document.getElementById('question-builder-list');
                        if (questionBuilderList && typeof Sortable !== 'undefined') {
                            new Sortable(questionBuilderList, {
                                animation: 150,
                                ghostClass: 'sortable-ghost',
                                chosenClass: 'sortable-chosen',
                                handle: '.drag-handle',
                                onEnd: (evt) => {
                                    // 更新数组顺序
                                    const oldIndex = evt.oldIndex;
                                    const newIndex = evt.newIndex;

                                    if (oldIndex !== newIndex) {
                                        const movedItem = this.builderQuestions.splice(oldIndex, 1)[0];
                                        this.builderQuestions.splice(newIndex, 0, movedItem);
                                        this.$message.success('题目顺序已调整');
                                    }
                                }
                            });
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>
