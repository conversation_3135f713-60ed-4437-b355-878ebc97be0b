<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布考试 - 仓库管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .form-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #303133;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 8px;
        }
        .exam-info {
            background: #F0F9FF;
            border: 1px solid #409EFF;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #E1F3D8;
        }
        .info-label {
            font-weight: 600;
            color: #606266;
        }
        .info-value {
            color: #303133;
        }
        .publish-settings {
            background: #FAFAFA;
            border-radius: 8px;
            padding: 20px;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-draft {
            background: #F5F7FA;
            color: #909399;
        }
        .status-published {
            background: #E1F3D8;
            color: #67C23A;
        }
        .status-stopped {
            background: #FEF0F0;
            color: #F56C6C;
        }
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 8px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        .btn-success {
            background: #67C23A;
            color: white;
        }
        .btn-warning {
            background: #E6A23C;
            color: white;
        }
        .btn-danger {
            background: #F56C6C;
            color: white;
        }
        .btn-default {
            background: #F5F7FA;
            color: #606266;
            border: 1px solid #DCDFE6;
        }
        .share-link {
            background: #F0F9FF;
            border: 1px solid #409EFF;
            border-radius: 6px;
            padding: 12px;
            margin-top: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .link-text {
            font-family: monospace;
            color: #409EFF;
            flex: 1;
            margin-right: 12px;
        }
        .qr-code {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><a href="question-bank-management-v2.html">题库管理</a></el-breadcrumb-item>
                <el-breadcrumb-item>发布考试</el-breadcrumb-item>
            </el-breadcrumb>
            <h1>发布考试</h1>
            <p>设置考试发布参数并生成考试链接</p>
        </div>

        <div class="container">
            <!-- 考试信息 -->
            <div class="form-card">
                <div class="section-title">考试信息</div>
                <div class="exam-info">
                    <div class="info-item">
                        <span class="info-label">考试名称：</span>
                        <span class="info-value">{{ examInfo.name || '-' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">考试说明：</span>
                        <span class="info-value">{{ examInfo.description || '无' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">考试时长：</span>
                        <span class="info-value">{{ examInfo.custom1 || 60 }}分钟</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">及格分数：</span>
                        <span class="info-value">{{ examInfo.custom2 || 60 }}分</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">题目数量：</span>
                        <span class="info-value">{{ questionCount }}题</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">总分：</span>
                        <span class="info-value">{{ totalScore }}分</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">当前状态：</span>
                        <span class="status-badge" :class="getStatusClass(examInfo.status)">
                            {{ getStatusText(examInfo.status) }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- 发布设置 -->
            <div class="form-card">
                <div class="section-title">发布设置</div>
                <div class="publish-settings">
                    <el-form :model="publishForm" :rules="publishRules" ref="publishForm" label-width="120px">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="开始时间">
                                    <el-date-picker
                                        v-model="publishForm.startTime"
                                        type="datetime"
                                        placeholder="选择开始时间"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="结束时间">
                                    <el-date-picker
                                        v-model="publishForm.endTime"
                                        type="datetime"
                                        placeholder="选择结束时间"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="限制IP">
                                    <el-switch v-model="publishForm.limitIp"></el-switch>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="需要登录">
                                    <el-switch v-model="publishForm.requireLogin"></el-switch>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="显示结果">
                                    <el-switch v-model="publishForm.showResult"></el-switch>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item label="允许参与人数">
                            <el-input-number v-model="publishForm.maxParticipants" :min="0" :max="10000" style="width: 200px;">
                                <template slot="append">人</template>
                            </el-input-number>
                            <span style="margin-left: 12px; color: #909399;">0表示不限制</span>
                        </el-form-item>
                        <el-form-item label="发布说明">
                            <el-input
                                type="textarea"
                                v-model="publishForm.publishNote"
                                placeholder="请输入发布说明（可选）"
                                :rows="3">
                            </el-input>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 考试链接 -->
            <div class="form-card" v-if="examInfo.status === '2'">
                <div class="section-title">考试链接</div>
                <div class="share-link">
                    <span class="link-text">{{ examLink }}</span>
                    <el-button type="primary" size="small" @click="copyLink">复制链接</el-button>
                </div>
                <div class="qr-code">
                    <div id="qrcode"></div>
                    <p style="color: #909399; margin-top: 12px;">扫描二维码参加考试</p>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn btn-default" @click="goBack">返回</button>
                <button v-if="examInfo.status === '1'" class="btn btn-success" @click="publishExam" :loading="publishing">
                    发布考试
                </button>
                <button v-if="examInfo.status === '2'" class="btn btn-warning" @click="updateSettings" :loading="updating">
                    更新设置
                </button>
                <button v-if="examInfo.status === '2'" class="btn btn-danger" @click="stopExam">
                    停止考试
                </button>
                <button class="btn btn-primary" @click="viewResults">
                    查看结果
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // Add a request interceptor to set the Authorization header
        axios.interceptors.request.use(config => {
            config.headers.Authorization = 'admin';
            return config;
        }, error => {
            return Promise.reject(error);
        });

        const API_BASE_URL = 'http://*************:10668';

        new Vue({
            el: '#app',
            data() {
                return {
                    formId: '',
                    examInfo: {},
                    questionCount: 0,
                    totalScore: 0,
                    publishing: false,
                    updating: false,
                    publishForm: {
                        startTime: null,
                        endTime: null,
                        limitIp: false,
                        requireLogin: false,
                        showResult: true,
                        maxParticipants: 0,
                        publishNote: ''
                    },
                    publishRules: {}
                }
            },
            computed: {
                examLink() {
                    return `${window.location.origin}/exam/take-exam-v2.html?formId=${this.formId}`;
                }
            },
            mounted() {
                this.formId = new URLSearchParams(window.location.search).get('formId');
                if (this.formId) {
                    this.loadExamInfo();
                }
            },
            methods: {
                async loadExamInfo() {
                    try {
                        const response = await axios.get(`${API_BASE_URL}/S18M01B1/getBillEntity?key=${this.formId}`);
                        if (response.data.code === 200) {
                            this.examInfo = response.data.data;
                            this.calculateExamStats();
                            if (this.examInfo.status === '2') {
                                this.generateQRCode();
                            }
                        }
                    } catch (error) {
                        this.$message.error('加载考试信息失败');
                        console.error(error);
                    }
                },
                
                calculateExamStats() {
                    if (this.examInfo.item) {
                        this.questionCount = this.examInfo.item.length;
                        this.totalScore = this.examInfo.item.reduce((sum, item) => {
                            try {
                                const scheme = JSON.parse(item.scheme);
                                return sum + (scheme.examConfig?.score || 0);
                            } catch (e) {
                                return sum;
                            }
                        }, 0);
                    }
                },
                
                getStatusText(status) {
                    const statusMap = {
                        '1': '未发布',
                        '2': '已发布',
                        '3': '已停止'
                    };
                    return statusMap[status] || '未知';
                },
                
                getStatusClass(status) {
                    const classMap = {
                        '1': 'status-draft',
                        '2': 'status-published',
                        '3': 'status-stopped'
                    };
                    return classMap[status] || 'status-draft';
                },
                
                async publishExam() {
                    try {
                        this.publishing = true;
                        
                        const response = await axios.post(`${API_BASE_URL}/S18M01B1/publish`, JSON.stringify({
                            formkey: this.formId
                        }), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        if (response.data.code === 200) {
                            this.$message.success('考试发布成功');
                            this.loadExamInfo();
                        } else {
                            this.$message.error(response.data.msg || '发布失败');
                        }
                    } catch (error) {
                        this.$message.error('发布考试失败');
                        console.error(error);
                    } finally {
                        this.publishing = false;
                    }
                },
                
                async updateSettings() {
                    try {
                        this.updating = true;
                        
                        const updateData = {
                            id: this.formId,
                            custom5: JSON.stringify(this.publishForm)
                        };
                        
                        const response = await axios.post(`${API_BASE_URL}/S18M01B1/update`, JSON.stringify(updateData), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        if (response.data.code === 200) {
                            this.$message.success('设置更新成功');
                        } else {
                            this.$message.error(response.data.msg || '更新失败');
                        }
                    } catch (error) {
                        this.$message.error('更新设置失败');
                        console.error(error);
                    } finally {
                        this.updating = false;
                    }
                },
                
                async stopExam() {
                    try {
                        await this.$confirm('确定要停止这场考试吗？', '确认操作', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        
                        const response = await axios.post(`${API_BASE_URL}/S18M01B1/stop`, JSON.stringify({
                            formkey: this.formId
                        }), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        if (response.data.code === 200) {
                            this.$message.success('考试已停止');
                            this.loadExamInfo();
                        } else {
                            this.$message.error(response.data.msg || '操作失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('停止考试失败');
                            console.error(error);
                        }
                    }
                },
                
                copyLink() {
                    navigator.clipboard.writeText(this.examLink).then(() => {
                        this.$message.success('链接已复制到剪贴板');
                    }).catch(() => {
                        this.$message.error('复制失败，请手动复制');
                    });
                },
                
                generateQRCode() {
                    this.$nextTick(() => {
                        const qrContainer = document.getElementById('qrcode');
                        if (qrContainer) {
                            qrContainer.innerHTML = '';
                            QRCode.toCanvas(qrContainer, this.examLink, {
                                width: 200,
                                height: 200,
                                margin: 2
                            }, (error) => {
                                if (error) console.error('生成二维码失败', error);
                            });
                        }
                    });
                },
                
                viewResults() {
                    window.location.href = `exam-results-v2.html?formId=${this.formId}`;
                },
                
                goBack() {
                    window.history.back();
                }
            }
        });
    </script>
</body>
</html>
