<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主子表关系测试</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #EBEEF5;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #303133;
        }
        .result-box {
            background: #F5F7FA;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            padding: 12px;
            margin-top: 12px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .form-row {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
            align-items: center;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>主子表关系测试页面</h1>
            <p>测试题库(主表)和题目(子表)的关联关系</p>
            
            <!-- 创建题库测试 -->
            <div class="test-section">
                <div class="test-title">1. 创建题库（主表）</div>
                <div class="form-row">
                    <el-input v-model="bankForm.name" placeholder="题库名称" style="width: 200px;"></el-input>
                    <el-input v-model="bankForm.description" placeholder="题库描述" style="width: 200px;"></el-input>
                    <el-button type="primary" @click="createBank" :loading="loading1">创建题库</el-button>
                </div>
                <div class="result-box" v-if="result1">{{ result1 }}</div>
            </div>
            
            <!-- 添加题目测试 -->
            <div class="test-section">
                <div class="test-title">2. 添加题目（子表）</div>
                <div class="form-row">
                    <el-input v-model="createdBankId" placeholder="题库ID" style="width: 200px;"></el-input>
                    <el-input v-model="questionForm.label" placeholder="题目标题" style="width: 200px;"></el-input>
                    <el-select v-model="questionForm.itemtype" placeholder="题目类型" style="width: 120px;">
                        <el-option label="单选题" value="RADIO"></el-option>
                        <el-option label="多选题" value="CHECKBOX"></el-option>
                        <el-option label="填空题" value="INPUT"></el-option>
                    </el-select>
                    <el-button type="primary" @click="addQuestion" :loading="loading2">添加题目</el-button>
                </div>
                <div class="result-box" v-if="result2">{{ result2 }}</div>
            </div>
            
            <!-- 查询题库详情测试 -->
            <div class="test-section">
                <div class="test-title">3. 查询题库详情（主子表联查）</div>
                <div class="form-row">
                    <el-input v-model="queryBankId" placeholder="题库ID" style="width: 200px;"></el-input>
                    <el-button type="primary" @click="queryBankDetail" :loading="loading3">查询详情</el-button>
                </div>
                <div class="result-box" v-if="result3">{{ result3 }}</div>
            </div>
            
            <!-- 主子表关系说明 -->
            <div class="test-section">
                <div class="test-title">主子表关系说明</div>
                <ul>
                    <li><strong>主表</strong>：Sa_QuestionBank（题库表）</li>
                    <li><strong>子表</strong>：Sa_QuestionBankItem（题目表）</li>
                    <li><strong>关联字段</strong>：子表的 <code>pid</code> 字段 = 主表的 <code>id</code> 字段</li>
                    <li><strong>创建题库</strong>：调用 <code>/S18M10B1/create</code>，可以同时传入 <code>item</code> 数组</li>
                    <li><strong>添加题目</strong>：调用 <code>/S18M10B1/createItem</code>，必须设置 <code>pid</code> 字段</li>
                    <li><strong>查询详情</strong>：调用 <code>/S18M10B1/getBillEntity</code>，自动关联查询子表</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script>
        // Add a request interceptor to set the Authorization header
        axios.interceptors.request.use(config => {
            config.headers.Authorization = 'admin';
            return config;
        }, error => {
            return Promise.reject(error);
        });

        const API_BASE_URL = 'http://*************:10668';

        new Vue({
            el: '#app',
            data() {
                return {
                    loading1: false,
                    loading2: false,
                    loading3: false,
                    result1: '',
                    result2: '',
                    result3: '',
                    createdBankId: '',
                    queryBankId: '',
                    bankForm: {
                        name: '测试题库' + Date.now(),
                        description: '这是一个测试题库'
                    },
                    questionForm: {
                        label: '这是一道测试题目',
                        itemtype: 'RADIO'
                    }
                }
            },
            methods: {
                async createBank() {
                    try {
                        this.loading1 = true;
                        this.result1 = '正在创建题库...';
                        
                        const bankData = {
                            name: this.bankForm.name,
                            description: this.bankForm.description,
                            banktype: 4,
                            ispublic: 1
                        };
                        
                        const response = await axios.post(`${API_BASE_URL}/S18M10B1/create`, JSON.stringify(bankData), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        this.result1 = `创建题库结果：
响应状态: ${response.status}
响应数据: ${JSON.stringify(response.data, null, 2)}

创建结果: ${response.data.code === 200 ? '成功' : '失败'}`;
                        
                        if (response.data.code === 200) {
                            this.createdBankId = response.data.data.id;
                            this.queryBankId = response.data.data.id;
                            this.$message.success('题库创建成功！');
                        } else {
                            this.$message.error(response.data.msg || '创建失败');
                        }
                        
                    } catch (error) {
                        this.result1 = `创建失败: ${error.message}
错误详情: ${JSON.stringify(error.response?.data || error, null, 2)}`;
                        this.$message.error('创建题库失败');
                    } finally {
                        this.loading1 = false;
                    }
                },
                
                async addQuestion() {
                    if (!this.createdBankId) {
                        this.$message.warning('请先创建题库');
                        return;
                    }
                    
                    try {
                        this.loading2 = true;
                        this.result2 = '正在添加题目...';
                        
                        const questionData = {
                            pid: this.createdBankId,  // 关键：设置父表ID
                            itemtype: this.questionForm.itemtype,
                            label: this.questionForm.label,
                            scheme: JSON.stringify({
                                typeId: this.questionForm.itemtype,
                                config: {
                                    label: this.questionForm.label,
                                    options: this.questionForm.itemtype === 'RADIO' ? [
                                        { label: '选项A', value: 1 },
                                        { label: '选项B', value: 2 }
                                    ] : [],
                                    required: true
                                },
                                examConfig: {
                                    scoringType: 1,
                                    score: 10,
                                    enableScore: true,
                                    answer: this.questionForm.itemtype === 'RADIO' ? [1] : null,
                                    answerAnalysis: '这是答案解析'
                                }
                            })
                        };
                        
                        const response = await axios.post(`${API_BASE_URL}/S18M10B1/createItem`, JSON.stringify(questionData), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        this.result2 = `添加题目结果：
响应状态: ${response.status}
响应数据: ${JSON.stringify(response.data, null, 2)}

添加结果: ${response.data.code === 200 ? '成功' : '失败'}
关联关系: pid=${this.createdBankId}`;
                        
                        if (response.data.code === 200) {
                            this.$message.success('题目添加成功！');
                        } else {
                            this.$message.error(response.data.msg || '添加失败');
                        }
                        
                    } catch (error) {
                        this.result2 = `添加失败: ${error.message}
错误详情: ${JSON.stringify(error.response?.data || error, null, 2)}`;
                        this.$message.error('添加题目失败');
                    } finally {
                        this.loading2 = false;
                    }
                },
                
                async queryBankDetail() {
                    if (!this.queryBankId) {
                        this.$message.warning('请输入题库ID');
                        return;
                    }
                    
                    try {
                        this.loading3 = true;
                        this.result3 = '正在查询题库详情...';
                        
                        const response = await axios.get(`${API_BASE_URL}/S18M10B1/getBillEntity?key=${this.queryBankId}`);
                        
                        this.result3 = `查询题库详情结果：
响应状态: ${response.status}
响应数据: ${JSON.stringify(response.data, null, 2)}

查询结果: ${response.data.code === 200 ? '成功' : '失败'}
题库信息: ${response.data.data?.name || '无'}
题目数量: ${response.data.data?.item?.length || 0} 道
主子表关联: ${response.data.data?.item?.length > 0 ? '正常' : '无关联数据'}`;
                        
                    } catch (error) {
                        this.result3 = `查询失败: ${error.message}
错误详情: ${JSON.stringify(error.response?.data || error, null, 2)}`;
                    } finally {
                        this.loading3 = false;
                    }
                }
            }
        });
    </script>
</body>
</html>
