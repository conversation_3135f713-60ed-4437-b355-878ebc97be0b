<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建考试表单 - 仓库管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .form-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #303133;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 8px;
        }
        .question-selector {
            border: 1px solid #DCDFE6;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .question-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #EBEEF5;
            border-radius: 6px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }
        .question-item:hover {
            border-color: #409EFF;
            background-color: #F0F9FF;
        }
        .question-item.selected {
            border-color: #409EFF;
            background-color: #E1F3D8;
        }
        .question-content {
            flex: 1;
            margin-left: 12px;
        }
        .question-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        .question-meta {
            font-size: 12px;
            color: #909399;
        }
        .selected-questions {
            background: #F0F9FF;
            border: 1px solid #409EFF;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
        }
        .selected-question-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        .drag-handle {
            cursor: move;
            color: #909399;
            margin-right: 8px;
        }
        .score-input {
            width: 80px;
            margin-left: 12px;
        }
        /* 拖拽排序样式 */
        .sortable-ghost {
            opacity: 0.5;
            background: #f0f9ff;
        }

        .sortable-chosen {
            transform: rotate(2deg);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        .drag-drop-area {
            min-height: 200px;
            border: 2px dashed #DCDFE6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #fafafa;
            transition: all 0.3s ease;
        }

        .drag-drop-area.drag-over {
            border-color: #409EFF;
            background: #f0f9ff;
            color: #409EFF;
        }

        .question-bank-selector {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .bank-item {
            padding: 16px;
            border-bottom: 1px solid #EBEEF5;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bank-item:hover {
            background: #f0f9ff;
        }

        .bank-item:last-child {
            border-bottom: none;
        }

        .bank-item.active {
            background: #e1f3d8;
            border-left: 4px solid #67C23A;
        }

        .exam-builder-container {
            display: flex;
            gap: 20px;
        }

        .question-library {
            width: 350px;
        }

        .exam-builder {
            flex: 1;
        }

        .total-score {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #E6A23C;
            margin-top: 16px;
            padding: 12px;
            background: #FDF6EC;
            border-radius: 6px;
        }
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 8px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        .btn-success {
            background: #67C23A;
            color: white;
        }
        .btn-default {
            background: #F5F7FA;
            color: #606266;
            border: 1px solid #DCDFE6;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><a href="question-bank-management-v2.html">题库管理</a></el-breadcrumb-item>
                <el-breadcrumb-item>创建考试表单</el-breadcrumb-item>
            </el-breadcrumb>
            <h1>创建考试表单</h1>
            <p>从题库中选择题目，组成考试表单</p>
        </div>

        <div class="container">
            <!-- 基本信息 -->
            <div class="form-card">
                <div class="section-title">考试基本信息</div>
                <el-form :model="examForm" :rules="examRules" ref="examForm" label-width="120px">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="考试名称" prop="name">
                                <el-input v-model="examForm.name" placeholder="请输入考试名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="考试时长" prop="duration">
                                <el-input-number v-model="examForm.duration" :min="1" :max="300" style="width: 100%;">
                                    <template slot="append">分钟</template>
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="考试说明">
                        <el-input
                            type="textarea"
                            v-model="examForm.description"
                            placeholder="请输入考试说明（可选）"
                            :rows="3">
                        </el-input>
                    </el-form-item>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="及格分数">
                                <el-input-number v-model="examForm.passScore" :min="0" :max="100" style="width: 100%;">
                                    <template slot="append">分</template>
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="允许重考">
                                <el-switch v-model="examForm.allowRetake"></el-switch>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="随机出题">
                                <el-switch v-model="examForm.randomOrder"></el-switch>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>

            <!-- 题目选择与拖拽构建 -->
            <div class="exam-builder-container">
                <!-- 题库选择器 -->
                <div class="question-library">
                    <div class="form-card">
                        <div class="section-title">题库选择</div>
                        <el-select v-model="selectedBankId" @change="loadBankQuestions" placeholder="请选择题库" style="width: 100%; margin-bottom: 16px;">
                            <el-option
                                v-for="bank in questionBanks"
                                :key="bank.id"
                                :label="bank.name"
                                :value="bank.id">
                            </el-option>
                        </el-select>

                        <div v-if="selectedBankId">
                            <h4>可选题目</h4>
                            <el-input
                                v-model="searchKeyword"
                                placeholder="搜索题目"
                                prefix-icon="el-icon-search"
                                style="margin-bottom: 16px;">
                            </el-input>
                            <div class="question-selector" style="max-height: 400px; overflow-y: auto;">
                                <div v-for="question in filteredQuestions" :key="question.id"
                                     class="question-item"
                                     draggable="true"
                                     @dragstart="handleQuestionDragStart($event, question)"
                                     @dragend="handleQuestionDragEnd"
                                     @click="selectQuestion(question)">
                                    <i class="el-icon-rank" style="margin-right: 8px; color: #909399;"></i>
                                    <div class="question-content">
                                        <div class="question-title" v-html="question.label"></div>
                                        <div class="question-meta">
                                            {{ getTypeText(question.itemtype) }} |
                                            {{ getQuestionScore(question) }}分 |
                                            {{ question.options ? question.options.length + '个选项' : '主观题' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 考试构建器 -->
                <div class="exam-builder">
                    <div class="form-card">
                        <div class="section-title">考试题目构建</div>
                        <p style="color: #909399; margin-bottom: 16px;">
                            从左侧拖拽题目到此处，或点击题目添加到考试中
                        </p>

                        <!-- 拖拽区域 -->
                        <div
                            class="drag-drop-area"
                            :class="{ 'drag-over': isDragOver }"
                            @dragover="handleDragOver"
                            @drop="handleDrop"
                            @dragenter="handleDragEnter"
                            @dragleave="handleDragLeave"
                            v-if="selectedQuestions.length === 0">
                            <i class="el-icon-upload" style="font-size: 48px; margin-bottom: 16px;"></i>
                            <p style="font-size: 16px; margin: 0;">拖拽题目到此处开始构建考试</p>
                            <p style="font-size: 14px; margin: 8px 0 0 0; color: #C0C4CC;">或点击左侧题目添加</p>
                        </div>

                        <!-- 已选题目列表 -->
                        <div v-else>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                <h4>已选题目 ({{ selectedQuestions.length }}题)</h4>
                                <div class="total-score" style="font-size: 16px; font-weight: 600; color: #E6A23C;">
                                    总分：{{ totalScore }}分
                                </div>
                            </div>

                            <div id="selected-questions-list">
                                <div v-for="(question, index) in selectedQuestions" :key="question.id"
                                     class="selected-question-item">
                                    <div style="display: flex; align-items: center; flex: 1;">
                                        <i class="el-icon-rank drag-handle" style="margin-right: 12px; cursor: move;"></i>
                                        <span style="font-weight: 600; margin-right: 8px;">{{ index + 1 }}.</span>
                                        <div style="flex: 1;">
                                            <div class="question-title" v-html="question.label"></div>
                                            <div class="question-meta" style="margin-top: 4px;">
                                                {{ getTypeText(question.itemtype) }} |
                                                原分值: {{ getQuestionScore(question) }}分
                                            </div>
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="font-size: 14px;">分值:</span>
                                        <el-input-number
                                            v-model="question.customScore"
                                            :min="1"
                                            :max="100"
                                            size="mini"
                                            style="width: 80px;">
                                        </el-input-number>
                                        <span style="font-size: 14px;">分</span>
                                        <el-button type="text" icon="el-icon-delete"
                                                   @click="removeQuestion(question)"
                                                   style="color: #F56C6C;">
                                        </el-button>
                                    </div>
                                </div>
                            </div>

                            <!-- 批量操作 -->
                            <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #EBEEF5;">
                                <el-button size="small" @click="clearAllQuestions">清空所有题目</el-button>
                                <el-button size="small" @click="randomizeOrder">随机排序</el-button>
                                <el-button size="small" @click="resetAllScores">重置分值</el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn btn-default" @click="goBack">返回</button>
                <button class="btn btn-primary" @click="previewExam" :disabled="selectedQuestions.length === 0">
                    预览考试
                </button>
                <button class="btn btn-success" @click="createExam" :disabled="selectedQuestions.length === 0" v-loading="creating">
                    创建考试
                </button>
            </div>
        </div>

        <!-- 预览对话框 -->
        <el-dialog title="考试预览" :visible.sync="previewDialogVisible" width="80%">
            <div style="max-height: 500px; overflow-y: auto;">
                <h3>{{ examForm.name }}</h3>
                <p v-if="examForm.description">{{ examForm.description }}</p>
                <p><strong>考试时长：</strong>{{ examForm.duration }}分钟 | <strong>总分：</strong>{{ totalScore }}分 | <strong>及格分：</strong>{{ examForm.passScore }}分</p>
                <hr>
                <div v-for="(question, index) in selectedQuestions" :key="question.id" style="margin-bottom: 24px;">
                    <h4>{{ index + 1 }}. <span v-html="question.label"></span> ({{ question.customScore }}分)</h4>
                    <div v-if="question.options && question.options.length > 0" style="margin-left: 20px;">
                        <div v-for="option in question.options" :key="option.value" style="margin-bottom: 8px;">
                            <span>{{ String.fromCharCode(64 + option.value) }}. {{ option.label }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <el-button @click="previewDialogVisible = false">关闭</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        // Add a request interceptor to set the Authorization header
        axios.interceptors.request.use(config => {
            config.headers.Authorization = 'admin';
            return config;
        }, error => {
            return Promise.reject(error);
        });

        const API_BASE_URL = 'http://*************:10668';

        new Vue({
            el: '#app',
            data() {
                return {
                    // 题库相关
                    questionBanks: [],
                    selectedBankId: '',
                    availableQuestions: [],
                    selectedQuestions: [],
                    searchKeyword: '',

                    // 拖拽相关
                    isDragOver: false,
                    draggedQuestion: null,

                    // 界面状态
                    previewDialogVisible: false,
                    creating: false,

                    // 考试表单数据
                    examForm: {
                        name: '',
                        description: '',
                        duration: 60,
                        passScore: 60,
                        allowRetake: false,
                        randomOrder: false
                    },
                    examRules: {
                        name: [
                            { required: true, message: '请输入考试名称', trigger: 'blur' },
                            { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
                        ],
                        duration: [
                            { required: true, message: '请设置考试时长', trigger: 'blur' }
                        ]
                    }
                }
            },
            computed: {
                filteredQuestions() {
                    if (!this.searchKeyword) return this.availableQuestions;
                    return this.availableQuestions.filter(q => 
                        q.label.toLowerCase().includes(this.searchKeyword.toLowerCase())
                    );
                },
                totalScore() {
                    return this.selectedQuestions.reduce((sum, q) => sum + (q.customScore || 0), 0);
                }
            },
            mounted() {
                this.loadQuestionBanks();

                // 如果URL中有bankId参数，自动选择该题库
                const urlBankId = new URLSearchParams(window.location.search).get('bankId');
                if (urlBankId) {
                    this.selectedBankId = urlBankId;
                    this.loadBankQuestions();
                }

                // 初始化拖拽排序
                this.$nextTick(() => {
                    this.initSortable();
                });
            },
            methods: {
                // 加载题库列表
                async loadQuestionBanks() {
                    try {
                        const response = await axios.post(`${API_BASE_URL}/S18M10B1/getBillList`, {
                            pageNum: 1,
                            pageSize: 100
                        });
                        if (response.data.code === 200) {
                            this.questionBanks = response.data.data.list || [];
                        }
                    } catch (error) {
                        this.$message.error('加载题库列表失败');
                        console.error(error);
                    }
                },

                // 加载指定题库的题目
                async loadBankQuestions() {
                    if (!this.selectedBankId) return;

                    try {
                        const response = await axios.get(`${API_BASE_URL}/S18M10B1/getBillEntity?key=${this.selectedBankId}`);
                        if (response.data.code === 200 && response.data.data.item) {
                            this.availableQuestions = response.data.data.item || [];
                            this.parseQuestions();
                        }
                    } catch (error) {
                        this.$message.error('加载题目失败');
                        console.error(error);
                    }
                },

                parseQuestions() {
                    this.availableQuestions.forEach(question => {
                        try {
                            const scheme = JSON.parse(question.scheme);
                            question.options = scheme.config?.options || [];
                            question.examConfig = scheme.examConfig || {};
                            question.customScore = question.examConfig.score || 10;
                        } catch (e) {
                            question.options = [];
                            question.examConfig = {};
                            question.customScore = 10;
                        }
                    });
                },

                // ========== 拖拽功能 ==========

                // 题目拖拽开始
                handleQuestionDragStart(event, question) {
                    this.draggedQuestion = question;
                    event.dataTransfer.effectAllowed = 'copy';
                    event.dataTransfer.setData('text/plain', JSON.stringify(question));
                },

                handleQuestionDragEnd() {
                    this.draggedQuestion = null;
                },

                // 拖拽区域事件
                handleDragOver(event) {
                    event.preventDefault();
                    event.dataTransfer.dropEffect = 'copy';
                },

                handleDragEnter(event) {
                    event.preventDefault();
                    this.isDragOver = true;
                },

                handleDragLeave(event) {
                    if (!event.currentTarget.contains(event.relatedTarget)) {
                        this.isDragOver = false;
                    }
                },

                handleDrop(event) {
                    event.preventDefault();
                    this.isDragOver = false;

                    try {
                        const questionData = JSON.parse(event.dataTransfer.getData('text/plain'));
                        this.addQuestionToExam(questionData);
                    } catch (error) {
                        console.error('拖拽数据解析失败', error);
                    }
                },

                // ========== 题目管理 ==========

                // 选择题目（点击添加）
                selectQuestion(question) {
                    this.addQuestionToExam(question);
                },

                // 添加题目到考试
                addQuestionToExam(question) {
                    // 检查是否已经添加
                    if (this.isQuestionSelected(question.id)) {
                        this.$message.warning('该题目已经添加到考试中');
                        return;
                    }

                    // 添加题目
                    const examQuestion = {
                        ...question,
                        customScore: question.examConfig?.score || 10
                    };

                    this.selectedQuestions.push(examQuestion);
                    this.$message.success('题目已添加到考试');
                },

                isQuestionSelected(questionId) {
                    return this.selectedQuestions.some(q => q.id === questionId);
                },

                removeQuestion(question) {
                    const index = this.selectedQuestions.findIndex(q => q.id === question.id);
                    if (index > -1) {
                        this.selectedQuestions.splice(index, 1);
                        this.$message.success('题目已移除');
                    }
                },

                // ========== 批量操作 ==========

                clearAllQuestions() {
                    this.$confirm('确定要清空所有题目吗？', '确认操作', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.selectedQuestions = [];
                        this.$message.success('已清空所有题目');
                    }).catch(() => {});
                },

                randomizeOrder() {
                    // Fisher-Yates 洗牌算法
                    for (let i = this.selectedQuestions.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [this.selectedQuestions[i], this.selectedQuestions[j]] =
                        [this.selectedQuestions[j], this.selectedQuestions[i]];
                    }
                    this.$message.success('题目顺序已随机化');
                },

                resetAllScores() {
                    this.selectedQuestions.forEach(question => {
                        question.customScore = question.examConfig?.score || 10;
                    });
                    this.$message.success('所有题目分值已重置');
                },
                
                getTypeText(type) {
                    const types = {
                        'RADIO': '单选题',
                        'CHECKBOX': '多选题',
                        'INPUT': '填空题',
                        'TEXTAREA': '简答题',
                        'NUMBER': '数字题'
                    };
                    return types[type] || type;
                },
                
                getQuestionScore(question) {
                    return question.examConfig?.score || 0;
                },
                
                previewExam() {
                    this.previewDialogVisible = true;
                },
                
                async createExam() {
                    try {
                        await this.$refs.examForm.validate();
                        
                        if (this.selectedQuestions.length === 0) {
                            this.$message.warning('请至少选择一道题目');
                            return;
                        }
                        
                        this.creating = true;
                        
                        // 构建表单数据，使用主子表结构
                        const examData = {
                            name: this.examForm.name,
                            description: this.examForm.description,
                            formtype: 4, // 考试类型
                            status: 1, // 未发布状态
                            custom1: this.examForm.timeLimit, // 考试时长
                            custom2: this.examForm.passingScore, // 及格分数
                            custom3: this.bankId, // 关联题库ID
                            item: this.selectedQuestions.map((q, index) => ({
                                formitemid: q.id,
                                type: q.itemtype,
                                label: q.label,
                                scheme: q.scheme,
                                sort: index + 1,
                                required: 1
                            }))
                        };

                        console.log('创建考试数据:', JSON.stringify(examData, null, 2));

                        const response = await axios.post(`${API_BASE_URL}/S18M01B1/create`, JSON.stringify(examData), {
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.data.code === 200) {
                            this.$message.success('考试创建成功！');
                            setTimeout(() => {
                                window.location.href = 'question-bank-management-v2.html';
                            }, 1500);
                        } else {
                            this.$message.error(response.data.message || '创建失败');
                        }
                    } catch (error) {
                        if (error.name !== 'NavigationDuplicated') {
                            this.$message.error('创建考试时发生错误');
                            console.error('创建考试失败', error);
                        }
                    } finally {
                        this.creating = false;
                    }
                },
                
                goBack() {
                    window.history.back();
                },

                // 初始化拖拽排序
                initSortable() {
                    this.$nextTick(() => {
                        const selectedQuestionsList = document.getElementById('selected-questions-list');
                        if (selectedQuestionsList && typeof Sortable !== 'undefined') {
                            new Sortable(selectedQuestionsList, {
                                animation: 150,
                                ghostClass: 'sortable-ghost',
                                chosenClass: 'sortable-chosen',
                                handle: '.drag-handle',
                                onEnd: (evt) => {
                                    // 更新数组顺序
                                    const oldIndex = evt.oldIndex;
                                    const newIndex = evt.newIndex;

                                    if (oldIndex !== newIndex) {
                                        const movedItem = this.selectedQuestions.splice(oldIndex, 1)[0];
                                        this.selectedQuestions.splice(newIndex, 0, movedItem);
                                        this.$message.success('题目顺序已调整');
                                    }
                                }
                            });
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>
