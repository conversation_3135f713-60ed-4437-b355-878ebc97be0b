<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试结果展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .exam-info {
            color: #666;
            font-size: 14px;
        }
        
        .exam-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .score-display {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .earned-score {
            color: #4CAF50;
        }
        
        .divider {
            color: rgba(255,255,255,0.7);
            margin: 0 10px;
        }
        
        .total-score {
            color: rgba(255,255,255,0.9);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .questions-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .questions-section h2 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .question-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        
        .question-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .question-item.correct {
            border-left: 4px solid #4CAF50;
            background: linear-gradient(to right, #f8fff8, #fafafa);
        }
        
        .question-item.incorrect {
            border-left: 4px solid #f44336;
            background: linear-gradient(to right, #fff8f8, #fafafa);
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .question-title {
            flex: 1;
            font-weight: bold;
            line-height: 1.5;
        }
        
        .question-number {
            color: #666;
            margin-right: 10px;
        }
        
        .score-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            white-space: nowrap;
        }
        
        .answer-section {
            margin: 15px 0;
        }
        
        .answer-row {
            display: flex;
            margin: 10px 0;
            align-items: flex-start;
        }
        
        .answer-label {
            width: 80px;
            color: #666;
            font-size: 14px;
            flex-shrink: 0;
            font-weight: 500;
        }
        
        .answer-content {
            flex: 1;
            padding: 8px 12px;
            border-radius: 4px;
            background: white;
            border: 1px solid #e0e0e0;
        }
        
        .user-answer {
            color: #333;
        }
        
        .user-answer.no-answer {
            color: #999;
            font-style: italic;
        }
        
        .standard-answer {
            color: #4CAF50;
            font-weight: bold;
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .explanation {
            background: linear-gradient(to right, #f0f7ff, #fafafa);
            border: 1px solid #e1f5fe;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .explanation-label {
            color: #1976d2;
            font-weight: bold;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .explanation-label::before {
            content: "💡";
            margin-right: 5px;
        }
        
        .explanation-content {
            color: #333;
            line-height: 1.6;
        }
        
        .result-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            font-weight: bold;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .icon-correct {
            color: white;
            background: #4CAF50;
        }
        
        .icon-incorrect {
            color: white;
            background: #f44336;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            color: #f44336;
        }
        
        .back-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-button:hover {
            background: #5a6fd8;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .score-display {
                font-size: 36px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .question-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .score-badge {
                margin-top: 10px;
            }
            
            .answer-row {
                flex-direction: column;
            }
            
            .answer-label {
                width: auto;
                margin-bottom: 5px;
            }
            
            .result-icon {
                position: static;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 返回按钮 -->
        <a href="javascript:history.back()" class="back-button">← 返回</a>
        
        <!-- 加载状态 -->
        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <div>正在加载考试结果...</div>
        </div>
        
        <!-- 错误状态 -->
        <div id="error" class="error" style="display: none;">
            <div id="errorMessage">加载失败，请稍后重试</div>
        </div>
        
        <!-- 考试结果内容 -->
        <div id="examResult" style="display: none;">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 id="examTitle">考试结果</h1>
                <div class="exam-info">
                    <span>考试时间: <span id="examDate">-</span></span>
                    <span style="margin-left: 20px;">提交时间: <span id="submitDate">-</span></span>
                </div>
            </div>
            
            <!-- 成绩汇总 -->
            <div class="exam-summary">
                <div class="score-display">
                    <span class="earned-score" id="earnedScore">0</span>
                    <span class="divider">/</span>
                    <span class="total-score" id="totalScore">0</span>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="accuracy">0%</div>
                        <div class="stat-label">正确率</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="correctCount">0</div>
                        <div class="stat-label">答对题数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalCount">0</div>
                        <div class="stat-label">总题数</div>
                    </div>
                </div>
            </div>
            
            <!-- 题目详情 -->
            <div class="questions-section">
                <h2>答题详情</h2>
                <div id="questionList">
                    <!-- 题目详情将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 格式化日期
        function formatDate(timestamp) {
            if (!timestamp) return '-';
            return new Date(timestamp).toLocaleString('zh-CN');
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('examResult').style.display = 'none';
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('error').style.display = 'block';
        }

        // 渲染考试结果
        function renderExamResult(examData) {
            try {
                // 解析详细得分信息
                let scoreDetails;
                if (examData.scoredetail) {
                    scoreDetails = JSON.parse(examData.scoredetail);
                } else {
                    showError('该考试记录没有详细得分信息');
                    return;
                }
                
                // 更新页面标题和基本信息
                document.getElementById('examTitle').textContent = (examData.formname || '考试结果').replace(/<[^>]*>/g, '');
                document.getElementById('examDate').textContent = formatDate(examData.createdate);
                document.getElementById('submitDate').textContent = formatDate(examData.modifydate);
                
                // 更新成绩汇总
                const summary = scoreDetails.summary;
                document.getElementById('earnedScore').textContent = summary.totalEarnedScore;
                document.getElementById('totalScore').textContent = summary.totalPossibleScore;
                document.getElementById('accuracy').textContent = summary.accuracy.toFixed(1) + '%';
                document.getElementById('correctCount').textContent = summary.correctCount;
                document.getElementById('totalCount').textContent = summary.totalCount;
                
                // 渲染题目详情
                const questionList = document.getElementById('questionList');
                questionList.innerHTML = '';
                
                scoreDetails.questionDetails.forEach((question, index) => {
                    const questionElement = document.createElement('div');
                    questionElement.className = `question-item ${question.isCorrect ? 'correct' : 'incorrect'}`;
                    
                    questionElement.innerHTML = `
                        <div class="question-header">
                            <div class="question-title">
                                <span class="question-number">第${index + 1}题</span>
                                ${question.questionTitle}
                            </div>
                            <div class="score-badge">${question.earnedScore}/${question.totalScore}分</div>
                        </div>
                        
                        <div class="answer-section">
                            <div class="answer-row">
                                <div class="answer-label">您的答案:</div>
                                <div class="answer-content user-answer ${!question.userAnswerText ? 'no-answer' : ''}">
                                    ${question.userAnswerText || '未作答'}
                                </div>
                            </div>
                            <div class="answer-row">
                                <div class="answer-label">标准答案:</div>
                                <div class="answer-content standard-answer">${question.standardAnswerText}</div>
                            </div>
                        </div>
                        
                        ${question.answerAnalysis ? `
                            <div class="explanation">
                                <div class="explanation-label">解析</div>
                                <div class="explanation-content">${question.answerAnalysis}</div>
                            </div>
                        ` : ''}
                        
                        <div class="result-icon ${question.isCorrect ? 'icon-correct' : 'icon-incorrect'}">
                            ${question.isCorrect ? '✓' : '✗'}
                        </div>
                    `;
                    
                    questionList.appendChild(questionElement);
                });
                
                // 显示结果，隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                document.getElementById('examResult').style.display = 'block';
                
            } catch (error) {
                console.error('解析考试结果失败:', error);
                showError('解析考试结果失败: ' + error.message);
            }
        }

        // 加载考试结果
        async function loadExamResult() {
            try {
                // 从URL参数获取表单ID
                const formId = getUrlParameter('formId');
                if (!formId) {
                    showError('缺少表单ID参数');
                    return;
                }
                
                const response = await fetch('/S18M07B1/getSomeFormByUserid', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        "PageNum": 1,
                        "PageSize": 20,
                        "OrderType": 1,
                        "SearchType": 1,
                        "scenedata": [{
                            "field": "Sa_FormData.formid",
                            "fieldtype": 0,
                            "math": "equal",
                            "value": formId
                        }]
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200 && data.data.list.length > 0) {
                    const examRecord = data.data.list[0];
                    renderExamResult(examRecord);
                } else {
                    showError('未找到考试记录');
                }
            } catch (error) {
                console.error('加载考试结果失败:', error);
                showError('加载考试结果失败: ' + error.message);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadExamResult();
        });
    </script>
</body>
</html>
