<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线考试 - 仓库管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .exam-header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .header-content {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .exam-title {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
        }
        .timer {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #E6A23C;
        }
        .timer.warning {
            color: #F56C6C;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        .container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .exam-info {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .question-nav {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .nav-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #303133;
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .nav-item {
            width: 40px;
            height: 40px;
            border: 2px solid #DCDFE6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .nav-item.answered {
            background: #E1F3D8;
            border-color: #67C23A;
            color: #67C23A;
        }
        .nav-item.current {
            background: #409EFF;
            border-color: #409EFF;
            color: white;
        }
        .question-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #EBEEF5;
        }
        .question-number {
            font-size: 18px;
            font-weight: 600;
            color: #409EFF;
        }
        .question-score {
            font-size: 14px;
            color: #E6A23C;
            font-weight: 600;
        }
        .question-content {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #303133;
        }
        .options-container {
            margin-bottom: 20px;
        }
        .option-item {
            margin-bottom: 12px;
            padding: 12px;
            border: 1px solid #EBEEF5;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .option-item:hover {
            border-color: #409EFF;
            background-color: #F0F9FF;
        }
        .option-item.selected {
            border-color: #409EFF;
            background-color: #E1F3D8;
        }
        .question-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        .btn-success {
            background: #67C23A;
            color: white;
        }
        .btn-default {
            background: #F5F7FA;
            color: #606266;
            border: 1px solid #DCDFE6;
        }
        .submit-section {
            background: white;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .progress-info {
            margin-bottom: 20px;
            font-size: 16px;
            color: #606266;
        }
        .answered-count {
            color: #67C23A;
            font-weight: 600;
        }
        .total-count {
            color: #409EFF;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 考试头部 -->
        <div class="exam-header">
            <div class="header-content">
                <div class="exam-title">{{ examInfo.name || '在线考试' }}</div>
                <div class="timer" :class="{ warning: timeWarning }">
                    <i class="el-icon-time" style="margin-right: 8px;"></i>
                    {{ formatTime(remainingTime) }}
                </div>
            </div>
        </div>

        <div class="container">
            <!-- 考试说明 -->
            <div class="exam-info" v-if="!examStarted">
                <h3>考试说明</h3>
                <p v-if="examInfo.description">{{ examInfo.description }}</p>
                <ul>
                    <li>考试时长：{{ examInfo.custom1 || 60 }}分钟</li>
                    <li>题目数量：{{ questions.length }}题</li>
                    <li>总分：{{ totalScore }}分</li>
                    <li>及格分数：{{ examInfo.custom2 || 60 }}分</li>
                    <li>请在规定时间内完成所有题目</li>
                    <li>考试过程中请勿刷新页面或关闭浏览器</li>
                </ul>
                <div style="text-align: center; margin-top: 30px;">
                    <el-button type="primary" size="large" @click="startExam">开始考试</el-button>
                </div>
            </div>

            <!-- 题目导航 -->
            <div class="question-nav" v-if="examStarted">
                <div class="nav-title">题目导航</div>
                <div class="nav-grid">
                    <div v-for="(question, index) in questions" :key="index"
                         class="nav-item"
                         :class="{ 
                             answered: isQuestionAnswered(index), 
                             current: currentQuestionIndex === index 
                         }"
                         @click="goToQuestion(index)">
                        {{ index + 1 }}
                    </div>
                </div>
            </div>

            <!-- 当前题目 -->
            <div class="question-card" v-if="examStarted && currentQuestion">
                <div class="question-header">
                    <div class="question-number">
                        第 {{ currentQuestionIndex + 1 }} 题
                    </div>
                    <div class="question-score">
                        {{ getQuestionScore(currentQuestion) }}分
                    </div>
                </div>
                
                <div class="question-content" v-html="currentQuestion.label"></div>
                
                <!-- 选择题选项 -->
                <div class="options-container" v-if="currentQuestion.options && currentQuestion.options.length > 0">
                    <div v-for="option in currentQuestion.options" :key="option.value"
                         class="option-item"
                         :class="{ selected: isOptionSelected(option.value) }"
                         @click="selectOption(option.value)">
                        <el-radio v-if="currentQuestion.itemtype === 'RADIO'"
                                  :value="answers[currentQuestionIndex]"
                                  :label="option.value">
                            {{ option.label }}
                        </el-radio>
                        <el-checkbox v-else-if="currentQuestion.itemtype === 'CHECKBOX'"
                                     :value="isOptionSelected(option.value)"
                                     @change="toggleOption(option.value)">
                            {{ option.label }}
                        </el-checkbox>
                    </div>
                </div>
                
                <!-- 填空题/简答题 -->
                <div v-else-if="['INPUT', 'TEXTAREA', 'NUMBER'].includes(currentQuestion.itemtype)">
                    <el-input v-if="currentQuestion.itemtype === 'INPUT'"
                              v-model="answers[currentQuestionIndex]"
                              placeholder="请输入答案"
                              style="width: 100%;">
                    </el-input>
                    <el-input-number v-else-if="currentQuestion.itemtype === 'NUMBER'"
                                     v-model="answers[currentQuestionIndex]"
                                     placeholder="请输入数字"
                                     style="width: 100%;">
                    </el-input-number>
                    <el-input v-else-if="currentQuestion.itemtype === 'TEXTAREA'"
                              type="textarea"
                              v-model="answers[currentQuestionIndex]"
                              placeholder="请输入答案"
                              :rows="6"
                              style="width: 100%;">
                    </el-input>
                </div>
                
                <div class="question-actions">
                    <el-button v-if="currentQuestionIndex > 0" @click="previousQuestion">上一题</el-button>
                    <div v-else></div>
                    <el-button v-if="currentQuestionIndex < questions.length - 1" 
                               type="primary" @click="nextQuestion">下一题</el-button>
                    <el-button v-else type="success" @click="showSubmitDialog">提交答卷</el-button>
                </div>
            </div>

            <!-- 提交区域 -->
            <div class="submit-section" v-if="examStarted">
                <div class="progress-info">
                    已答题：<span class="answered-count">{{ answeredCount }}</span> / 
                    <span class="total-count">{{ questions.length }}</span>
                </div>
                <el-button type="success" size="large" @click="showSubmitDialog">提交答卷</el-button>
            </div>
        </div>

        <!-- 提交确认对话框 -->
        <el-dialog title="提交确认" :visible.sync="submitDialogVisible" width="500px">
            <div style="text-align: center;">
                <p>您确定要提交答卷吗？</p>
                <p>已答题：<span style="color: #67C23A; font-weight: 600;">{{ answeredCount }}</span> / {{ questions.length }}</p>
                <p v-if="answeredCount < questions.length" style="color: #F56C6C;">
                    还有 {{ questions.length - answeredCount }} 题未作答
                </p>
                <p style="color: #E6A23C;">提交后将无法修改答案</p>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="submitDialogVisible = false">继续答题</el-button>
                <el-button type="primary" @click="submitExam" :loading="submitting">确认提交</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script>
        // Add a request interceptor to set the Authorization header
        axios.interceptors.request.use(config => {
            config.headers.Authorization = 'admin';
            return config;
        }, error => {
            return Promise.reject(error);
        });

        const API_BASE_URL = 'http://*************:10668';

        new Vue({
            el: '#app',
            data() {
                return {
                    formId: '',
                    examInfo: {},
                    questions: [],
                    answers: {},
                    currentQuestionIndex: 0,
                    examStarted: false,
                    startTime: null,
                    remainingTime: 0,
                    timer: null,
                    submitDialogVisible: false,
                    submitting: false
                }
            },
            computed: {
                currentQuestion() {
                    return this.questions[this.currentQuestionIndex];
                },
                totalScore() {
                    return this.questions.reduce((sum, q) => sum + this.getQuestionScore(q), 0);
                },
                answeredCount() {
                    return Object.keys(this.answers).filter(key => {
                        const answer = this.answers[key];
                        return answer !== null && answer !== undefined && answer !== '';
                    }).length;
                },
                timeWarning() {
                    return this.remainingTime <= 300; // 最后5分钟警告
                }
            },
            mounted() {
                this.formId = new URLSearchParams(window.location.search).get('formId');
                if (this.formId) {
                    this.loadExamInfo();
                }
            },
            beforeDestroy() {
                if (this.timer) {
                    clearInterval(this.timer);
                }
            },
            methods: {
                async loadExamInfo() {
                    try {
                        const response = await axios.get(`${API_BASE_URL}/S18M01B1/getBillEntity?key=${this.formId}`);
                        if (response.data.code === 200) {
                            this.examInfo = response.data.data;
                            this.questions = response.data.data.item || [];
                            this.parseQuestions();
                            this.initAnswers();
                        }
                    } catch (error) {
                        this.$message.error('加载考试信息失败');
                        console.error(error);
                    }
                },
                
                parseQuestions() {
                    this.questions.forEach(question => {
                        try {
                            const scheme = JSON.parse(question.scheme);
                            question.options = scheme.config?.options || [];
                            question.examConfig = scheme.examConfig || {};
                        } catch (e) {
                            question.options = [];
                            question.examConfig = {};
                        }
                    });
                },
                
                initAnswers() {
                    this.questions.forEach((question, index) => {
                        this.$set(this.answers, index, null);
                    });
                },
                
                startExam() {
                    this.examStarted = true;
                    this.startTime = new Date();
                    this.remainingTime = (parseInt(this.examInfo.custom1) || 60) * 60; // 转换为秒
                    this.startTimer();
                },
                
                startTimer() {
                    this.timer = setInterval(() => {
                        this.remainingTime--;
                        if (this.remainingTime <= 0) {
                            this.timeUp();
                        }
                    }, 1000);
                },
                
                timeUp() {
                    clearInterval(this.timer);
                    this.$message.warning('考试时间已到，系统将自动提交答卷');
                    this.submitExam();
                },
                
                formatTime(seconds) {
                    const hours = Math.floor(seconds / 3600);
                    const minutes = Math.floor((seconds % 3600) / 60);
                    const secs = seconds % 60;
                    
                    if (hours > 0) {
                        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                    } else {
                        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                    }
                },
                
                getQuestionScore(question) {
                    return question.examConfig?.score || 0;
                },
                
                isQuestionAnswered(index) {
                    const answer = this.answers[index];
                    return answer !== null && answer !== undefined && answer !== '';
                },
                
                isOptionSelected(value) {
                    const answer = this.answers[this.currentQuestionIndex];
                    if (Array.isArray(answer)) {
                        return answer.includes(value);
                    }
                    return answer === value;
                },
                
                selectOption(value) {
                    if (this.currentQuestion.itemtype === 'RADIO') {
                        this.$set(this.answers, this.currentQuestionIndex, value);
                    }
                },
                
                toggleOption(value) {
                    if (this.currentQuestion.itemtype === 'CHECKBOX') {
                        let answer = this.answers[this.currentQuestionIndex] || [];
                        if (!Array.isArray(answer)) {
                            answer = [];
                        }
                        
                        const index = answer.indexOf(value);
                        if (index > -1) {
                            answer.splice(index, 1);
                        } else {
                            answer.push(value);
                        }
                        
                        this.$set(this.answers, this.currentQuestionIndex, answer);
                    }
                },
                
                goToQuestion(index) {
                    this.currentQuestionIndex = index;
                },
                
                previousQuestion() {
                    if (this.currentQuestionIndex > 0) {
                        this.currentQuestionIndex--;
                    }
                },
                
                nextQuestion() {
                    if (this.currentQuestionIndex < this.questions.length - 1) {
                        this.currentQuestionIndex++;
                    }
                },
                
                showSubmitDialog() {
                    this.submitDialogVisible = true;
                },
                
                async submitExam() {
                    try {
                        this.submitting = true;
                        
                        const endTime = new Date();
                        const completeTime = Math.floor((endTime - this.startTime) / 1000);
                        
                        // 构建提交数据，兼容TDuck格式
                        const submitData = {
                            formid: this.formId,
                            completeTime: completeTime,
                            originaldata: JSON.stringify(this.answers),
                            realdata: JSON.stringify(this.answers),
                            submitOs: navigator.platform,
                            submitBrowser: navigator.userAgent.split(' ').pop(),
                            beginTime: this.startTime.toISOString().slice(0, 19).replace('T', ' ')
                        };
                        
                        const response = await axios.post(`${API_BASE_URL}/SaFormdata/create`, JSON.stringify(submitData), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        if (response.data.code === 200) {
                            clearInterval(this.timer);
                            this.$message.success('答卷提交成功');

                            // 跳转到结果页面
                            setTimeout(() => {
                                window.location.href = `exam-result-v2.html?dataId=${response.data.data}`;
                            }, 1500);
                        } else {
                            this.$message.error(response.data.msg || '提交失败');
                        }
                    } catch (error) {
                        this.$message.error('提交答卷失败');
                        console.error(error);
                    } finally {
                        this.submitting = false;
                        this.submitDialogVisible = false;
                    }
                }
            }
        });
    </script>
</body>
</html>
