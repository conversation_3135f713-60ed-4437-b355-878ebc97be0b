<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线考试管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }
        
        .layout-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #304156;
            color: white;
            overflow-y: auto;
            transition: width 0.3s ease;
        }
        
        .sidebar.collapsed {
            width: 64px;
        }
        
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #434a5a;
            background: #263445;
        }
        
        .sidebar.collapsed .sidebar-header {
            padding: 20px 10px;
        }
        
        .logo {
            font-size: 18px;
            font-weight: 600;
            color: #409EFF;
        }
        
        .sidebar.collapsed .logo {
            font-size: 14px;
        }
        
        .sidebar-menu {
            padding: 0;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #434a5a;
            text-decoration: none;
            color: #bfcbd9;
        }
        
        .menu-item:hover {
            background: #434a5a;
            color: white;
        }
        
        .menu-item.active {
            background: #409EFF;
            color: white;
        }
        
        .menu-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            font-size: 16px;
            text-align: center;
        }
        
        .sidebar.collapsed .menu-icon {
            margin-right: 0;
        }
        
        .menu-text {
            flex: 1;
            transition: opacity 0.3s ease;
        }
        
        .sidebar.collapsed .menu-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 60px;
            background: white;
            border-bottom: 1px solid #e6e6e6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 1px 4px rgba(0,21,41,.08);
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .collapse-btn {
            margin-right: 20px;
            cursor: pointer;
            font-size: 18px;
            color: #606266;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            color: #606266;
        }
        
        .content-area {
            flex: 1;
            overflow: hidden;
        }
        
        .iframe-container {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        
        .welcome-page {
            padding: 40px;
            text-align: center;
            background: white;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .welcome-title {
            font-size: 32px;
            color: #303133;
            margin-bottom: 16px;
        }
        
        .welcome-subtitle {
            font-size: 16px;
            color: #606266;
            margin-bottom: 40px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature-card {
            padding: 24px;
            border: 1px solid #EBEEF5;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #409EFF;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 16px;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #303133;
        }
        
        .feature-desc {
            color: #606266;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
                <div class="sidebar-header">
                    <div class="logo">{{ sidebarCollapsed ? 'EMS' : '考试管理系统' }}</div>
                </div>
                <div class="sidebar-menu">
                    <a v-for="item in menuItems" :key="item.key"
                       class="menu-item"
                       :class="{ active: currentPage === item.key }"
                       @click="navigateTo(item.key, item.url)">
                        <div class="menu-icon">{{ item.icon }}</div>
                        <div class="menu-text">{{ item.title }}</div>
                    </a>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 头部 -->
                <div class="header">
                    <div class="header-left">
                        <div class="collapse-btn" @click="toggleSidebar">
                            <i :class="sidebarCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
                        </div>
                        <div class="page-title">{{ currentPageTitle }}</div>
                    </div>
                    <div class="header-right">
                        <div class="user-info">
                            <i class="el-icon-user"></i>
                            <span style="margin-left: 8px;">管理员</span>
                        </div>
                    </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="content-area">
                    <!-- 欢迎页面 -->
                    <div v-if="currentPage === 'welcome'" class="welcome-page">
                        <h1 class="welcome-title">欢迎使用在线考试管理系统</h1>
                        <p class="welcome-subtitle">一个功能完整的在线考试平台，支持题库管理、考试创建、在线答题和成绩统计</p>
                        
                        <div class="feature-grid">
                            <div class="feature-card" @click="navigateTo('question-bank', 'question-bank-management-v2.html')">
                                <div class="feature-icon">📚</div>
                                <div class="feature-title">题库管理</div>
                                <div class="feature-desc">创建和管理题库，支持多种题型，灵活的题目配置</div>
                            </div>
                            <div class="feature-card" @click="navigateTo('exam-create', 'exam-form-create-v2.html')">
                                <div class="feature-icon">📝</div>
                                <div class="feature-title">考试创建</div>
                                <div class="feature-desc">基于题库快速创建考试，设置考试时间和规则</div>
                            </div>
                            <div class="feature-card" @click="navigateTo('exam-publish', 'exam-publish-v2.html')">
                                <div class="feature-icon">🚀</div>
                                <div class="feature-title">考试发布</div>
                                <div class="feature-desc">发布考试，生成考试链接，管理考试状态</div>
                            </div>
                            <div class="feature-card" @click="navigateTo('api-test', 'api-test.html')">
                                <div class="feature-icon">🔧</div>
                                <div class="feature-title">API测试</div>
                                <div class="feature-desc">测试系统API接口，验证功能正常性</div>
                            </div>
                            <div class="feature-card" @click="navigateTo('master-detail-test', 'master-detail-test.html')">
                                <div class="feature-icon">🔗</div>
                                <div class="feature-title">主子表测试</div>
                                <div class="feature-desc">测试题库和题目的主子表关联关系</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- iframe内容 -->
                    <iframe v-else
                            :src="currentUrl"
                            class="iframe-container"
                            frameborder="0">
                    </iframe>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    sidebarCollapsed: false,
                    currentPage: 'welcome',
                    currentUrl: '',
                    menuItems: [
                        { key: 'welcome', title: '首页', icon: '🏠', url: '' },
                        { key: 'question-bank', title: '题库管理', icon: '📚', url: 'question-bank-management-v2.html' },
                        { key: 'exam-create', title: '创建考试', icon: '📝', url: 'exam-form-create-v2.html' },
                        { key: 'exam-publish', title: '发布考试', icon: '🚀', url: 'exam-publish-v2.html' },
                        { key: 'exam-take', title: '参加考试', icon: '✏️', url: 'take-exam-v2.html' },
                        { key: 'api-test', title: 'API测试', icon: '🔧', url: 'api-test.html' },
                        { key: 'master-detail-test', title: '主子表测试', icon: '🔗', url: 'master-detail-test.html' }
                    ]
                }
            },
            computed: {
                currentPageTitle() {
                    const item = this.menuItems.find(item => item.key === this.currentPage);
                    return item ? item.title : '在线考试管理系统';
                }
            },
            methods: {
                toggleSidebar() {
                    this.sidebarCollapsed = !this.sidebarCollapsed;
                },
                
                navigateTo(key, url) {
                    this.currentPage = key;
                    this.currentUrl = url;
                }
            }
        });
    </script>
</body>
</html>
