<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #EBEEF5;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #303133;
        }
        .result-box {
            background: #F5F7FA;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            padding: 12px;
            margin-top: 12px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>API测试页面</h1>
            
            <!-- 题库列表测试 -->
            <div class="test-section">
                <div class="test-title">1. 题库列表查询测试</div>
                <el-button type="primary" @click="testQuestionBankList" :loading="loading1">测试题库列表API</el-button>
                <div class="result-box" v-if="result1">{{ result1 }}</div>
            </div>
            
            <!-- 创建题库测试 -->
            <div class="test-section">
                <div class="test-title">2. 创建题库测试</div>
                <el-row :gutter="12">
                    <el-col :span="6">
                        <el-input v-model="testBankName" placeholder="题库名称"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-input v-model="testBankDesc" placeholder="题库描述"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-button type="primary" @click="testCreateBank" :loading="loading2">创建题库</el-button>
                    </el-col>
                </el-row>
                <div class="result-box" v-if="result2">{{ result2 }}</div>
            </div>
            
            <!-- 题库详情测试 -->
            <div class="test-section">
                <div class="test-title">3. 题库详情查询测试</div>
                <el-row :gutter="12">
                    <el-col :span="8">
                        <el-input v-model="testBankId" placeholder="输入题库ID"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-button type="primary" @click="testBankDetail" :loading="loading3">查询题库详情</el-button>
                    </el-col>
                </el-row>
                <div class="result-box" v-if="result3">{{ result3 }}</div>
            </div>
            
            <!-- API格式说明 -->
            <div class="test-section">
                <div class="test-title">API返回格式说明</div>
                <p>您的API返回格式：</p>
                <pre>{
  "code": 200,
  "msg": null,
  "data": {
    "total": 1,
    "list": [...],  // 列表数据
    // 或者单个对象数据
  }
}</pre>
                <p>页面判断逻辑已修正为：<code>response.data.code === 200</code></p>
                <p>数据获取逻辑已修正为：<code>response.data.data.list</code> 或 <code>response.data.data</code></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script>
        // Add a request interceptor to set the Authorization header
        axios.interceptors.request.use(config => {
            config.headers.Authorization = 'admin';
            return config;
        }, error => {
            return Promise.reject(error);
        });

        const API_BASE_URL = 'http://*************:10668';

        new Vue({
            el: '#app',
            data() {
                return {
                    loading1: false,
                    loading2: false,
                    loading3: false,
                    result1: '',
                    result2: '',
                    result3: '',
                    testBankName: '测试题库' + Date.now(),
                    testBankDesc: '这是一个测试题库',
                    testBankId: ''
                }
            },
            methods: {
                async testQuestionBankList() {
                    try {
                        this.loading1 = true;
                        this.result1 = '正在请求...';
                        
                        const queryParam = {
                            current: 0,
                            size: 100,
                            orderBy: "Sa_QuestionBank.CreateDate"
                        };
                        
                        const response = await axios.post(`${API_BASE_URL}/S18M10B1/getBillList`, JSON.stringify(queryParam), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        this.result1 = `请求成功！
响应状态: ${response.status}
响应数据: ${JSON.stringify(response.data, null, 2)}

判断逻辑: response.data.code === 200 ? ${response.data.code === 200}
数据列表: ${response.data.data?.list?.length || 0} 条记录`;
                        
                        if (response.data.code === 200 && response.data.data?.list?.length > 0) {
                            this.testBankId = response.data.data.list[0].id;
                        }
                        
                    } catch (error) {
                        this.result1 = `请求失败: ${error.message}
错误详情: ${JSON.stringify(error.response?.data || error, null, 2)}`;
                    } finally {
                        this.loading1 = false;
                    }
                },
                
                async testCreateBank() {
                    try {
                        this.loading2 = true;
                        this.result2 = '正在创建...';
                        
                        const bankData = {
                            name: this.testBankName,
                            description: this.testBankDesc,
                            banktype: 4,
                            ispublic: 1
                        };
                        
                        const response = await axios.post(`${API_BASE_URL}/S18M10B1/create`, JSON.stringify(bankData), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        this.result2 = `创建请求完成！
响应状态: ${response.status}
响应数据: ${JSON.stringify(response.data, null, 2)}

判断逻辑: response.data.code === 200 ? ${response.data.code === 200}
创建结果: ${response.data.code === 200 ? '成功' : '失败'}`;
                        
                        if (response.data.code === 200) {
                            this.testBankId = response.data.data.id;
                            this.$message.success('题库创建成功！');
                        } else {
                            this.$message.error(response.data.msg || '创建失败');
                        }
                        
                    } catch (error) {
                        this.result2 = `创建失败: ${error.message}
错误详情: ${JSON.stringify(error.response?.data || error, null, 2)}`;
                        this.$message.error('创建题库失败');
                    } finally {
                        this.loading2 = false;
                    }
                },
                
                async testBankDetail() {
                    if (!this.testBankId) {
                        this.$message.warning('请先输入题库ID或创建一个题库');
                        return;
                    }
                    
                    try {
                        this.loading3 = true;
                        this.result3 = '正在查询...';
                        
                        const response = await axios.get(`${API_BASE_URL}/S18M10B1/getBillEntity?key=${this.testBankId}`);
                        
                        this.result3 = `查询完成！
响应状态: ${response.status}
响应数据: ${JSON.stringify(response.data, null, 2)}

判断逻辑: response.data.code === 200 ? ${response.data.code === 200}
题库信息: ${response.data.code === 200 ? '获取成功' : '获取失败'}`;
                        
                    } catch (error) {
                        this.result3 = `查询失败: ${error.message}
错误详情: ${JSON.stringify(error.response?.data || error, null, 2)}`;
                    } finally {
                        this.loading3 = false;
                    }
                }
            }
        });
    </script>
</body>
</html>
