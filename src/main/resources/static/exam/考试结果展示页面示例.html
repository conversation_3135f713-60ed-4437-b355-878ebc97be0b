<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试结果展示 - 基于ScoreDetail字段</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .exam-info {
            color: #666;
            font-size: 14px;
        }
        
        .exam-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .score-display {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .earned-score {
            color: #4CAF50;
        }
        
        .divider {
            color: rgba(255,255,255,0.7);
            margin: 0 10px;
        }
        
        .total-score {
            color: rgba(255,255,255,0.9);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .questions-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .questions-section h2 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .question-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        
        .question-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .question-item.correct {
            border-left: 4px solid #4CAF50;
            background: linear-gradient(to right, #f8fff8, #fafafa);
        }
        
        .question-item.incorrect {
            border-left: 4px solid #f44336;
            background: linear-gradient(to right, #fff8f8, #fafafa);
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .question-title {
            flex: 1;
            font-weight: bold;
            line-height: 1.5;
        }
        
        .question-number {
            color: #666;
            margin-right: 10px;
        }
        
        .score-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            white-space: nowrap;
        }
        
        .answer-section {
            margin: 15px 0;
        }
        
        .answer-row {
            display: flex;
            margin: 10px 0;
            align-items: flex-start;
        }
        
        .answer-label {
            width: 80px;
            color: #666;
            font-size: 14px;
            flex-shrink: 0;
            font-weight: 500;
        }
        
        .answer-content {
            flex: 1;
            padding: 8px 12px;
            border-radius: 4px;
            background: white;
            border: 1px solid #e0e0e0;
        }
        
        .user-answer {
            color: #333;
        }
        
        .user-answer.no-answer {
            color: #999;
            font-style: italic;
        }
        
        .standard-answer {
            color: #4CAF50;
            font-weight: bold;
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .explanation {
            background: linear-gradient(to right, #f0f7ff, #fafafa);
            border: 1px solid #e1f5fe;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .explanation-label {
            color: #1976d2;
            font-weight: bold;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .explanation-label::before {
            content: "💡";
            margin-right: 5px;
        }
        
        .explanation-content {
            color: #333;
            line-height: 1.6;
        }
        
        .result-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            font-weight: bold;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .icon-correct {
            color: white;
            background: #4CAF50;
        }
        
        .icon-incorrect {
            color: white;
            background: #f44336;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .score-display {
                font-size: 36px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .question-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .score-badge {
                margin-top: 10px;
            }
            
            .answer-row {
                flex-direction: column;
            }
            
            .answer-label {
                width: auto;
                margin-bottom: 5px;
            }
            
            .result-icon {
                position: static;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 加载状态 -->
        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <div>正在加载考试结果...</div>
        </div>
        
        <!-- 考试结果内容 -->
        <div id="examResult" style="display: none;">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 id="examTitle">数学试卷</h1>
                <div class="exam-info">
                    <span>考试时间: <span id="examDate">2025-07-10 14:30:00</span></span>
                    <span style="margin-left: 20px;">提交时间: <span id="submitDate">2025-07-10 15:30:00</span></span>
                </div>
            </div>
            
            <!-- 成绩汇总 -->
            <div class="exam-summary">
                <div class="score-display">
                    <span class="earned-score" id="earnedScore">100</span>
                    <span class="divider">/</span>
                    <span class="total-score" id="totalScore">100</span>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="accuracy">100%</div>
                        <div class="stat-label">正确率</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="correctCount">3</div>
                        <div class="stat-label">答对题数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalCount">3</div>
                        <div class="stat-label">总题数</div>
                    </div>
                </div>
            </div>
            
            <!-- 题目详情 -->
            <div class="questions-section">
                <h2>答题详情</h2>
                <div id="questionList">
                    <!-- 题目详情将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟从接口获取的数据（实际使用时替换为真实接口调用）
        const mockExamData = {
            "id": "1942878059141005312",
            "formname": "数学试卷",
            "totalscore": 100,
            "sumtotalscore": 100,
            "createdate": 1752053201000,
            "modifydate": 1752053206000,
            "scoredetail": JSON.stringify({
                "questionDetails": [
                    {
                        "formItemId": "radio1752048734612",
                        "questionTitle": "1. 已知函数f(x) = x² + 2x + 1，求f(2)的值是多少？",
                        "userAnswer": 3,
                        "standardAnswer": [3],
                        "userAnswerText": "9",
                        "standardAnswerText": "9",
                        "isCorrect": true,
                        "earnedScore": 30.0,
                        "totalScore": 30.0,
                        "scoringType": 1,
                        "explanation": "f(2) = 2² + 2×2 + 1 = 4 + 4 + 1 = 9"
                    },
                    {
                        "formItemId": "checkbox1752048737253",
                        "questionTitle": "2. x²=1的解是？",
                        "userAnswer": [1, 2],
                        "standardAnswer": [1, 2],
                        "userAnswerText": "1, -1",
                        "standardAnswerText": "1, -1",
                        "isCorrect": true,
                        "earnedScore": 20.0,
                        "totalScore": 20.0,
                        "scoringType": 1,
                        "explanation": "x²=1的解为x=±1，即x=1或x=-1"
                    },
                    {
                        "formItemId": "input1752048717109",
                        "questionTitle": "3. 解方程：2x + 5 = 15（只需填写x的值）",
                        "userAnswer": "5",
                        "standardAnswer": "5",
                        "userAnswerText": "5",
                        "standardAnswerText": "5",
                        "isCorrect": true,
                        "earnedScore": 50.0,
                        "totalScore": 50.0,
                        "scoringType": 3,
                        "explanation": "2x + 5 = 15，移项得2x = 15 - 5 = 10，所以x = 5"
                    }
                ],
                "summary": {
                    "totalEarnedScore": 100.0,
                    "totalPossibleScore": 100.0,
                    "correctCount": 3,
                    "totalCount": 3,
                    "accuracy": 100.0
                }
            })
        };

        // 格式化日期
        function formatDate(timestamp) {
            if (!timestamp) return '';
            return new Date(timestamp).toLocaleString('zh-CN');
        }

        // 渲染考试结果
        function renderExamResult(examData) {
            try {
                // 解析详细得分信息
                const scoreDetails = JSON.parse(examData.scoredetail);
                
                // 更新页面标题和基本信息
                document.getElementById('examTitle').textContent = examData.formname || '考试结果';
                document.getElementById('examDate').textContent = formatDate(examData.createdate);
                document.getElementById('submitDate').textContent = formatDate(examData.modifydate);
                
                // 更新成绩汇总
                const summary = scoreDetails.summary;
                document.getElementById('earnedScore').textContent = summary.totalEarnedScore;
                document.getElementById('totalScore').textContent = summary.totalPossibleScore;
                document.getElementById('accuracy').textContent = summary.accuracy.toFixed(1) + '%';
                document.getElementById('correctCount').textContent = summary.correctCount;
                document.getElementById('totalCount').textContent = summary.totalCount;
                
                // 渲染题目详情
                const questionList = document.getElementById('questionList');
                questionList.innerHTML = '';
                
                scoreDetails.questionDetails.forEach((question, index) => {
                    const questionElement = document.createElement('div');
                    questionElement.className = `question-item ${question.isCorrect ? 'correct' : 'incorrect'}`;
                    
                    questionElement.innerHTML = `
                        <div class="question-header">
                            <div class="question-title">
                                <span class="question-number">第${index + 1}题</span>
                                ${question.questionTitle}
                            </div>
                            <div class="score-badge">${question.earnedScore}/${question.totalScore}分</div>
                        </div>
                        
                        <div class="answer-section">
                            <div class="answer-row">
                                <div class="answer-label">您的答案:</div>
                                <div class="answer-content user-answer ${!question.userAnswerText ? 'no-answer' : ''}">
                                    ${question.userAnswerText || '未作答'}
                                </div>
                            </div>
                            <div class="answer-row">
                                <div class="answer-label">标准答案:</div>
                                <div class="answer-content standard-answer">${question.standardAnswerText}</div>
                            </div>
                        </div>
                        
                        ${question.explanation ? `
                            <div class="explanation">
                                <div class="explanation-label">解析</div>
                                <div class="explanation-content">${question.explanation}</div>
                            </div>
                        ` : ''}
                        
                        <div class="result-icon ${question.isCorrect ? 'icon-correct' : 'icon-incorrect'}">
                            ${question.isCorrect ? '✓' : '✗'}
                        </div>
                    `;
                    
                    questionList.appendChild(questionElement);
                });
                
                // 显示结果，隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                document.getElementById('examResult').style.display = 'block';
                
            } catch (error) {
                console.error('解析考试结果失败:', error);
                document.getElementById('loading').innerHTML = '<div style="color: #f44336;">解析考试结果失败，请稍后重试</div>';
            }
        }

        // 实际使用时的接口调用函数
        async function loadExamResult(formId, userId) {
            try {
                const response = await fetch('/S18M07B1/getSomeFormByUserid', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'your-token-here'
                    },
                    body: JSON.stringify({
                        "PageNum": 1,
                        "PageSize": 20,
                        "OrderType": 1,
                        "SearchType": 1,
                        "scenedata": [{
                            "field": "Sa_FormData.formid",
                            "fieldtype": 0,
                            "math": "equal",
                            "value": formId
                        }]
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200 && data.data.list.length > 0) {
                    const examRecord = data.data.list[0];
                    if (examRecord.scoredetail) {
                        renderExamResult(examRecord);
                    } else {
                        throw new Error('该考试记录没有详细得分信息');
                    }
                } else {
                    throw new Error('未找到考试记录');
                }
            } catch (error) {
                console.error('加载考试结果失败:', error);
                document.getElementById('loading').innerHTML = `<div style="color: #f44336;">加载失败: ${error.message}</div>`;
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟加载延迟
            setTimeout(() => {
                renderExamResult(mockExamData);
            }, 1000);
            
            // 实际使用时，调用真实接口
            // const formId = new URLSearchParams(window.location.search).get('formId');
            // const userId = getCurrentUserId(); // 获取当前用户ID的函数
            // loadExamResult(formId, userId);
        });
    </script>
</body>
</html>
