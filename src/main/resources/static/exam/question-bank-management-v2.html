<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库管理 - 仓库管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .bank-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .bank-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid #409EFF;
        }
        .bank-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .bank-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .bank-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin: 0;
        }
        .bank-type {
            background: #E1F3D8;
            color: #67C23A;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        .bank-description {
            color: #606266;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        .bank-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #409EFF;
        }
        .stat-label {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
        }
        .bank-actions {
            display: flex;
            gap: 8px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        .btn-primary:hover {
            background: #337ecc;
        }
        .btn-success {
            background: #67C23A;
            color: white;
        }
        .btn-success:hover {
            background: #529b2e;
        }
        .btn-warning {
            background: #E6A23C;
            color: white;
        }
        .btn-warning:hover {
            background: #b88230;
        }
        .btn-danger {
            background: #F56C6C;
            color: white;
        }
        .btn-danger:hover {
            background: #dd6161;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #909399;
        }
        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <h1>题库管理系统</h1>
            <p>创建和管理考试题库，支持多种题型和智能评分</p>
        </div>

        <div class="container">
            <!-- 工具栏 -->
            <div class="toolbar">
                <el-row :gutter="20" type="flex" justify="space-between" align="middle">
                    <el-col :span="12">
                        <el-input
                            v-model="searchKeyword"
                            placeholder="搜索题库名称或描述"
                            prefix-icon="el-icon-search"
                            @input="handleSearch">
                        </el-input>
                    </el-col>
                    <el-col :span="12" style="text-align: right;">
                        <el-button type="primary" icon="el-icon-plus" @click="showCreateDialog">
                            创建题库
                        </el-button>
                        <el-button type="success" icon="el-icon-upload" @click="showImportDialog">
                            导入题库
                        </el-button>
                    </el-col>
                </el-row>
            </div>

            <!-- 题库列表 -->
            <div v-if="questionBanks.length > 0" class="bank-grid">
                <div v-for="bank in filteredBanks" :key="bank.id" class="bank-card">
                    <div class="bank-header">
                        <h3 class="bank-title">{{ bank.name }}</h3>
                        <span class="bank-type">{{ getBankTypeText(bank.banktype) }}</span>
                    </div>
                    
                    <p class="bank-description">{{ bank.description || '暂无描述' }}</p>
                    
                    <div class="bank-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ bank.questioncount || 0 }}</div>
                            <div class="stat-label">题目数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ bank.usageCount || 0 }}</div>
                            <div class="stat-label">使用次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ formatDate(bank.createdate) }}</div>
                            <div class="stat-label">创建时间</div>
                        </div>
                    </div>
                    
                    <div class="bank-actions">
                        <button class="btn btn-primary" @click="manageQuestions(bank)">
                            管理题目
                        </button>
                        <button class="btn btn-success" @click="createExam(bank)">
                            创建考试
                        </button>
                        <button class="btn btn-warning" @click="editBank(bank)">
                            编辑
                        </button>
                        <button class="btn btn-danger" @click="deleteBank(bank)">
                            删除
                        </button>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-state">
                <div class="empty-icon">📚</div>
                <h3>暂无题库</h3>
                <p>点击"创建题库"开始创建您的第一个题库</p>
                <el-button type="primary" @click="showCreateDialog">创建题库</el-button>
            </div>
        </div>

        <!-- 创建题库对话框 -->
        <el-dialog title="创建题库" :visible.sync="createDialogVisible" width="600px">
            <el-form :model="bankForm" :rules="bankRules" ref="bankForm" label-width="100px">
                <el-form-item label="题库名称" prop="name">
                    <el-input v-model="bankForm.name" placeholder="请输入题库名称"></el-input>
                </el-form-item>
                <el-form-item label="题库描述" prop="description">
                    <el-input
                        type="textarea"
                        v-model="bankForm.description"
                        placeholder="请输入题库描述"
                        :rows="3">
                    </el-input>
                </el-form-item>
                <el-form-item label="题库类型" prop="banktype">
                    <el-select v-model="bankForm.banktype" placeholder="请选择题库类型" style="width: 100%;">
                        <el-option label="考试题库" :value="4"></el-option>
                        <el-option label="练习题库" :value="1"></el-option>
                        <el-option label="调研题库" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="公开设置">
                    <el-switch
                        v-model="bankForm.ispublic"
                        :active-value="1"
                        :inactive-value="0"
                        active-text="公开"
                        inactive-text="私有">
                    </el-switch>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="createDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="createBank" :loading="creating">创建</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script>
        // Add a request interceptor to set the Authorization header
        axios.interceptors.request.use(config => {
            config.headers.Authorization = 'admin';
            return config;
        }, error => {
            return Promise.reject(error);
        });

        const API_BASE_URL = 'http://*************:10668';

        new Vue({
            el: '#app',
            data() {
                return {
                    questionBanks: [],
                    searchKeyword: '',
                    createDialogVisible: false,
                    creating: false,
                    bankForm: {
                        name: '',
                        description: '',
                        banktype: 4,
                        ispublic: 1
                    },
                    bankRules: {
                        name: [
                            { required: true, message: '请输入题库名称', trigger: 'blur' },
                            { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
                        ],
                        banktype: [
                            { required: true, message: '请选择题库类型', trigger: 'change' }
                        ]
                    }
                }
            },
            computed: {
                filteredBanks() {
                    if (!this.searchKeyword) return this.questionBanks;
                    return this.questionBanks.filter(bank => 
                        bank.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
                        (bank.description && bank.description.toLowerCase().includes(this.searchKeyword.toLowerCase()))
                    );
                }
            },
            mounted() {
                this.loadQuestionBanks();
            },
            methods: {
                async loadQuestionBanks() {
                    try {
                        const queryParam = {
                            current: 0,
                            size: 100,
                            orderBy: "Sa_QuestionBank.CreateDate"
                        };
                        const response = await axios.post(`${API_BASE_URL}/S18M10B1/getBillList`, JSON.stringify(queryParam), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        if (response.data.code === 200) {
                            this.questionBanks = response.data.data.list || [];
                        }
                    } catch (error) {
                        this.$message.error('加载题库列表失败');
                        console.error(error);
                    }
                },
                
                showCreateDialog() {
                    this.createDialogVisible = true;
                    this.resetBankForm();
                },
                
                resetBankForm() {
                    this.bankForm = {
                        name: '',
                        description: '',
                        banktype: 4,
                        ispublic: 1
                    };
                    if (this.$refs.bankForm) {
                        this.$refs.bankForm.resetFields();
                    }
                },
                
                async createBank() {
                    try {
                        await this.$refs.bankForm.validate();
                        this.creating = true;
                        
                        const response = await axios.post(`${API_BASE_URL}/S18M10B1/create`, JSON.stringify(this.bankForm), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                        if (response.data.code === 200) {
                            this.$message.success('题库创建成功');
                            this.createDialogVisible = false;
                            this.loadQuestionBanks();
                        } else {
                            this.$message.error(response.data.msg || '创建失败');
                        }
                    } catch (error) {
                        this.$message.error('创建题库失败');
                        console.error(error);
                    } finally {
                        this.creating = false;
                    }
                },
                
                manageQuestions(bank) {
                    window.location.href = `question-management-v2.html?bankId=${bank.id}`;
                },
                
                createExam(bank) {
                    window.location.href = `exam-form-create-v2.html?bankId=${bank.id}`;
                },
                
                editBank(bank) {
                    this.bankForm = { ...bank };
                    this.createDialogVisible = true;
                },
                
                async deleteBank(bank) {
                    try {
                        await this.$confirm('确定要删除这个题库吗？删除后无法恢复。', '确认删除', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        
                        const response = await axios.get(`${API_BASE_URL}/S18M10B1/delete?key=${bank.id}`);
                        if (response.data.code === 200) {
                            this.$message.success('删除成功');
                            this.loadQuestionBanks();
                        } else {
                            this.$message.error(response.data.msg || '删除失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('删除失败');
                            console.error(error);
                        }
                    }
                },
                
                getBankTypeText(type) {
                    const types = {
                        1: '练习题库',
                        2: '调研题库',
                        4: '考试题库'
                    };
                    return types[type] || '未知类型';
                },
                
                formatDate(dateStr) {
                    if (!dateStr) return '-';
                    return new Date(dateStr).toLocaleDateString();
                },
                
                handleSearch() {
                    // 搜索逻辑已在computed中实现
                },
                
                showImportDialog() {
                    this.$message.info('导入功能开发中...');
                }
            }
        });
    </script>
</body>
</html>
