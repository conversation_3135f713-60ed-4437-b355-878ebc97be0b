<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试结果展示示例</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .score-summary {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .score-display {
            font-size: 48px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .earned-score {
            color: #4CAF50;
        }
        
        .divider {
            color: #999;
            margin: 0 10px;
        }
        
        .total-score {
            color: #666;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .question-list {
            padding: 20px;
        }
        
        .question-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 15px;
            padding: 15px;
            position: relative;
        }
        
        .question-item.correct {
            border-left: 4px solid #4CAF50;
            background-color: #f8fff8;
        }
        
        .question-item.incorrect {
            border-left: 4px solid #f44336;
            background-color: #fff8f8;
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .question-title {
            flex: 1;
            font-weight: bold;
            color: #333;
        }
        
        .question-number {
            color: #666;
            margin-right: 10px;
        }
        
        .score-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .answer-section {
            margin: 10px 0;
        }
        
        .answer-row {
            display: flex;
            margin: 5px 0;
        }
        
        .answer-label {
            width: 80px;
            color: #666;
            font-size: 14px;
        }
        
        .answer-content {
            flex: 1;
            color: #333;
        }
        
        .user-answer {
            color: #333;
        }
        
        .standard-answer {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .explanation {
            background: #f0f7ff;
            border: 1px solid #e1f5fe;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
        }
        
        .explanation-label {
            color: #1976d2;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .result-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 20px;
            font-weight: bold;
        }
        
        .icon-check {
            color: #4CAF50;
        }
        
        .icon-cross {
            color: #f44336;
        }
        
        .no-answer {
            color: #999;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数学试卷 - 考试结果</h1>
        </div>
        
        <div class="score-summary">
            <div class="score-display">
                <span class="earned-score" id="earnedScore">100</span>
                <span class="divider">/</span>
                <span class="total-score" id="totalScore">100</span>
            </div>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="accuracy">100%</div>
                    <div class="stat-label">正确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="correctCount">3</div>
                    <div class="stat-label">答对题数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalCount">3</div>
                    <div class="stat-label">总题数</div>
                </div>
            </div>
        </div>
        
        <div class="question-list" id="questionList">
            <!-- 题目详情将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 模拟从接口获取的详细算分结果数据
        const mockScoreDetails = {
            "questionDetails": [
                {
                    "formItemId": "radio1752048734612",
                    "questionTitle": "1. 已知函数f(x) = x² + 2x + 1，求f(2)的值是多少？",
                    "userAnswer": 3,
                    "standardAnswer": [3],
                    "userAnswerText": "9",
                    "standardAnswerText": "9",
                    "isCorrect": true,
                    "earnedScore": 30.0,
                    "totalScore": 30.0,
                    "scoringType": 1,
                    "explanation": "f(2) = 2² + 2×2 + 1 = 4 + 4 + 1 = 9"
                },
                {
                    "formItemId": "checkbox1752048737253",
                    "questionTitle": "2. x²=1",
                    "userAnswer": [1, 2],
                    "standardAnswer": [1, 2],
                    "userAnswerText": "1, -1",
                    "standardAnswerText": "1, -1",
                    "isCorrect": true,
                    "earnedScore": 20.0,
                    "totalScore": 20.0,
                    "scoringType": 1,
                    "explanation": "x²=1的解为x=±1"
                },
                {
                    "formItemId": "input1752048717109",
                    "questionTitle": "3. 解方程：2x + 5 = 15（只需填写x的值）",
                    "userAnswer": "5",
                    "standardAnswer": "5",
                    "userAnswerText": "5",
                    "standardAnswerText": "5",
                    "isCorrect": true,
                    "earnedScore": 50.0,
                    "totalScore": 50.0,
                    "scoringType": 3,
                    "explanation": "2x + 5 = 15，所以2x = 10，x = 5"
                }
            ],
            "summary": {
                "totalEarnedScore": 100.0,
                "totalPossibleScore": 100.0,
                "correctCount": 3,
                "totalCount": 3,
                "accuracy": 100.0
            }
        };

        // 渲染考试结果
        function renderExamResult(scoreDetails) {
            // 渲染汇总信息
            const summary = scoreDetails.summary;
            document.getElementById('earnedScore').textContent = summary.totalEarnedScore;
            document.getElementById('totalScore').textContent = summary.totalPossibleScore;
            document.getElementById('accuracy').textContent = summary.accuracy.toFixed(1) + '%';
            document.getElementById('correctCount').textContent = summary.correctCount;
            document.getElementById('totalCount').textContent = summary.totalCount;

            // 渲染题目详情
            const questionList = document.getElementById('questionList');
            questionList.innerHTML = '';

            scoreDetails.questionDetails.forEach((question, index) => {
                const questionItem = document.createElement('div');
                questionItem.className = `question-item ${question.isCorrect ? 'correct' : 'incorrect'}`;
                
                questionItem.innerHTML = `
                    <div class="question-header">
                        <div class="question-title">
                            <span class="question-number">第${index + 1}题</span>
                            ${question.questionTitle}
                        </div>
                        <div class="score-badge">${question.earnedScore}/${question.totalScore}分</div>
                    </div>
                    
                    <div class="answer-section">
                        <div class="answer-row">
                            <div class="answer-label">您的答案:</div>
                            <div class="answer-content user-answer">
                                ${question.userAnswerText || '<span class="no-answer">未作答</span>'}
                            </div>
                        </div>
                        <div class="answer-row">
                            <div class="answer-label">标准答案:</div>
                            <div class="answer-content standard-answer">${question.standardAnswerText}</div>
                        </div>
                    </div>
                    
                    ${question.explanation ? `
                        <div class="explanation">
                            <div class="explanation-label">解析:</div>
                            <div>${question.explanation}</div>
                        </div>
                    ` : ''}
                    
                    <div class="result-icon">
                        <span class="${question.isCorrect ? 'icon-check' : 'icon-cross'}">
                            ${question.isCorrect ? '✓' : '✗'}
                        </span>
                    </div>
                `;
                
                questionList.appendChild(questionItem);
            });
        }

        // 实际使用时的代码示例
        function loadExamResult(formDataId) {
            // 1. 调用接口获取考试数据
            // const response = await fetch('/S18M07B1/getSomeFormByUserid?userid=xxx&type=submit');
            // const data = await response.json();
            // const formData = data.data.list[0];
            
            // 2. 解析详细算分结果
            // const detailsJson = formData.custom1;
            // if (detailsJson) {
            //     const scoreDetails = JSON.parse(detailsJson);
            //     renderExamResult(scoreDetails);
            // }
            
            // 3. 模拟数据展示
            renderExamResult(mockScoreDetails);
        }

        // 页面加载时渲染结果
        document.addEventListener('DOMContentLoaded', function() {
            loadExamResult();
        });
    </script>
</body>
</html>
