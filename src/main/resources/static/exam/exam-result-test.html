<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试结果展示测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .link-button {
            background: #4CAF50;
        }
        
        .link-button:hover {
            background: #45a049;
        }
        
        .demo-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .demo-links a {
            display: inline-block;
            padding: 10px 20px;
            background: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .demo-links a:hover {
            background: #1976D2;
        }
        
        .info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .info h4 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .code {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>考试结果展示测试页面</h1>
        
        <div class="info">
            <h4>使用说明</h4>
            <p>1. 输入考试表单ID，点击"查看考试结果"按钮</p>
            <p>2. 或者使用下方的演示链接直接测试</p>
            <p>3. 确保考试表单已经有用户提交的数据</p>
        </div>
        
        <div class="test-section">
            <h3>手动测试</h3>
            <div class="form-group">
                <label for="formId">考试表单ID:</label>
                <input type="text" id="formId" placeholder="请输入考试表单ID，例如：1942805349257969664" value="1942805349257969664">
            </div>
            <button onclick="viewExamResult()">查看考试结果</button>
            <button onclick="testAPI()" class="link-button">测试API接口</button>
        </div>
        
        <div class="test-section">
            <h3>演示链接</h3>
            <div class="demo-links">
                <a href="exam-result.html?formId=1942805349257969664" target="_blank">数学试卷结果</a>
                <a href="exam-result.html" target="_blank">无参数测试</a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>API测试结果</h3>
            <div id="apiResult" class="code" style="min-height: 100px; display: none;">
                测试结果将显示在这里...
            </div>
        </div>
        
        <div class="test-section">
            <h3>URL格式说明</h3>
            <div class="code">
                考试结果页面URL格式：<br>
                /exam/exam-result.html?formId=表单ID<br><br>
                
                示例：<br>
                http://localhost:63342/inks-service-sa-table/exam/exam-result.html?formId=1942805349257969664
            </div>
        </div>
        
        <div class="test-section">
            <h3>相关页面链接</h3>
            <div class="demo-links">
                <a href="index.html">考试系统首页</a>
                <a href="question-bank-management-v2.html">题库管理</a>
                <a href="exam-form-create-v2.html">创建考试</a>
                <a href="take-exam-v2.html">参加考试</a>
            </div>
        </div>
    </div>

    <script>
        // 查看考试结果
        function viewExamResult() {
            const formId = document.getElementById('formId').value.trim();
            if (!formId) {
                alert('请输入考试表单ID');
                return;
            }
            
            const url = `exam-result.html?formId=${formId}`;
            window.open(url, '_blank');
        }
        
        // 测试API接口
        async function testAPI() {
            const formId = document.getElementById('formId').value.trim();
            if (!formId) {
                alert('请输入考试表单ID');
                return;
            }
            
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试API接口...';
            
            try {
                const response = await fetch('/S18M07B1/getSomeFormByUserid', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        "PageNum": 1,
                        "PageSize": 20,
                        "OrderType": 1,
                        "SearchType": 1,
                        "scenedata": [{
                            "field": "Sa_FormData.formid",
                            "fieldtype": 0,
                            "math": "equal",
                            "value": formId
                        }]
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200 && data.data.list.length > 0) {
                    const examRecord = data.data.list[0];
                    
                    let resultText = `API调用成功！\n\n`;
                    resultText += `表单名称: ${examRecord.formname || '未知'}\n`;
                    resultText += `总得分: ${examRecord.totalscore || 0}\n`;
                    resultText += `试卷总分: ${examRecord.sumtotalscore || 0}\n`;
                    resultText += `提交时间: ${examRecord.modifydate ? new Date(examRecord.modifydate).toLocaleString() : '未知'}\n\n`;
                    
                    if (examRecord.scoredetail) {
                        try {
                            const scoreDetails = JSON.parse(examRecord.scoredetail);
                            resultText += `详细得分信息:\n`;
                            resultText += `- 答对题数: ${scoreDetails.summary.correctCount}/${scoreDetails.summary.totalCount}\n`;
                            resultText += `- 正确率: ${scoreDetails.summary.accuracy.toFixed(1)}%\n`;
                            resultText += `- 题目详情: ${scoreDetails.questionDetails.length}题\n`;
                        } catch (e) {
                            resultText += `ScoreDetail解析失败: ${e.message}\n`;
                        }
                    } else {
                        resultText += `注意: 该记录没有ScoreDetail字段数据\n`;
                    }
                    
                    resultDiv.textContent = resultText;
                } else {
                    resultDiv.textContent = `API调用失败或无数据:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.textContent = `API调用出错: ${error.message}`;
            }
        }
        
        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('考试结果展示测试页面已加载');
            console.log('当前URL:', window.location.href);
        });
    </script>
</body>
</html>
