spring:
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  datasource:
    #MYsql连接字符串
    url: ***************************************************************************************************************************************************
    username: root
    password: asd@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    maximum-pool-size: 10
    hikari:
      connection-test-query: SELECT 1
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000


feign:
  sentinel:
    enabled: true
logging:
  level:
    org:
      springframework:
        security: info

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.table.**.domain
    #配置打印SQL语句到控制台
wechat:
  miniapp:
    config:
      #微信小程序的appid
      appid: wx65055daa7b7fb1c1
      #微信小程序的Secret
      secret: 39d1816c9de19f2e9b9d1d56086c4940
      #微信小程序消息服务器配置的token
      token: 123456
      #微信小程序消息服务器配置的EncodingAESKey
      aesKey: 123456
      msgDataFormat: JSON