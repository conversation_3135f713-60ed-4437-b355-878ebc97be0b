<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.table.mapper.SaQuestionbankitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo">
        <include refid="selectSaQuestionbankitemVo"/>
        where Sa_QuestionBankItem.id = #{key} 
    </select>
    <sql id="selectSaQuestionbankitemVo">
         select
id, Pid, ItemType, Label, Scheme, SortOrder, Status, IsDeleted, UsageCount, CorrectRate, Remark, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, Revision        from Sa_QuestionBankItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo">
        <include refid="selectSaQuestionbankitemVo"/>
        where Sa_QuestionBankItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo">
        <include refid="selectSaQuestionbankitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_QuestionBankItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_QuestionBankItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
   and Sa_QuestionBankItem.itemtype like concat('%', #{SearchPojo.itemtype}, '%')
</if>
<if test="SearchPojo.label != null and SearchPojo.label != ''">
   and Sa_QuestionBankItem.label like concat('%', #{SearchPojo.label}, '%')
</if>
<if test="SearchPojo.scheme != null and SearchPojo.scheme != ''">
   and Sa_QuestionBankItem.scheme like concat('%', #{SearchPojo.scheme}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_QuestionBankItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   and Sa_QuestionBankItem.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   and Sa_QuestionBankItem.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   and Sa_QuestionBankItem.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Sa_QuestionBankItem.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_QuestionBankItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_QuestionBankItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_QuestionBankItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_QuestionBankItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_QuestionBankItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
   and Sa_QuestionBankItem.deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_QuestionBankItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
   or Sa_QuestionBankItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
</if>
<if test="SearchPojo.label != null and SearchPojo.label != ''">
   or Sa_QuestionBankItem.Label like concat('%', #{SearchPojo.label}, '%')
</if>
<if test="SearchPojo.scheme != null and SearchPojo.scheme != ''">
   or Sa_QuestionBankItem.Scheme like concat('%', #{SearchPojo.scheme}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_QuestionBankItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or Sa_QuestionBankItem.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or Sa_QuestionBankItem.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Sa_QuestionBankItem.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Sa_QuestionBankItem.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_QuestionBankItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_QuestionBankItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_QuestionBankItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_QuestionBankItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_QuestionBankItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
   or Sa_QuestionBankItem.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_QuestionBankItem(id, Pid, ItemType, Label, Scheme, SortOrder, Status, IsDeleted, UsageCount, CorrectRate, Remark, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemtype}, #{label}, #{scheme}, #{sortorder}, #{status}, #{isdeleted}, #{usagecount}, #{correctrate}, #{remark}, #{rownum}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{deptid}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_QuestionBankItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemtype != null ">
                ItemType = #{itemtype},
            </if>
            <if test="label != null ">
                Label = #{label},
            </if>
            <if test="scheme != null ">
                Scheme = #{scheme},
            </if>
            <if test="sortorder != null">
                SortOrder = #{sortorder},
            </if>
            <if test="status != null">
                Status = #{status},
            </if>
            <if test="isdeleted != null">
                IsDeleted = #{isdeleted},
            </if>
            <if test="usagecount != null">
                UsageCount = #{usagecount},
            </if>
            <if test="correctrate != null">
                CorrectRate = #{correctrate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="createbyid != null ">
                CreateByid = #{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy = #{createby},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="listerid != null ">
                Listerid = #{listerid},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="deptid != null ">
                Deptid = #{deptid},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_QuestionBankItem where id = #{key}
    </delete>

    <!--根据题目ID和题库ID获取题目信息-->
    <select id="getByQuestionItemId" resultType="inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo">
        <include refid="selectSaQuestionbankitemVo"/>
        where Sa_QuestionBankItem.id = #{questionItemId}
        and Sa_QuestionBankItem.Pid = #{questionBankId}
    </select>

</mapper>

