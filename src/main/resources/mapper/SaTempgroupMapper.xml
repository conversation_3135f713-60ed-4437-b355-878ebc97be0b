<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.table.mapper.SaTempgroupMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.table.domain.pojo.SaTempgroupPojo">
        select
          id, GroupName, RowNum, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_TempGroup
        where Sa_TempGroup.id = #{key} 
    </select>
    <sql id="selectSaTempgroupVo">
         select
          id, GroupName, RowNum, Remark, CreateByid, <PERSON>reate<PERSON><PERSON>, <PERSON>reate<PERSON>ate, <PERSON><PERSON><PERSON>, <PERSON>er, <PERSON>difyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_TempGroup
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.table.domain.pojo.SaTempgroupPojo">
        <include refid="selectSaTempgroupVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_TempGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.groupname != null ">
   and Sa_TempGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_TempGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_TempGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_TempGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_TempGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_TempGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_TempGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_TempGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_TempGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_TempGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_TempGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.groupname != null ">
   or Sa_TempGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_TempGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_TempGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_TempGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_TempGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_TempGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_TempGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_TempGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_TempGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_TempGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_TempGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_TempGroup(id, GroupName, RowNum, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{groupname}, #{rownum}, #{remark}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_TempGroup
        <set>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_TempGroup where id = #{key} 
    </delete>
                                                                    </mapper>

