<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.crm.mapper.SaPersonMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.crm.domain.pojo.SaPersonPojo">
        <include refid="selectSaPersonVo"/>
        where Sa_Person.id = #{key}
    </select>
    <sql id="selectSaPersonVo">
        select Sa_Person.id,
               Sa_Person.Groupid,
               Sa_Person.PersonName,
               Sa_Person.Telephone,
               Sa_Person.Mobile,
               Sa_Person.Position,
               Sa_Person.Wechat,
               Sa_Person.Email,
               Sa_Person.DecisionMark,
               Sa_Person.Operatorid,
               Sa_Person.Operator,
               Sa_Person.DepartName,
               Sa_Person.EnabledMark,
               Sa_Person.StateNum,
               Sa_Person.StateDate,
               Sa_Person.RowNum,
               Sa_Person.Remark,
               Sa_Person.CreateBy,
               Sa_Person.CreateByid,
               Sa_Person.CreateDate,
               Sa_Person.Lister,
               Sa_Person.Listerid,
               Sa_Person.ModifyDate,
               Sa_Person.Custom1,
               Sa_Person.Custom2,
               Sa_Person.Custom3,
               Sa_Person.Custom4,
               Sa_Person.Custom5,
               Sa_Person.Custom6,
               Sa_Person.Custom7,
               Sa_Person.Custom8,
               Sa_Person.Deptid,
               Sa_Person.Tenantid,
               Sa_Person.TenantName,
               Sa_Person.Revision,
               Sa_Customer.CustName,
               Sa_Customer.CustName AS GroupName
        from Sa_Person
                 LEFT JOIN Sa_Customer ON Sa_Person.Groupid = Sa_Customer.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.crm.domain.pojo.SaPersonPojo">
        <include refid="selectSaPersonVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Sa_Person.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.groupid != null ">
            and Sa_Person.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.personname != null ">
            and Sa_Person.PersonName like concat('%', #{SearchPojo.personname}, '%')
        </if>
        <if test="SearchPojo.telephone != null ">
            and Sa_Person.Telephone like concat('%', #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.mobile != null ">
            and Sa_Person.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.position != null ">
            and Sa_Person.Position like concat('%', #{SearchPojo.position}, '%')
        </if>
        <if test="SearchPojo.wechat != null ">
            and Sa_Person.Wechat like concat('%', #{SearchPojo.wechat}, '%')
        </if>
        <if test="SearchPojo.email != null ">
            and Sa_Person.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Sa_Person.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Sa_Person.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.departname != null ">
            and Sa_Person.DepartName like concat('%', #{SearchPojo.departname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Sa_Person.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Sa_Person.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Sa_Person.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Sa_Person.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Sa_Person.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Sa_Person.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Sa_Person.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Sa_Person.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Sa_Person.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Sa_Person.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Sa_Person.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Sa_Person.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Sa_Person.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Sa_Person.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Sa_Person.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.groupid != null ">
                or Sa_Person.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.personname != null ">
                or Sa_Person.PersonName like concat('%', #{SearchPojo.personname}, '%')
            </if>
            <if test="SearchPojo.telephone != null ">
                or Sa_Person.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.mobile != null ">
                or Sa_Person.Mobile like concat('%', #{SearchPojo.mobile}, '%')
            </if>
            <if test="SearchPojo.position != null ">
                or Sa_Person.Position like concat('%', #{SearchPojo.position}, '%')
            </if>
            <if test="SearchPojo.wechat != null ">
                or Sa_Person.Wechat like concat('%', #{SearchPojo.wechat}, '%')
            </if>
            <if test="SearchPojo.email != null ">
                or Sa_Person.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Sa_Person.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Sa_Person.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.departname != null ">
                or Sa_Person.DepartName like concat('%', #{SearchPojo.departname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Sa_Person.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Sa_Person.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Sa_Person.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Sa_Person.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Sa_Person.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Sa_Person.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Sa_Person.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Sa_Person.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Sa_Person.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Sa_Person.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Sa_Person.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Sa_Person.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Sa_Person.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Sa_Person.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Sa_Person.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Person(id, Groupid, PersonName, Telephone, Mobile, Position, Wechat, Email, DecisionMark,
                              Operatorid, Operator, DepartName, EnabledMark, StateNum, StateDate, RowNum, Remark,
                              CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3,
                              Custom4, Custom5, Custom6, Custom7, Custom8, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{groupid}, #{personname}, #{telephone}, #{mobile}, #{position}, #{wechat}, #{email},
                #{decisionmark}, #{operatorid}, #{operator}, #{departname}, #{enabledmark}, #{statenum}, #{statedate},
                #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Person
        <set>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="personname != null ">
                PersonName =#{personname},
            </if>
            <if test="telephone != null ">
                Telephone =#{telephone},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="position != null ">
                Position =#{position},
            </if>
            <if test="wechat != null ">
                Wechat =#{wechat},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="decisionmark != null">
                DecisionMark =#{decisionmark},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="departname != null ">
                DepartName =#{departname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="statenum != null">
                StateNum =#{statenum},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Person
        where id = #{key}
    </delete>
    <!--    按线索id查询联系人列表-->
    <select id="getListByLeads" resultType="inks.service.sa.crm.domain.pojo.SaPersonPojo">
        <include refid="selectSaPersonVo"/>
        where id in (select Personid from Sa_PersonLeads where Leadsid = #{key})
    </select>
    <select id="getListByGroup" resultType="inks.service.sa.crm.domain.pojo.SaPersonPojo">
        <include refid="selectSaPersonVo"/>
        where id in (select Personid from Sa_PersonGroup where Groupid = #{key})
    </select>

</mapper>

