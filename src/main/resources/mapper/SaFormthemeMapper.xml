<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.table.mapper.SaFormthemeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.table.domain.pojo.SaFormthemePojo">
        select
          id, Name, Style, HeadImgUrl, BackgroundImg, ThemeColor, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_FormTheme
        where Sa_FormTheme.id = #{key} 
    </select>
    <sql id="selectSaFormthemeVo">
         select
          id, Name, Style, HeadImgUrl, BackgroundImg, ThemeColor, Remark, Create<PERSON>yid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ate, <PERSON><PERSON><PERSON>, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_FormTheme
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.table.domain.pojo.SaFormthemePojo">
        <include refid="selectSaFormthemeVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_FormTheme.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.name != null ">
   and Sa_FormTheme.Name like concat('%', #{SearchPojo.name}, '%')
</if>
<if test="SearchPojo.headimgurl != null ">
   and Sa_FormTheme.HeadImgUrl like concat('%', #{SearchPojo.headimgurl}, '%')
</if>
<if test="SearchPojo.backgroundimg != null ">
   and Sa_FormTheme.BackgroundImg like concat('%', #{SearchPojo.backgroundimg}, '%')
</if>
<if test="SearchPojo.themecolor != null ">
   and Sa_FormTheme.ThemeColor like concat('%', #{SearchPojo.themecolor}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_FormTheme.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_FormTheme.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_FormTheme.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_FormTheme.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_FormTheme.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_FormTheme.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_FormTheme.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_FormTheme.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_FormTheme.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_FormTheme.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.name != null ">
   or Sa_FormTheme.Name like concat('%', #{SearchPojo.name}, '%')
</if>
<if test="SearchPojo.headimgurl != null ">
   or Sa_FormTheme.HeadImgUrl like concat('%', #{SearchPojo.headimgurl}, '%')
</if>
<if test="SearchPojo.backgroundimg != null ">
   or Sa_FormTheme.BackgroundImg like concat('%', #{SearchPojo.backgroundimg}, '%')
</if>
<if test="SearchPojo.themecolor != null ">
   or Sa_FormTheme.ThemeColor like concat('%', #{SearchPojo.themecolor}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_FormTheme.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_FormTheme.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_FormTheme.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_FormTheme.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_FormTheme.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_FormTheme.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_FormTheme.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_FormTheme.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_FormTheme.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_FormTheme.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_FormTheme(id, Name, Style, HeadImgUrl, BackgroundImg, ThemeColor, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{name}, #{style}, #{headimgurl}, #{backgroundimg}, #{themecolor}, #{remark}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_FormTheme
        <set>
            <if test="name != null ">
                Name =#{name},
            </if>
            <if test="style != null">
                Style =#{style},
            </if>
            <if test="headimgurl != null ">
                HeadImgUrl =#{headimgurl},
            </if>
            <if test="backgroundimg != null ">
                BackgroundImg =#{backgroundimg},
            </if>
            <if test="themecolor != null ">
                ThemeColor =#{themecolor},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_FormTheme where id = #{key} 
    </delete>
                                                                                </mapper>

