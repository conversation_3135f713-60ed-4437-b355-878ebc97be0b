<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.table.mapper.SaFormitemMapper">

    <!--查询单个-->
    <sql id="Base_Column_List">
        id,
        pid,
        formitemid,
        `type`,
        `label`,
        textlabel,
        isdisplaytype,
        ishidetype,
        isspecialtype,
        showlabel,
        defaultvalue,
        required,
        placeholder,
        sort,
        span,
        scheme,
        reglist,
        custom1,
        custom2,
        custom3,
        custom4,
        custom5,
        tenantid,
        revision
    </sql>
    <resultMap id="BaseResultMap" type="inks.service.sa.table.domain.SaFormitemEntity">
        <result column="id" property="id"/>
        <result column="pid" property="pid"/>
        <result column="formitemid" property="formitemid"/>
        <result column="type" property="type"/>
        <result column="label" property="label"/>
        <result column="textlabel" property="textlabel"/>
        <result column="isdisplaytype" property="isdisplaytype"/>
        <result column="ishidetype" property="ishidetype"/>
        <result column="isspecialtype" property="isspecialtype"/>
        <result column="showlabel" property="showlabel"/>
        <result column="defaultvalue" property="defaultvalue"/>
        <result column="required" property="required"/>
        <result column="placeholder" property="placeholder"/>
        <result column="sort" property="sort"/>
        <result column="span" property="span"/>
        <result column="scheme" property="scheme"/>
        <result column="reglist" property="reglist"/>
        <result column="custom1" property="custom1"/>
        <result column="custom2" property="custom2"/>
        <result column="custom3" property="custom3"/>
        <result column="custom4" property="custom4"/>
        <result column="custom5" property="custom5"/>
        <result column="tenantid" property="tenantid"/>
        <result column="revision" property="revision"/>
    </resultMap>
    <select id="getEntity" resultType="inks.service.sa.table.domain.pojo.SaFormitemPojo">
        select
          id, Pid, FormItemId, Type, Label, TextLabel, IsDisplayType, IsHideType, IsSpecialType, ShowLabel, DefaultValue, Required, Placeholder, Sort, Span, Scheme, RegList,ShowColumn, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_FormItem
        where Sa_FormItem.id = #{key}
    </select>
    <sql id="selectSaFormitemVo">
         select
          id, Pid, FormItemId, Type, Label, TextLabel, IsDisplayType, IsHideType, IsSpecialType, ShowLabel, DefaultValue, Required, Placeholder, Sort, Span, Scheme, RegList,ShowColumn, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_FormItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.table.domain.pojo.SaFormitemPojo">
        <include refid="selectSaFormitemVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_FormItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_FormItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.formitemid != null and SearchPojo.formitemid != ''">
   and Sa_FormItem.formitemid like concat('%', #{SearchPojo.formitemid}, '%')
</if>
<if test="SearchPojo.type != null and SearchPojo.type != ''">
   and Sa_FormItem.type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.label != null and SearchPojo.label != ''">
   and Sa_FormItem.label like concat('%', #{SearchPojo.label}, '%')
</if>
<if test="SearchPojo.textlabel != null and SearchPojo.textlabel != ''">
   and Sa_FormItem.textlabel like concat('%', #{SearchPojo.textlabel}, '%')
</if>
<if test="SearchPojo.defaultvalue != null and SearchPojo.defaultvalue != ''">
   and Sa_FormItem.defaultvalue like concat('%', #{SearchPojo.defaultvalue}, '%')
</if>
<if test="SearchPojo.placeholder != null and SearchPojo.placeholder != ''">
   and Sa_FormItem.placeholder like concat('%', #{SearchPojo.placeholder}, '%')
</if>
<if test="SearchPojo.scheme != null and SearchPojo.scheme != ''">
   and Sa_FormItem.scheme like concat('%', #{SearchPojo.scheme}, '%')
</if>
<if test="SearchPojo.reglist != null and SearchPojo.reglist != ''">
   and Sa_FormItem.reglist like concat('%', #{SearchPojo.reglist}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_FormItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_FormItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_FormItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_FormItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_FormItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_FormItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.formitemid != null and SearchPojo.formitemid != ''">
   or Sa_FormItem.FormItemId like concat('%', #{SearchPojo.formitemid}, '%')
</if>
<if test="SearchPojo.type != null and SearchPojo.type != ''">
   or Sa_FormItem.Type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.label != null and SearchPojo.label != ''">
   or Sa_FormItem.Label like concat('%', #{SearchPojo.label}, '%')
</if>
<if test="SearchPojo.textlabel != null and SearchPojo.textlabel != ''">
   or Sa_FormItem.TextLabel like concat('%', #{SearchPojo.textlabel}, '%')
</if>
<if test="SearchPojo.defaultvalue != null and SearchPojo.defaultvalue != ''">
   or Sa_FormItem.DefaultValue like concat('%', #{SearchPojo.defaultvalue}, '%')
</if>
<if test="SearchPojo.placeholder != null and SearchPojo.placeholder != ''">
   or Sa_FormItem.Placeholder like concat('%', #{SearchPojo.placeholder}, '%')
</if>
<if test="SearchPojo.scheme != null and SearchPojo.scheme != ''">
   or Sa_FormItem.Scheme like concat('%', #{SearchPojo.scheme}, '%')
</if>
<if test="SearchPojo.reglist != null and SearchPojo.reglist != ''">
   or Sa_FormItem.RegList like concat('%', #{SearchPojo.reglist}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_FormItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_FormItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_FormItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_FormItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_FormItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.table.domain.pojo.SaFormitemPojo">
        select
          id, Pid, FormItemId, Type, Label, TextLabel, IsDisplayType, IsHideType, IsSpecialType, ShowLabel, DefaultValue, Required, Placeholder, Sort, Span, Scheme, RegList,ShowColumn, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_FormItem
        where Sa_FormItem.Pid = #{Pid}
        order by Sort
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_FormItem(id, Pid, FormItemId, Type, Label, TextLabel, IsDisplayType, IsHideType, IsSpecialType, ShowLabel, DefaultValue, Required, Placeholder, Sort, Span, Scheme, RegList,ShowColumn,  Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{formitemid}, #{type}, #{label}, #{textlabel}, #{isdisplaytype}, #{ishidetype}, #{isspecialtype}, #{showlabel}, #{defaultvalue}, #{required}, #{placeholder}, #{sort}, #{span}, #{scheme}, #{reglist},
                #{showcolumn},  #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_FormItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="formitemid != null ">
                FormItemId = #{formitemid},
            </if>
            <if test="type != null ">
                Type = #{type},
            </if>
            <if test="label != null ">
                Label = #{label},
            </if>
            <if test="textlabel != null ">
                TextLabel = #{textlabel},
            </if>
            <if test="isdisplaytype != null">
                IsDisplayType = #{isdisplaytype},
            </if>
            <if test="ishidetype != null">
                IsHideType = #{ishidetype},
            </if>
            <if test="isspecialtype != null">
                IsSpecialType = #{isspecialtype},
            </if>
            <if test="showlabel != null">
                ShowLabel = #{showlabel},
            </if>
            <if test="defaultvalue != null ">
                DefaultValue = #{defaultvalue},
            </if>
            <if test="required != null">
                Required = #{required},
            </if>
            <if test="placeholder != null ">
                Placeholder = #{placeholder},
            </if>
            <if test="sort != null">
                Sort = #{sort},
            </if>
            <if test="span != null">
                Span = #{span},
            </if>
            <if test="scheme != null ">
                Scheme = #{scheme},
            </if>
            <if test="reglist != null ">
                RegList = #{reglist},
            </if>
            <if test="showcolumn != null ">
                ShowColumn = #{showcolumn},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_FormItem where id = #{key}
    </delete>

    <select id="getLastItemSort" resultType="java.lang.Long">
        select max(Sort) from Sa_FormItem where Pid = #{formKey}
    </select>

    <select id="getByFormItemId"  resultType="inks.service.sa.table.domain.pojo.SaFormitemPojo">
        select * from Sa_FormItem
        where Sa_FormItem.FormItemId = #{formItemId}
        and Sa_FormItem.Pid=#{formId}
    </select>

    <select id="getSchemeByFormItemId" resultType="java.lang.String">
        select Scheme from Sa_FormItem
        where Sa_FormItem.FormItemId = #{formItemId}
        and Sa_FormItem.Pid=#{formId}
    </select>
</mapper>

