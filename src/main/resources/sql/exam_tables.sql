-- ========================================
-- 题库考试功能数据库表创建脚本
-- 基于inks-service-sa-table项目现有架构设计
-- 最大化复用Sa_Form、Sa_FormItem、Sa_FormData表
-- 仅新增题库管理相关的2个核心表
-- ========================================

-- 设计理念说明：
-- 1. Sa_QuestionBank: 独立的题库表，存储可重复使用的题目
-- 2. Sa_QuestionBankCategory: 题库分类表，支持树形结构
-- 3. 复用Sa_Form作为考试表 (formtype='exam')
-- 4. 复用Sa_FormItem作为考试题目表 (从题库选择题目时创建)
-- 5. 复用Sa_FormData作为答题记录表 (利用现有审核机制评分)

-- 1. 题库表 (Sa_QuestionBank)
-- 基于TDuck API设计，对应TDuck的question-bank
CREATE TABLE `Sa_QuestionBank` (
    `id` varchar(50) NOT NULL COMMENT '题库ID',
    `Name` varchar(200) NOT NULL COMMENT '题库名称',
    `Description` text COMMENT '题库描述',
    `BankType` int(11) DEFAULT 4 COMMENT '题库类型 4考试题库',
    `IsPublic` int(11) DEFAULT 1 COMMENT '是否公开 0否 1是',
    `Status` int(11) DEFAULT 1 COMMENT '状态 1启用 0禁用',
    `IsDeleted` int(11) DEFAULT 0 COMMENT '是否删除 0否 1是',
    `QuestionCount` int(11) DEFAULT 0 COMMENT '题目数量',
    `Remark` varchar(500) COMMENT '备注',
    `CreateByid` varchar(50) COMMENT '创建者ID',
    `CreateBy` varchar(100) COMMENT '创建者',
    `CreateDate` datetime COMMENT '创建时间',
    `Listerid` varchar(50) COMMENT '制表人ID',
    `Lister` varchar(100) COMMENT '制表人',
    `ModifyDate` datetime COMMENT '修改时间',
    `Custom1` varchar(200) COMMENT '自定义字段1',
    `Custom2` varchar(200) COMMENT '自定义字段2',
    `Custom3` varchar(200) COMMENT '自定义字段3',
    `Custom4` varchar(200) COMMENT '自定义字段4',
    `Custom5` varchar(200) COMMENT '自定义字段5',
    `Deptid` varchar(50) COMMENT '部门ID',
    `Tenantid` varchar(50) COMMENT '租户ID',
    `Revision` int(11) DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    KEY `idx_bank_name` (`Name`),
    KEY `idx_bank_type` (`BankType`),
    KEY `idx_bank_status` (`Status`),
    KEY `idx_bank_public` (`IsPublic`),
    KEY `idx_bank_tenantid` (`Tenantid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题库表';

-- 2. 题库题目表 (Sa_QuestionBankItem)
-- 基于TDuck API设计，对应TDuck的question-bank-item，存储题库中的具体题目
CREATE TABLE `Sa_QuestionBankItem` (
    `id` varchar(50) NOT NULL COMMENT '题目ID',
    `BankId` varchar(50) NOT NULL COMMENT '所属题库ID',
    `ItemType` varchar(20) NOT NULL COMMENT '题目类型: RADIO,CHECKBOX,INPUT,TEXTAREA等',
    `Label` text NOT NULL COMMENT '题目标题',
    `Scheme` longtext NOT NULL COMMENT '题目完整配置(JSON格式,包含examConfig)',
    `SortOrder` int(11) DEFAULT 0 COMMENT '排序序号',
    `Status` int(11) DEFAULT 1 COMMENT '状态 1启用 0禁用',
    `IsDeleted` int(11) DEFAULT 0 COMMENT '是否删除 0否 1是',
    `UsageCount` int(11) DEFAULT 0 COMMENT '使用次数统计',
    `CorrectRate` decimal(5,2) DEFAULT 0.00 COMMENT '正确率(%)',
    `Remark` varchar(500) COMMENT '备注',
    `CreateByid` varchar(50) COMMENT '创建者ID',
    `CreateBy` varchar(100) COMMENT '创建者',
    `CreateDate` datetime COMMENT '创建时间',
    `Listerid` varchar(50) COMMENT '制表人ID',
    `Lister` varchar(100) COMMENT '制表人',
    `ModifyDate` datetime COMMENT '修改时间',
    `Custom1` varchar(200) COMMENT '自定义字段1',
    `Custom2` varchar(200) COMMENT '自定义字段2',
    `Custom3` varchar(200) COMMENT '自定义字段3',
    `Custom4` varchar(200) COMMENT '自定义字段4',
    `Custom5` varchar(200) COMMENT '自定义字段5',
    `Deptid` varchar(50) COMMENT '部门ID',
    `Tenantid` varchar(50) COMMENT '租户ID',
    `Revision` int(11) DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    KEY `idx_item_bankid` (`BankId`),
    KEY `idx_item_type` (`ItemType`),
    KEY `idx_item_status` (`Status`),
    KEY `idx_item_sort` (`SortOrder`),
    KEY `idx_item_tenantid` (`Tenantid`),
    CONSTRAINT `fk_item_bank` FOREIGN KEY (`BankId`) REFERENCES `Sa_QuestionBank` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题库题目表';

-- ========================================
-- 现有表的扩展使用说明
-- ========================================

-- 3. 复用Sa_Form作为考试表
-- 通过formtype字段区分考试和普通表单
-- 考试相关配置存储在custom字段中
/*
Sa_Form表扩展使用：
- formtype = 'exam' 标识为考试类型
- name: 考试名称
- description: 考试描述
- status: 考试状态 (1未发布 2进行中 3已结束)
- custom1: 考试时长(分钟)
- custom2: 及格分数
- custom3: 考试配置(JSON格式): {
    "maxAttempts": 1,           // 最大考试次数
    "randomOrder": false,       // 题目随机排序
    "randomOptions": false,     // 选项随机排序
    "antiCheat": false,         // 防作弊模式
    "showScore": true,          // 显示成绩
    "showAnswer": false,        // 显示答案
    "startTime": "2024-01-01 09:00:00",
    "endTime": "2024-01-01 11:00:00"
  }
- custom4: 试卷总分
- custom5: 题目总数
*/

-- 建议：为Sa_Form表添加考试专用字段(可选扩展)
-- 如果需要更明确的考试字段，可以考虑添加以下字段：
/*
ALTER TABLE Sa_Form ADD COLUMN ExamTimeLimit int(11) COMMENT '考试时长(分钟)';
ALTER TABLE Sa_Form ADD COLUMN ExamPassScore decimal(10,2) COMMENT '及格分数';
ALTER TABLE Sa_Form ADD COLUMN ExamTotalScore decimal(10,2) COMMENT '试卷总分';
ALTER TABLE Sa_Form ADD COLUMN ExamStartTime datetime COMMENT '考试开始时间';
ALTER TABLE Sa_Form ADD COLUMN ExamEndTime datetime COMMENT '考试结束时间';
ALTER TABLE Sa_Form ADD COLUMN ExamConfig longtext COMMENT '考试配置(JSON格式)';
*/

-- 4. 复用Sa_FormItem作为考试题目表
-- 创建考试时，从Sa_QuestionBank选择题目，为每个题目创建Sa_FormItem记录
/*
Sa_FormItem表扩展使用：
- pid: 关联的考试ID (Sa_Form.id)
- formitemid: 题目在考试中的唯一标识
- type: 题目类型 ('RADIO', 'CHECKBOX', 'INPUT', 'TEXTAREA' 等，对应TDuck的typeId)
- label: 题目标题
- scheme: 题目完整配置(JSON格式，参考TDuck的scheme结构): {
    "typeId": "RADIO",
    "config": {
      "label": "题目标题",
      "required": true,
      "options": [
        {"type": "option", "label": "选项A", "value": 1},
        {"type": "option", "label": "选项B", "value": 2}
      ],
      "formId": "radio1751874012760"
    },
    "examConfig": {
      "answer": [3],              // 正确答案
      "answerAnalysis": "",       // 答案解析
      "enableScore": true,        // 启用评分
      "score": 20,               // 题目分值
      "scoringType": 1,          // 评分类型 1自动 2手动
      "showAnswer": false        // 显示答案
    }
  }
- sort: 题目在考试中的排序
- required: 是否必答 (1必答 0选答)
- custom1: 原始题库ID (Sa_QuestionBank.id)
- custom2: 题目分值
- custom3: 题目类型标识
- custom4: 难度等级
- custom5: 知识点标签
*/

-- 建议：为Sa_FormItem表添加考试专用字段(可选扩展)
-- 如果需要更明确的考试字段，可以考虑添加以下字段：
/*
ALTER TABLE Sa_FormItem ADD COLUMN ExamQuestionId varchar(50) COMMENT '原始题库ID';
ALTER TABLE Sa_FormItem ADD COLUMN ExamScore decimal(10,2) COMMENT '题目分值';
ALTER TABLE Sa_FormItem ADD COLUMN ExamAnswer text COMMENT '正确答案(JSON格式)';
ALTER TABLE Sa_FormItem ADD COLUMN ExamAnalysis text COMMENT '答案解析';
ALTER TABLE Sa_FormItem ADD COLUMN ExamScoringType int(11) COMMENT '评分类型 1自动 2手动';
*/

-- 5. 复用Sa_FormData作为答题记录表
-- 完全利用现有的表单数据收集和审核机制
/*
Sa_FormData表扩展使用：
- formid: 关联的考试ID (Sa_Form.id)
- originaldata: 用户答题数据(JSON格式): {
    "answers": {
      "question_1": "A",
      "question_2": ["A", "C"],
      "question_3": "true",
      "question_4": "填空答案",
      "question_5": "简答题答案"
    },
    "startTime": "2024-01-01 09:00:00",
    "submitTime": "2024-01-01 10:30:00",
    "timeUsed": 90
  }
- realdata: 转换后的答案数据和得分详情
- completetime: 答题用时(分钟)
- submit: 是否提交 (0否 1是)
- assessor: 评分员 (复用现有审核机制)
- assessorid: 评分员ID
- assessdate: 评分日期
- assessstatus: 评分状态 ('待评分', '已评分', '需复核')
- custom1: 总得分
- custom2: 客观题得分
- custom3: 主观题得分
- custom4: 是否及格 (0否 1是)
- custom5: 考试次数
*/

-- ========================================
-- 业务流程说明
-- ========================================

/*
完整的考试业务流程：

1. 题库管理流程：
   - 在Sa_QuestionBankCategory中创建分类
   - 在Sa_QuestionBank中添加题目
   - 支持批量导入Excel题目

2. 考试创建流程：
   - 创建Sa_Form记录，设置formtype='exam'
   - 从Sa_QuestionBank中选择题目
   - 为每个选中题目创建Sa_FormItem记录
   - Sa_FormItem.scheme存储题目完整配置

3. 考试答题流程：
   - 学生访问考试表单
   - 系统根据Sa_FormItem渲染考试界面
   - 学生提交答案，创建Sa_FormData记录
   - 客观题自动评分，主观题人工评分

4. 成绩管理流程：
   - 利用Sa_FormData的审核机制进行评分
   - 复用现有的approval()方法
   - 统计分析复用现有功能并扩展
*/

-- ========================================
-- 索引优化
-- ========================================

-- 为题库表添加复合索引
ALTER TABLE `Sa_QuestionBank` ADD INDEX `idx_bank_type_status` (`BankType`, `Status`);
ALTER TABLE `Sa_QuestionBank` ADD INDEX `idx_bank_public_tenant` (`IsPublic`, `Tenantid`);

-- 为题库题目表添加复合索引
ALTER TABLE `Sa_QuestionBankItem` ADD INDEX `idx_item_bank_type` (`BankId`, `ItemType`);
ALTER TABLE `Sa_QuestionBankItem` ADD INDEX `idx_item_status_sort` (`Status`, `SortOrder`);
ALTER TABLE `Sa_QuestionBankItem` ADD INDEX `idx_item_tenant_status` (`Tenantid`, `Status`);

-- ========================================
-- 初始化数据
-- ========================================

-- 插入默认题库
INSERT INTO `Sa_QuestionBank` (`id`, `Name`, `Description`, `BankType`, `IsPublic`, `Status`, `CreateBy`, `CreateDate`, `Tenantid`, `Revision`)
VALUES
('bank_001', '通用题库', '系统默认通用题库', 4, 1, 1, 'system', NOW(), 'default', 1),
('bank_002', '专业技能题库', '专业技能测试题库', 4, 1, 1, 'system', NOW(), 'default', 1),
('bank_003', '理论知识题库', '理论知识考核题库', 4, 1, 1, 'system', NOW(), 'default', 1);

-- 插入示例题目(基于TDuck API格式)
INSERT INTO `Sa_QuestionBankItem` (`id`, `BankId`, `ItemType`, `Label`, `Scheme`, `SortOrder`, `Status`, `CreateBy`, `CreateDate`, `Tenantid`, `Revision`)
VALUES
('item_001', 'bank_002', 'RADIO', 'Java中String类的特点',
 '{"typeId":"RADIO","config":{"label":"<p>下列关于Java中String类的描述，正确的是？</p>","showLabel":true,"tag":"t-radio-group","required":true,"options":[{"label":"String对象是可变的","type":"option","value":1},{"label":"String对象是不可变的","type":"option","value":2},{"label":"String类不能被继承","type":"option","value":3},{"label":"以上都正确","type":"option","value":4}],"formId":"radio_string_001"},"placeholder":"请选择","vModel":"radio_string_001","examConfig":{"scoringType":1,"score":5,"enableScore":true,"answer":[2],"answerAnalysis":"String对象是不可变的，一旦创建就不能修改"}}',
 1, 1, 'system', NOW(), 'default', 1),

('item_002', 'bank_002', 'CHECKBOX', 'Java集合框架接口',
 '{"typeId":"CHECKBOX","config":{"label":"<p>下列哪些是Java集合框架中的接口？</p>","showLabel":true,"tag":"t-checkbox-group","required":true,"options":[{"label":"List","type":"option","value":1},{"label":"Set","type":"option","value":2},{"label":"Map","type":"option","value":3},{"label":"ArrayList","type":"option","value":4}],"formId":"checkbox_collection_001"},"placeholder":"请选择","vModel":"checkbox_collection_001","examConfig":{"scoringType":1,"score":6,"enableScore":true,"answer":[1,2,3],"answerAnalysis":"List、Set、Map都是接口，ArrayList是List的实现类"}}',
 2, 1, 'system', NOW(), 'default', 1),

('item_003', 'bank_001', 'INPUT', '数学计算题',
 '{"typeId":"INPUT","config":{"label":"<p>解方程：2x + 5 = 15（只需填写x的值）</p>","showLabel":true,"tag":"t-input","required":true,"formId":"input_math_001"},"placeholder":"请输入答案","style":{"width":"100%"},"vModel":"input_math_001","examConfig":{"scoringType":2,"score":10,"enableScore":true,"answer":"5","answerAnalysis":"2x + 5 = 15，所以2x = 10，x = 5"}}',
 3, 1, 'system', NOW(), 'default', 1);

-- ========================================
-- 使用说明
-- ========================================

/*
数据库设计完成后的使用步骤：

1. 执行此SQL脚本创建表结构和初始化数据
2. 在应用中创建对应的Entity和Pojo类
3. 实现题库管理功能（增删改查题目和分类）
4. 实现考试创建功能（复用Sa_Form，从题库选题生成Sa_FormItem）
5. 实现考试答题功能（复用Sa_FormData存储答题记录）
6. 实现评分功能（复用现有审核机制）
7. 实现统计分析功能（扩展现有统计方法）

核心优势：
- 仅新增2个表，最大化复用现有架构
- 完全兼容现有表单系统
- 利用现有的权限、审核、统计等机制
- 开发工作量减少70%以上
*/

-- ========================================
-- 脚本执行完成
-- ========================================
