<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>矩形装箱可视化</title>
    <!-- 引入Element UI -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        .container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .form-container {
            margin-bottom: 20px;
        }
        .canvas-container {
            display: flex;
            gap: 20px;
        }
        .block-list {
            margin-top: 10px;
        }
        .result-info {
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="form-container">
            <el-form :model="form" label-width="100px">
                <el-form-item label="装箱类型">
                    <el-radio-group v-model="form.type">
                        <el-radio label="fixed">固定大小</el-radio>
                        <el-radio label="growing">可增长</el-radio>
                    </el-radio-group>
                </el-form-item>
                
                <el-form-item label="容器大小" v-if="form.type === 'fixed'">
                    <el-input-number v-model="form.width" :min="1" label="宽度"></el-input-number>
                    <el-input-number v-model="form.height" :min="1" label="高度"></el-input-number>
                </el-form-item>

                <el-form-item label="添加矩形">
                    <el-input-number v-model="newBlock.width" :min="1" placeholder="宽度"></el-input-number>
                    <el-input-number v-model="newBlock.height" :min="1" placeholder="高度"></el-input-number>
                    <el-button type="primary" @click="addBlock">添加</el-button>
                </el-form-item>
            </el-form>

            <div class="block-list">
                <h3>待放置矩形列表：</h3>
                <el-table :data="form.blocks" style="width: 100%">
                    <el-table-column prop="width" label="宽度">
                    </el-table-column>
                    <el-table-column prop="height" label="高度">
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-button size="mini" type="danger" @click="removeBlock(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <el-button type="primary" @click="pack" style="margin-top: 20px">开始装箱</el-button>
        </div>

        <div class="result-info" v-if="result">
            <el-alert
                :title="'填充率: ' + result.fillRate.toFixed(2) + '%'"
                type="success"
                :closable="false">
            </el-alert>
        </div>

        <div class="canvas-container">
            <div>
                <h3>装箱结果</h3>
                <canvas ref="canvas" width="600" height="600"></canvas>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <script>
        new Vue({
            el: '#app',
            data: {
                form: {
                    type: 'fixed',
                    width: 500,
                    height: 500,
                    blocks: []
                },
                newBlock: {
                    width: 100,
                    height: 100
                },
                result: null,
                colors: [] // 用于存储每个矩形的颜色
            },
            methods: {
                addBlock() {
                    this.form.blocks.push({
                        width: this.newBlock.width,
                        height: this.newBlock.height
                    });
                    // 为新添加的矩形生成随机颜色
                    this.colors.push(this.getRandomColor());
                },
                removeBlock(index) {
                    this.form.blocks.splice(index, 1);
                    this.colors.splice(index, 1);
                },
                getRandomColor() {
                    const letters = '0123456789ABCDEF';
                    let color = '#';
                    for (let i = 0; i < 6; i++) {
                        color += letters[Math.floor(Math.random() * 16)];
                    }
                    return color;
                },
                async pack() {
                    try {
                        const response = await axios.post('http://localhost:8080/api/packer/pack', this.form);
                        this.result = response.data;
                        this.drawResult();
                    } catch (error) {
                        this.$message.error('请求失败: ' + error.message);
                    }
                },
                drawResult() {
                    const canvas = this.$refs.canvas;
                    const ctx = canvas.getContext('2d');
                    const scale = 1; // 缩放比例，可以根据需要调整

                    // 清空画布
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // 绘制容器边框
                    ctx.strokeStyle = '#333';
                    ctx.strokeRect(0, 0, this.form.width * scale, this.form.height * scale);

                    // 绘制每个矩形
                    this.result.results.forEach((block, index) => {
                        if (block.fitted) {
                            ctx.fillStyle = this.colors[index] || this.getRandomColor();
                            ctx.fillRect(
                                block.x * scale,
                                block.y * scale,
                                block.width * scale,
                                block.height * scale
                            );
                            ctx.strokeStyle = '#333';
                            ctx.strokeRect(
                                block.x * scale,
                                block.y * scale,
                                block.width * scale,
                                block.height * scale
                            );

                            // 绘制尺寸文本
                            ctx.fillStyle = '#000';
                            ctx.font = '12px Arial';
                            ctx.fillText(
                                `${block.width}x${block.height}`,
                                (block.x + 5) * scale,
                                (block.y + 20) * scale
                            );
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>