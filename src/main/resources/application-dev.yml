server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  datasource:
    #MYsql连接字符串
    url: jdbc:mysql://**************:53308/inkstable?useUnicode=true&characterEncoding=utf-8&allowMutilQueries=true&serverTimezone=Asia/Shanghai&useSSL=false
    # 威海公网
#    url: **************************************************************************************************************************************************
    username: root
    password: asd@123456
#   威海公网
#    password: asd@weihai
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
  flyway:
    enabled: false    #是否开启flyway，默认true
    baseline-on-migrate: true
    placeholder-replacement: false
    locations: classpath:/db/migration #默认位置
    # 添加以下配置确保Flyway在应用启动时执行
    baseline-version: 0
    out-of-order: true
    validate-on-migrate: true
#  redis:
#    database: 0
#    # Redis服务器地址 写你的ip
#    host: **************
#    # Redis服务器连接端口
#    port: 56379
#    # Redis服务器连接密码（默认为空）
#    password: asd@123456
#    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
#    jedis:
#      pool:
#        max-active: 200
#        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
#        max-wait: -1
#        # 连接池中的最大空闲连接
#        max-idle: 10
#        # 连接池中的最小空闲连接
#        min-idle: 0
#    # 连接超时时间（毫秒） 去链接redis服务端
#    timeout: 6000
  web: #配置静态资源访问路径
    resources:
      static-locations: classpath:/
  mvc:
    view:
      suffix: .html


mybatis:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.table.**.domain
  #配置打印SQL语句到控制台


inks:
  redisType: mysql
  user:
    wxscan-create: true    #微信公众号扫码可直接创建admin权限用户，默认false
  feign:
    GrfUrl: http://dev.inksyun.com:18801
    UtsUrl: http://192.168.99.96:10684
  # 调用oam公众号接口获取openid #内网测试号:http://192.168.99.96:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce
  tid: tid-inks-table
