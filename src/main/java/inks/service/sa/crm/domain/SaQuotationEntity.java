package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 报价单(SaQuotation)实体类
 *
 * <AUTHOR>
 * @since 2025-06-18 10:50:28
 */
@Data
public class SaQuotationEntity implements Serializable {
    private static final long serialVersionUID = 466336325727723501L;
     // id
    private String id;
     // 报价单号
    private String refno;
     // 单据类型
    private String billtype;
     // 报价标题
    private String billtitle;
     // 报价日期
    private Date billdate;
     // 机会率
    private String probability;
     // 客户ID
    private String groupid;
     // 商机id
    private String businessid;
     // 客户
    private String customer;
     // 地址
    private String custaddress;
     // 联系人
    private String custlinkman;
     // 电话
    private String custtel;
     // 传真
    private String custfax;
     // 是否含税
    private Integer taxmark;
     // 项目周期
    private String periods;
     // 报价有效期
    private String validitydate;
     // 货币
    private String currency;
     // 交货方式
    private String delivery;
     // 结款方式
    private String payment;
     // 工作阶段
    private String workstage;
     // 业务员id
    private String operatorid;
     // 业务员
    private String operator;
     // 附加条款
    private String billclause;
     // 含税金额
    private Double billtaxamount;
     // 未税金额
    private Double billamount;
     // 税额
    private Double billtaxtotal;
     // 摘要
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 正在进行OA
    private Integer oaflowmark;
     // 状态
    private String statecode;
     // 状态日期
    private Date statedate;
     // 负责人id
    private String principalid;
     // 负责人
    private String principal;
     // 最后跟进时间
    private Date lastfollowdate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门id
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;


}

