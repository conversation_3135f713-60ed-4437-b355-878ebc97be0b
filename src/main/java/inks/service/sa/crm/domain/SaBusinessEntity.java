package inks.service.sa.crm.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 商机(2024)(SaBusiness)实体类
 *
 * <AUTHOR>
 * @since 2025-06-18 10:49:38
 */
@Data
public class SaBusinessEntity implements Serializable {
    private static final long serialVersionUID = 973674214275860925L;
     // id
    private String id;
     // 商机单号
    private String refno;
     // 单据类型
    private String billtype;
     // 商机标题
    private String billtitle;
     // 商机日期
    private Date billdate;
     // 含税金额
    private Double billtaxamount;
     // 未税金额
    private Double billamount;
     // 税额
    private Double billtaxtotal;
     // 合同金额
    private Double contractamount;
     // 客户id
    private String groupid;
     // 地址
    private String address;
     // 联系人
    private String linkman;
     // 电话
    private String tel;
     // 传真
    private String fax;
     // 报备方
    private String reporter;
     // 商机来源
    private String source;
     // 商机类型
    private String businesstype;
     // 销售类型
    private String salestype;
     // 销售进度
    private String salesprogress;
     // 预计成交金额
    private String estimatedamt;
     // 预计成交时间
    private Date estimatedtime;
     // 市场活动
    private String marketing;
     // 商机情况
    private String status;
     // 工作阶段
    private String workstage;
     // 阶段id
    private String stageid;
     // 阶段数据
    private String stagetext;
     // 0进行中/1赢单/2输单/3无效
    private Integer stageresult;
     // 业务员id
    private String operatorid;
     // 业务员
    private String operator;
     // 摘要
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 正在进行OA
    private Integer oaflowmark;
     // 状态
    private String statecode;
     // 状态日期
    private Date statedate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门id
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;


}

