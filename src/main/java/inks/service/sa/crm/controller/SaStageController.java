package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.domain.pojo.SaStagePojo;
import inks.service.sa.crm.domain.pojo.SaStageitemPojo;
import inks.service.sa.crm.domain.pojo.SaStageitemdetailPojo;
import inks.service.sa.crm.service.SaStageService;
import inks.service.sa.crm.service.SaStageitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 阶段(SaStage)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-20 09:09:25
 */
//@RestController
//@RequestMapping("saStage")
public class SaStageController {

    private final static Logger logger = LoggerFactory.getLogger(SaStageController.class);
    @Resource
    private SaStageService saStageService;
    /**
     * 服务对象Item
     */
    @Resource
    private SaStageitemService saStageitemService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取阶段详细信息", notes = "获取阶段详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Stage.List")
    public R<SaStagePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saStageService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Stage.List")
    public R<PageInfo<SaStageitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Stage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saStageService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取阶段详细信息", notes = "获取阶段详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Stage.List")
    public R<SaStagePojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saStageService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Stage.List")
    public R<PageInfo<SaStagePojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Stage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saStageService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Stage.List")
    public R<PageInfo<SaStagePojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Stage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saStageService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增阶段", notes = "新增阶段", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Stage.Add")
    public R<SaStagePojo> create(@RequestBody String json) {
        try {
            SaStagePojo saStagePojo = JSONArray.parseObject(json, SaStagePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saStagePojo.setCreateby(loginUser.getRealName());   // 创建者
            saStagePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saStagePojo.setCreatedate(new Date());   // 创建时间
            saStagePojo.setLister(loginUser.getRealname());   // 制表
            saStagePojo.setListerid(loginUser.getUserid());    // 制表id            
            saStagePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saStageService.insert(saStagePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改阶段", notes = "修改阶段", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Stage.Edit")
    public R<SaStagePojo> update(@RequestBody String json) {
        try {
            SaStagePojo saStagePojo = JSONArray.parseObject(json, SaStagePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saStagePojo.setLister(loginUser.getRealname());   // 制表
            saStagePojo.setListerid(loginUser.getUserid());    // 制表id   
            saStagePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saStageService.update(saStagePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除阶段", notes = "删除阶段", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Stage.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saStageService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增阶段Item", notes = "新增阶段Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Stage.Add")
    public R<SaStageitemPojo> createItem(@RequestBody String json) {
        try {
            SaStageitemPojo saStageitemPojo = JSONArray.parseObject(json, SaStageitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saStageitemService.insert(saStageitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除阶段Item", notes = "删除阶段Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Stage.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saStageitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Stage.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaStagePojo saStagePojo = this.saStageService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saStagePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saStagePojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaStageitemPojo saStageitemPojo = new SaStageitemPojo();
                    saStagePojo.getItem().add(saStageitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saStagePojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

