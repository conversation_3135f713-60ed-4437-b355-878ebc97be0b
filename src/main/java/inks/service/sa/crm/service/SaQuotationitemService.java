package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaQuotationitemPojo;

import java.util.List;

/**
 * 商机项目(SaQuotationitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-14 10:50:42
 */
public interface SaQuotationitemService {


    SaQuotationitemPojo getEntity(String key);


    PageInfo<SaQuotationitemPojo> getPageList(QueryParam queryParam);

    List<SaQuotationitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saQuotationitemPojo 实例对象
     * @return 实例对象
     */
    SaQuotationitemPojo insert(SaQuotationitemPojo saQuotationitemPojo);

    /**
     * 修改数据
     *
     * @param saQuotationitempojo 实例对象
     * @return 实例对象
     */
    SaQuotationitemPojo update(SaQuotationitemPojo saQuotationitempojo);


    int delete(String key);

    /**
     * 修改数据
     *
     * @param saQuotationitempojo 实例对象
     * @return 实例对象
     */
    SaQuotationitemPojo clearNull(SaQuotationitemPojo saQuotationitempojo);
}
