package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaDmsuserEntity;
import inks.service.sa.crm.domain.pojo.SaDmsuserPojo;
import inks.service.sa.crm.mapper.SaDmsuserMapper;
import inks.service.sa.crm.service.SaDmsuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * DMS用户(SaDmsuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-08 16:42:43
 */
@Service("saDmsuserService")
public class SaDmsuserServiceImpl implements SaDmsuserService {
    @Resource
    private SaDmsuserMapper saDmsuserMapper;


    @Override
    public SaDmsuserPojo getEntity(String key) {
        return this.saDmsuserMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDmsuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDmsuserPojo> lst = saDmsuserMapper.getPageList(queryParam);
            PageInfo<SaDmsuserPojo> pageInfo = new PageInfo<SaDmsuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDmsuserPojo insert(SaDmsuserPojo saDmsuserPojo) {
        //初始化NULL字段
        if (saDmsuserPojo.getParentid() == null) saDmsuserPojo.setParentid("");
        if (saDmsuserPojo.getUsername() == null) saDmsuserPojo.setUsername("");
        if (saDmsuserPojo.getRealname() == null) saDmsuserPojo.setRealname("");
        if (saDmsuserPojo.getNickname() == null) saDmsuserPojo.setNickname("");
        if (saDmsuserPojo.getUserpassword() == null) saDmsuserPojo.setUserpassword("");
        if (saDmsuserPojo.getOpenid() == null) saDmsuserPojo.setOpenid("");
        if (saDmsuserPojo.getMobile() == null) saDmsuserPojo.setMobile("");
        if (saDmsuserPojo.getEmail() == null) saDmsuserPojo.setEmail("");
        if (saDmsuserPojo.getSex() == null) saDmsuserPojo.setSex(0);
        if (saDmsuserPojo.getLangcode() == null) saDmsuserPojo.setLangcode("");
        if (saDmsuserPojo.getAvatar() == null) saDmsuserPojo.setAvatar("");
        if (saDmsuserPojo.getUsertype() == null) saDmsuserPojo.setUsertype(0);
        if (saDmsuserPojo.getIsadmin() == null) saDmsuserPojo.setIsadmin(0);
        if (saDmsuserPojo.getDeptid() == null) saDmsuserPojo.setDeptid("");
        if (saDmsuserPojo.getDeptcode() == null) saDmsuserPojo.setDeptcode("");
        if (saDmsuserPojo.getDeptname() == null) saDmsuserPojo.setDeptname("");
        if (saDmsuserPojo.getIsdeptadmin() == null) saDmsuserPojo.setIsdeptadmin(0);
        if (saDmsuserPojo.getDeptrownum() == null) saDmsuserPojo.setDeptrownum(0);
        if (saDmsuserPojo.getRownum() == null) saDmsuserPojo.setRownum(0);
        if (saDmsuserPojo.getUserstatus() == null) saDmsuserPojo.setUserstatus(0);
        if (saDmsuserPojo.getUsercode() == null) saDmsuserPojo.setUsercode("");
        if (saDmsuserPojo.getGroupids() == null) saDmsuserPojo.setGroupids("");
        if (saDmsuserPojo.getGroupnames() == null) saDmsuserPojo.setGroupnames("");
        if (saDmsuserPojo.getDmsfunctids() == null) saDmsuserPojo.setDmsfunctids("");
        if (saDmsuserPojo.getDmsfunctnames() == null) saDmsuserPojo.setDmsfunctnames("");
        if (saDmsuserPojo.getRemark() == null) saDmsuserPojo.setRemark("");
        if (saDmsuserPojo.getCreateby() == null) saDmsuserPojo.setCreateby("");
        if (saDmsuserPojo.getCreatebyid() == null) saDmsuserPojo.setCreatebyid("");
        if (saDmsuserPojo.getCreatedate() == null) saDmsuserPojo.setCreatedate(new Date());
        if (saDmsuserPojo.getLister() == null) saDmsuserPojo.setLister("");
        if (saDmsuserPojo.getListerid() == null) saDmsuserPojo.setListerid("");
        if (saDmsuserPojo.getModifydate() == null) saDmsuserPojo.setModifydate(new Date());
        if (saDmsuserPojo.getTenantid() == null) saDmsuserPojo.setTenantid("");
        if (saDmsuserPojo.getTenantname() == null) saDmsuserPojo.setTenantname("");
        if (saDmsuserPojo.getRevision() == null) saDmsuserPojo.setRevision(0);
        SaDmsuserEntity saDmsuserEntity = new SaDmsuserEntity();
        BeanUtils.copyProperties(saDmsuserPojo, saDmsuserEntity);
        //生成雪花id
        saDmsuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
        saDmsuserEntity.setRevision(1);  //乐观锁
        this.saDmsuserMapper.insert(saDmsuserEntity);
        return this.getEntity(saDmsuserEntity.getUserid());

    }

    /**
     * 修改数据
     *
     * @param saDmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDmsuserPojo update(SaDmsuserPojo saDmsuserPojo) {
        SaDmsuserEntity saDmsuserEntity = new SaDmsuserEntity();
        BeanUtils.copyProperties(saDmsuserPojo, saDmsuserEntity);
        this.saDmsuserMapper.update(saDmsuserEntity);
        return this.getEntity(saDmsuserEntity.getUserid());
    }


    @Override
    public int delete(String key) {
        return this.saDmsuserMapper.delete(key);
    }


}
