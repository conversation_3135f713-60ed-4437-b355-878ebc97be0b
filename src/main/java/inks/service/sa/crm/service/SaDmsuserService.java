package inks.service.sa.crm.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaDmsuserPojo;

/**
 * DMS用户(SaDmsuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-08 16:42:43
 */
public interface SaDmsuserService {


    SaDmsuserPojo getEntity(String key);


    PageInfo<SaDmsuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDmsuserPojo 实例对象
     * @return 实例对象
     */
    SaDmsuserPojo insert(SaDmsuserPojo saDmsuserPojo);

    /**
     * 修改数据
     *
     * @param saDmsuserpojo 实例对象
     * @return 实例对象
     */
    SaDmsuserPojo update(SaDmsuserPojo saDmsuserpojo);


    int delete(String key);
}
