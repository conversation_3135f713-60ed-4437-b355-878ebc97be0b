package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaSmpdeliEntity;
import inks.service.sa.crm.domain.SaSmpdeliitemEntity;
import inks.service.sa.crm.domain.pojo.SaSmpdeliPojo;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemPojo;
import inks.service.sa.crm.domain.pojo.SaSmpdeliitemdetailPojo;
import inks.service.sa.crm.mapper.SaSmpdeliMapper;
import inks.service.sa.crm.mapper.SaSmpdeliitemMapper;
import inks.service.sa.crm.service.SaSmpdeliService;
import inks.service.sa.crm.service.SaSmpdeliitemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 样品发货(SaSmpdeli)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-03 16:20:28
 */
@Service("saSmpdeliService")
public class SaSmpdeliServiceImpl implements SaSmpdeliService {
    @Resource
    private SaSmpdeliMapper saSmpdeliMapper;

    @Resource
    private SaSmpdeliitemMapper saSmpdeliitemMapper;


    @Resource
    private SaSmpdeliitemService saSmpdeliitemService;


    @Override
    public SaSmpdeliPojo getEntity(String key) {
        return this.saSmpdeliMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaSmpdeliitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSmpdeliitemdetailPojo> lst = saSmpdeliMapper.getPageList(queryParam);
            PageInfo<SaSmpdeliitemdetailPojo> pageInfo = new PageInfo<SaSmpdeliitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaSmpdeliPojo getBillEntity(String key) {
        try {
            //读取主表
            SaSmpdeliPojo saSmpdeliPojo = this.saSmpdeliMapper.getEntity(key);
            //读取子表
            saSmpdeliPojo.setItem(saSmpdeliitemMapper.getList(saSmpdeliPojo.getId()));
            return saSmpdeliPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaSmpdeliPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSmpdeliPojo> lst = saSmpdeliMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saSmpdeliitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaSmpdeliPojo> pageInfo = new PageInfo<SaSmpdeliPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaSmpdeliPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSmpdeliPojo> lst = saSmpdeliMapper.getPageTh(queryParam);
            PageInfo<SaSmpdeliPojo> pageInfo = new PageInfo<SaSmpdeliPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public SaSmpdeliPojo insert(SaSmpdeliPojo saSmpdeliPojo) {
        //初始化NULL字段
        cleanNull(saSmpdeliPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaSmpdeliEntity saSmpdeliEntity = new SaSmpdeliEntity();
        BeanUtils.copyProperties(saSmpdeliPojo, saSmpdeliEntity);
        //设置id和新建日期
        saSmpdeliEntity.setId(id);
        saSmpdeliEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saSmpdeliMapper.insert(saSmpdeliEntity);
        //Item子表处理
        List<SaSmpdeliitemPojo> lst = saSmpdeliPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                SaSmpdeliitemPojo itemPojo = this.saSmpdeliitemService.clearNull(lst.get(i));
                SaSmpdeliitemEntity saSmpdeliitemEntity = new SaSmpdeliitemEntity();
                BeanUtils.copyProperties(itemPojo, saSmpdeliitemEntity);
                //设置id和Pid
                saSmpdeliitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saSmpdeliitemEntity.setPid(id);
                saSmpdeliitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saSmpdeliitemMapper.insert(saSmpdeliitemEntity);
            }
        }
        if (lst != null) {
            // 同步样品需求(计划)
            for (SaSmpdeliitemPojo saSmpdeliitemPojo : lst) {
                String citeitemid = saSmpdeliitemPojo.getCiteitemid();
                if (StringUtils.isNotBlank(citeitemid)) {
                    saSmpdeliMapper.syncPlanItemFinishQty(citeitemid);
                    saSmpdeliMapper.syncPlanFinishCount(citeitemid);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saSmpdeliEntity.getId());
    }


    @Override
    @Transactional
    public SaSmpdeliPojo update(SaSmpdeliPojo saSmpdeliPojo) {
        //主表更改
        SaSmpdeliEntity saSmpdeliEntity = new SaSmpdeliEntity();
        BeanUtils.copyProperties(saSmpdeliPojo, saSmpdeliEntity);
        this.saSmpdeliMapper.update(saSmpdeliEntity);
        if (saSmpdeliPojo.getItem() != null) {
            //Item子表处理
            List<SaSmpdeliitemPojo> lst = saSmpdeliPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saSmpdeliMapper.getDelItemIds(saSmpdeliPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saSmpdeliitemMapper.delete(lstDelIds.get(i));
                }
            }
            //循环每个item子表
            for (SaSmpdeliitemPojo smpdeliitemPojo : lst) {
                SaSmpdeliitemEntity saSmpdeliitemEntity = new SaSmpdeliitemEntity();
                if ("".equals(smpdeliitemPojo.getId()) || smpdeliitemPojo.getId() == null) {
                    //初始化item的NULL
                    SaSmpdeliitemPojo itemPojo = this.saSmpdeliitemService.clearNull(smpdeliitemPojo);
                    BeanUtils.copyProperties(itemPojo, saSmpdeliitemEntity);
                    //设置id和Pid
                    saSmpdeliitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    saSmpdeliitemEntity.setPid(saSmpdeliEntity.getId());  // 主表 id
                    saSmpdeliitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.saSmpdeliitemMapper.insert(saSmpdeliitemEntity);
                } else {
                    BeanUtils.copyProperties(smpdeliitemPojo, saSmpdeliitemEntity);
                    this.saSmpdeliitemMapper.update(saSmpdeliitemEntity);
                }
            }
            // 同步样品需求(计划)
            for (SaSmpdeliitemPojo saSmpdeliitemPojo : lst) {
                String citeitemid = saSmpdeliitemPojo.getCiteitemid();
                if (StringUtils.isNotBlank(citeitemid)) {
                    saSmpdeliMapper.syncPlanItemFinishQty(citeitemid);
                    saSmpdeliMapper.syncPlanFinishCount(citeitemid);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saSmpdeliEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
        SaSmpdeliPojo saSmpdeliPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaSmpdeliitemPojo> lst = saSmpdeliPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (SaSmpdeliitemPojo smpdeliitemPojo : lst) {
                this.saSmpdeliitemMapper.delete(smpdeliitemPojo.getId());
            }
            // 同步样品需求(计划)
            for (SaSmpdeliitemPojo saSmpdeliitemPojo : lst) {
                String citeitemid = saSmpdeliitemPojo.getCiteitemid();
                if (StringUtils.isNotBlank(citeitemid)) {
                    saSmpdeliMapper.syncPlanItemFinishQty(citeitemid);
                    saSmpdeliMapper.syncPlanFinishCount(citeitemid);
                }
            }
        }
        return this.saSmpdeliMapper.delete(key);
    }


    @Override
    @Transactional
    public SaSmpdeliPojo approval(SaSmpdeliPojo saSmpdeliPojo) {
        //主表更改
        SaSmpdeliEntity saSmpdeliEntity = new SaSmpdeliEntity();
        BeanUtils.copyProperties(saSmpdeliPojo, saSmpdeliEntity);
        this.saSmpdeliMapper.approval(saSmpdeliEntity);
        //返回Bill实例
        return this.getBillEntity(saSmpdeliEntity.getId());
    }

    private static void cleanNull(SaSmpdeliPojo saSmpdeliPojo) {
        if (saSmpdeliPojo.getRefno() == null) saSmpdeliPojo.setRefno("");
        if (saSmpdeliPojo.getBilltype() == null) saSmpdeliPojo.setBilltype("");
        if (saSmpdeliPojo.getBilltitle() == null) saSmpdeliPojo.setBilltitle("");
        if (saSmpdeliPojo.getBilldate() == null) saSmpdeliPojo.setBilldate(new Date());
        if (saSmpdeliPojo.getGroupid() == null) saSmpdeliPojo.setGroupid("");
        if (saSmpdeliPojo.getBusinessid() == null) saSmpdeliPojo.setBusinessid("");
        if (saSmpdeliPojo.getTelephone() == null) saSmpdeliPojo.setTelephone("");
        if (saSmpdeliPojo.getLinkman() == null) saSmpdeliPojo.setLinkman("");
        if (saSmpdeliPojo.getDeliadd() == null) saSmpdeliPojo.setDeliadd("");
        if (saSmpdeliPojo.getTaxrate() == null) saSmpdeliPojo.setTaxrate(0);
        if (saSmpdeliPojo.getTransport() == null) saSmpdeliPojo.setTransport("");
        if (saSmpdeliPojo.getSalesman() == null) saSmpdeliPojo.setSalesman("");
        if (saSmpdeliPojo.getSalesmanid() == null) saSmpdeliPojo.setSalesmanid("");
        if (saSmpdeliPojo.getOperator() == null) saSmpdeliPojo.setOperator("");
        if (saSmpdeliPojo.getOperatorid() == null) saSmpdeliPojo.setOperatorid("");
        if (saSmpdeliPojo.getSummary() == null) saSmpdeliPojo.setSummary("");
        if (saSmpdeliPojo.getCreateby() == null) saSmpdeliPojo.setCreateby("");
        if (saSmpdeliPojo.getCreatebyid() == null) saSmpdeliPojo.setCreatebyid("");
        if (saSmpdeliPojo.getCreatedate() == null) saSmpdeliPojo.setCreatedate(new Date());
        if (saSmpdeliPojo.getLister() == null) saSmpdeliPojo.setLister("");
        if (saSmpdeliPojo.getListerid() == null) saSmpdeliPojo.setListerid("");
        if (saSmpdeliPojo.getModifydate() == null) saSmpdeliPojo.setModifydate(new Date());
        if (saSmpdeliPojo.getAssessor() == null) saSmpdeliPojo.setAssessor("");
        if (saSmpdeliPojo.getAssessorid() == null) saSmpdeliPojo.setAssessorid("");
        if (saSmpdeliPojo.getAssessdate() == null) saSmpdeliPojo.setAssessdate(new Date());
        if (saSmpdeliPojo.getBillstatecode() == null) saSmpdeliPojo.setBillstatecode("");
        if (saSmpdeliPojo.getBillstatedate() == null) saSmpdeliPojo.setBillstatedate(new Date());
        if (saSmpdeliPojo.getBilltaxamount() == null) saSmpdeliPojo.setBilltaxamount(0D);
        if (saSmpdeliPojo.getBilltaxtotal() == null) saSmpdeliPojo.setBilltaxtotal(0D);
        if (saSmpdeliPojo.getBillamount() == null) saSmpdeliPojo.setBillamount(0D);
        if (saSmpdeliPojo.getBillreceived() == null) saSmpdeliPojo.setBillreceived(0D);
        if (saSmpdeliPojo.getItemcount() == null) saSmpdeliPojo.setItemcount(0);
        if (saSmpdeliPojo.getFinishcount() == null) saSmpdeliPojo.setFinishcount(0);
        if (saSmpdeliPojo.getDisannulcount() == null) saSmpdeliPojo.setDisannulcount(0);
        if (saSmpdeliPojo.getPrintcount() == null) saSmpdeliPojo.setPrintcount(0);
        if (saSmpdeliPojo.getCustom1() == null) saSmpdeliPojo.setCustom1("");
        if (saSmpdeliPojo.getCustom2() == null) saSmpdeliPojo.setCustom2("");
        if (saSmpdeliPojo.getCustom3() == null) saSmpdeliPojo.setCustom3("");
        if (saSmpdeliPojo.getCustom4() == null) saSmpdeliPojo.setCustom4("");
        if (saSmpdeliPojo.getCustom5() == null) saSmpdeliPojo.setCustom5("");
        if (saSmpdeliPojo.getCustom6() == null) saSmpdeliPojo.setCustom6("");
        if (saSmpdeliPojo.getCustom7() == null) saSmpdeliPojo.setCustom7("");
        if (saSmpdeliPojo.getCustom8() == null) saSmpdeliPojo.setCustom8("");
        if (saSmpdeliPojo.getCustom9() == null) saSmpdeliPojo.setCustom9("");
        if (saSmpdeliPojo.getCustom10() == null) saSmpdeliPojo.setCustom10("");
        if(saSmpdeliPojo.getDeptid()==null) saSmpdeliPojo.setDeptid("");
        if(saSmpdeliPojo.getTenantid()==null) saSmpdeliPojo.setTenantid("");
        if (saSmpdeliPojo.getTenantname() == null) saSmpdeliPojo.setTenantname("");
        if (saSmpdeliPojo.getRevision() == null) saSmpdeliPojo.setRevision(0);
    }

    @Override
    public SaSmpdeliPojo closed(List<SaSmpdeliitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "中止" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            SaSmpdeliitemPojo Pojo = lst.get(i);
            SaSmpdeliitemPojo dbPojo = this.saSmpdeliitemMapper.getEntity(Pojo.getId());
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已作废,禁止操作");
                    }
                    SaSmpdeliitemEntity entity = new SaSmpdeliitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.saSmpdeliitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已" + strType + ",无需操作");
                }
            }
            // 同步样品需求(计划)
            for (SaSmpdeliitemPojo saSmpdeliitemPojo : lst) {
                String citeitemid = saSmpdeliitemPojo.getCiteitemid();
                if (StringUtils.isNotBlank(citeitemid)) {
                    saSmpdeliMapper.syncPlanItemFinishQty(citeitemid);
                    saSmpdeliMapper.syncPlanFinishCount(citeitemid);
                }
            }
        }

        ////--> lst.stream()拿到去重的goodsid Set集合
        //Set<String> goodsidLstSet = lst.stream().map(SaSmpdeliitemPojo::getGoodsid).collect(Collectors.toSet());
        //// 同步货品数量 SQL替代MQ
        //goodsidLstSet.forEach(goodsid -> {
        //    syncMapper.updateGoodsBusRemQty(goodsid, tid);
        //});

        if (disNum > 0) {
            this.saSmpdeliMapper.updateFinishCount(Pid, tid);
            //主表更改
            SaSmpdeliEntity saSmpdeliEntity = new SaSmpdeliEntity();
            saSmpdeliEntity.setId(Pid);
            saSmpdeliEntity.setLister(loginUser.getRealname());
            saSmpdeliEntity.setListerid(loginUser.getUserid());
            saSmpdeliEntity.setModifydate(new Date());
            saSmpdeliEntity.setTenantid(loginUser.getTenantid());
            this.saSmpdeliMapper.update(saSmpdeliEntity);
            //返回Bill实例
            return this.getBillEntity(saSmpdeliEntity.getId());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }


    @Override
    public SaSmpdeliPojo disannul(List<SaSmpdeliitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            SaSmpdeliitemPojo Pojo = lst.get(i);
            SaSmpdeliitemPojo dbPojo = this.saSmpdeliitemMapper.getEntity(Pojo.getId());
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已关闭,禁止作废操作");
                    }
                    SaSmpdeliitemEntity entity = new SaSmpdeliitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.saSmpdeliitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getItemname() + "已" + strType + ",无需操作");
                }

            }
        }

        ////--> lst.stream()拿到去重的goodsid Set集合
        //Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
        //// 同步货品数量 SQL替代MQ
        //goodsidLstSet.forEach(goodsid -> {
        //    syncMapper.updateGoodsBusRemQty(goodsid, tid);
        //});

        if (disNum > 0) {
            this.saSmpdeliMapper.updateDisannulCountAndAmount(Pid, tid);
            //主表更改
            SaSmpdeliEntity saSmpdeliEntity = new SaSmpdeliEntity();
            saSmpdeliEntity.setId(Pid);
            saSmpdeliEntity.setLister(loginUser.getRealname());
            saSmpdeliEntity.setListerid(loginUser.getUserid());
            saSmpdeliEntity.setModifydate(new Date());
            saSmpdeliEntity.setTenantid(loginUser.getTenantid());
            this.saSmpdeliMapper.update(saSmpdeliEntity);
            //返回Bill实例
            return this.getBillEntity(saSmpdeliEntity.getId());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }
}
