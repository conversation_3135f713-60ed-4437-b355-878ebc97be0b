package inks.service.sa.crm.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.crm.domain.pojo.SaDemanddictPojo;
import inks.service.sa.crm.domain.SaDemanddictEntity;
import inks.service.sa.crm.mapper.SaDemanddictMapper;
import inks.service.sa.crm.service.SaDemanddictService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 需求字典(SaDemanddict)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-11 10:02:22
 */
@Service("saDemanddictService")
public class SaDemanddictServiceImpl implements SaDemanddictService {
    @Resource
    private SaDemanddictMapper saDemanddictMapper;

    @Override
    public SaDemanddictPojo getEntity(String key) {
        return this.saDemanddictMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDemanddictPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemanddictPojo> lst = saDemanddictMapper.getPageList(queryParam);
            PageInfo<SaDemanddictPojo> pageInfo = new PageInfo<SaDemanddictPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaDemanddictPojo insert(SaDemanddictPojo saDemanddictPojo) {
        //初始化NULL字段
        cleanNull(saDemanddictPojo);
        SaDemanddictEntity saDemanddictEntity = new SaDemanddictEntity(); 
        BeanUtils.copyProperties(saDemanddictPojo,saDemanddictEntity);
        //生成雪花id
          saDemanddictEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saDemanddictEntity.setRevision(1);  //乐观锁
          this.saDemanddictMapper.insert(saDemanddictEntity);
        return this.getEntity(saDemanddictEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saDemanddictPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDemanddictPojo update(SaDemanddictPojo saDemanddictPojo) {
        SaDemanddictEntity saDemanddictEntity = new SaDemanddictEntity(); 
        BeanUtils.copyProperties(saDemanddictPojo,saDemanddictEntity);
        this.saDemanddictMapper.update(saDemanddictEntity);
        return this.getEntity(saDemanddictEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDemanddictMapper.delete(key) ;
    }
    

    private static void cleanNull(SaDemanddictPojo saDemanddictPojo) {
        if(saDemanddictPojo.getDemandcode()==null) saDemanddictPojo.setDemandcode("");
        if(saDemanddictPojo.getDemandname()==null) saDemanddictPojo.setDemandname("");
        if(saDemanddictPojo.getDemandtype()==null) saDemanddictPojo.setDemandtype("");
        if(saDemanddictPojo.getDescription()==null) saDemanddictPojo.setDescription("");
        if(saDemanddictPojo.getRownum()==null) saDemanddictPojo.setRownum(0);
        if(saDemanddictPojo.getRemark()==null) saDemanddictPojo.setRemark("");
        if(saDemanddictPojo.getCreateby()==null) saDemanddictPojo.setCreateby("");
        if(saDemanddictPojo.getCreatebyid()==null) saDemanddictPojo.setCreatebyid("");
        if(saDemanddictPojo.getCreatedate()==null) saDemanddictPojo.setCreatedate(new Date());
        if(saDemanddictPojo.getLister()==null) saDemanddictPojo.setLister("");
        if(saDemanddictPojo.getListerid()==null) saDemanddictPojo.setListerid("");
        if(saDemanddictPojo.getModifydate()==null) saDemanddictPojo.setModifydate(new Date());
        if(saDemanddictPojo.getTenantid()==null) saDemanddictPojo.setTenantid("");
        if(saDemanddictPojo.getRevision()==null) saDemanddictPojo.setRevision(0);
   }

}
