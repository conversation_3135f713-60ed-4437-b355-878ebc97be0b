package inks.service.sa.crm.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.crm.domain.pojo.SaFollowviewPojo;
import inks.service.sa.crm.domain.SaFollowviewEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 跟踪记录(SaFollowview)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-26 09:51:37
 */
@Mapper
public interface SaFollowviewMapper {


    SaFollowviewPojo getEntity(@Param("key") String key);

    List<SaFollowviewPojo> getPageList(QueryParam queryParam);

    int insert(SaFollowviewEntity saFollowviewEntity);

    int update(SaFollowviewEntity saFollowviewEntity);

    int delete(@Param("key") String key);

    List<SaFollowviewPojo> getListByCiteIdAndIsAuto(String citeid,  String citetype, Integer isauto);

    List<Map<String, Object>> countByIsAuto(String filter);

    List<Map<String, Object>> countByCiteType(String citetype, Integer isauto);

    List<SaFollowviewPojo> getListByAll(SaFollowviewPojo safollowview);

    int unFinish(String key);
}

