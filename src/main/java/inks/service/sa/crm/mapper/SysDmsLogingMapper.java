package inks.service.sa.crm.mapper;


import inks.common.core.domain.LoginUser;
import inks.service.sa.crm.domain.pojo.SaDmsuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysDmsLogingMapper {
    LoginUser login(@Param("userName") String userName, @Param("passWord") String passWord);

    Integer count(String userName);

    List<SaDmsuserPojo> getListByOpenid(String openid);

//    List<PiTenant> getListInTids(@Param("tidList") List<String> tidList);

    List<SaDmsuserPojo> getListByUserid(String userid);

    SaDmsuserPojo getDmsUserByUserid(@Param("userid") String userid, @Param("tid") String tid);
}
