package inks.service.sa.table.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import inks.common.core.domain.R;
import inks.service.sa.table.domain.pojo.SaFormsettingPojo;
import inks.service.sa.table.domain.vo.FormSettingSchemaStruct;
import inks.service.sa.table.mapper.SaFormsettingMapper;
import inks.service.sa.table.service.SaFormsettingService;
import inks.service.sa.table.utils.HttpUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 表单设置表(Sa_FormSetting)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:43
 */
@RestController
@RequestMapping("S18M05B1")
@Api(tags="S18M05B1:表单设置表")
public class S18M05B1Controller extends SaFormsettingController {
    @Resource
    private SaFormsettingService saFormsettingService;
    @Resource
    private SaFormsettingMapper saFormsettingMapper;




    /**
     * 公开接口
     * 表单填写时需要的设置
     */
    @GetMapping("/settings/{key}")
    @ApiOperation(value=" 表单填写时需要的设置", notes="表单填写时需要的设置", produces="application/json")
    public R queryPublicFormSettingByKey(@PathVariable("key") String formId) {
        FormSettingSchemaStruct formSettingSchema = getFormSettingSchema(formId);
        return R.ok(formSettingSchema);
    }

    /**
     * 表单提交设置查询
     */
    @GetMapping("/setting/{key}")
    @ApiOperation(value=" 表单提交设置查询", notes="表单提交设置查询", produces="application/json")
    public R<Map<String, Object>> queryFormSettingByKey(@PathVariable("key") String formId) {
        SaFormsettingPojo setting = saFormsettingMapper.getEntityByFormId(formId);
        if (ObjectUtil.isNull(setting)) {
            return R.ok();
        }
        JSONObject jsonObject = JSONObject.parseObject(setting.getSettings(), Feature.OrderedField);
        jsonObject.put("formid", formId);
        jsonObject.put("id", setting.getId());

        return R.ok(jsonObject);
    }



    /**
     * 当前填写设置的状态
     *
     * @param formId 表单key
     * @param wxOpenId 微信openid
     * @param type     类型 1公开填写 2.指定填写
     */
    @GetMapping("/setting-status")
    @PermitAll
    public R<Boolean> querySettingStatus(@RequestParam String formId, @RequestParam(required = false) String wxOpenId, @RequestParam(required = false) Integer type, HttpServletRequest request) {
        R<Boolean> userFormWriteSettingStatus = saFormsettingService.getUserFormWriteSettingStatus(formId, HttpUtils.getIpAddr(request), wxOpenId, type);
        return userFormWriteSettingStatus;
    }



    /**
     * 公开接口
     * 检查填写密码是否正确
     */
    @ApiOperation(value=" 检查填写密码是否正确", notes="检查填写密码是否正确", produces="application/json")
    @PostMapping("/checkWritePwd")
    public R<Boolean> checkWritePwd(@RequestBody String json) {
        JSONObject jsonObject = JSONObject.parseObject(json, Feature.OrderedField);
        String formid = jsonObject.getString("formid");
        String password = jsonObject.getString("password");
        // 通过formId获取表单设置,并把settings字段转为FormSettingSchemaStruct对象
        FormSettingSchemaStruct formSettingSchema = getFormSettingSchema(formid);
        if (formSettingSchema.getWritePassword().equals(password)) {
            return R.ok(true);
        }
        return R.fail("密码输入错误");
    }




    /**
     * @Description  通过formId获取表单设置,并把settings字段转为FormSettingSchemaStruct对象
     *               对应TDuck的接口为:userFormSettingService.getFormSettingSchema(formKey);
     * <AUTHOR>
     * @param[1] formId
     * @return FormSettingSchemaStruct
     * @time 2023/8/30 16:03
     */
    public FormSettingSchemaStruct getFormSettingSchema(String formId) {
        SaFormsettingPojo saFormsettingPojo = saFormsettingMapper.getEntityByFormId(formId);
        if (saFormsettingPojo==null) {
            throw new RuntimeException("表单formId不存在");
        }
        return JSONArray.parseObject(saFormsettingPojo.getSettings(), FormSettingSchemaStruct.class);
    }

}
