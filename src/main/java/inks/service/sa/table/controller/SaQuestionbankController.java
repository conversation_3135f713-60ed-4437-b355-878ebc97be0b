package inks.service.sa.table.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaQuestionbankPojo;
import inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.table.domain.pojo.SaQuestionbankitemdetailPojo;
import inks.service.sa.table.service.SaQuestionbankService;
import inks.service.sa.table.service.SaQuestionbankitemService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaBillcodeService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 题库表(Sa_QuestionBank)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:43
 */
//@RestController
//@RequestMapping("saQuestionbank")
public class SaQuestionbankController {

    @Resource
    private SaQuestionbankService saQuestionbankService;
    @Resource
    private SaQuestionbankitemService saQuestionbankitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaQuestionbankController.class);
    

    @ApiOperation(value=" 获取题库表详细信息", notes="获取题库表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.List")
    public R<SaQuestionbankPojo> getEntity(String key) {
      try {
           // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saQuestionbankService.getEntity(key));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.List")
    public R<PageInfo<SaQuestionbankitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_QuestionBank.CreateDate");
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saQuestionbankService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value=" 获取题库表详细信息", notes="获取题库表详细信息", produces="application/json")
    @RequestMapping(value="/getBillEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.List")
    public R<SaQuestionbankPojo> getBillEntity(String key) {
      try {
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saQuestionbankService.getBillEntity(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getBillList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.List")
    public R<PageInfo<SaQuestionbankPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
           if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_QuestionBank.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saQuestionbankService.getBillList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageTh",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.List")
    public R<PageInfo<SaQuestionbankPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_QuestionBank.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saQuestionbankService.getPageTh(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value=" 新增题库表", notes="新增题库表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.Add")
    public R<SaQuestionbankPojo> create(@RequestBody String json) {
        try {
       SaQuestionbankPojo saQuestionbankPojo = JSONArray.parseObject(json,SaQuestionbankPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saQuestionbankPojo.setCreateby(loginUser.getRealName());   // 创建者
            saQuestionbankPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saQuestionbankPojo.setCreatedate(new Date());   // 创建时间
            saQuestionbankPojo.setLister(loginUser.getRealname());   // 制表
            saQuestionbankPojo.setListerid(loginUser.getUserid());    // 制表id            
            saQuestionbankPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saQuestionbankService.insert(saQuestionbankPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="修改题库表", notes="修改题库表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.Edit")
    public R<SaQuestionbankPojo> update(@RequestBody String json) {
        try {
         SaQuestionbankPojo saQuestionbankPojo = JSONArray.parseObject(json,SaQuestionbankPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saQuestionbankPojo.setLister(loginUser.getRealname());   // 制表
            saQuestionbankPojo.setListerid(loginUser.getUserid());    // 制表id   
            saQuestionbankPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saQuestionbankService.update(saQuestionbankPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除题库表", notes="删除题库表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.Delete")
    public R<Integer> delete(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saQuestionbankService.delete(key));
   }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value=" 新增题库表Item", notes="新增题库表Item", produces="application/json") 
    @RequestMapping(value="/createItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.Add")
    public R<SaQuestionbankitemPojo> createItem(@RequestBody String json) {
       try {
     SaQuestionbankitemPojo saQuestionbankitemPojo = JSONArray.parseObject(json,SaQuestionbankitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saQuestionbankitemService.insert(saQuestionbankitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value=" 修改题库表Item", notes="修改题库表Item", produces="application/json") 
    @RequestMapping(value="/updateItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.Edit")
    public R<SaQuestionbankitemPojo> updateItem(@RequestBody String json) {
       try {
     SaQuestionbankitemPojo saQuestionbankitemPojo = JSONArray.parseObject(json,SaQuestionbankitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saQuestionbankitemService.update(saQuestionbankitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }    

    @ApiOperation(value="删除题库表Item", notes="删除题库表Item", produces="application/json")   
    @RequestMapping(value="/deleteItem",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.Delete")
    public R<Integer> deleteItem(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saQuestionbankitemService.delete(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_QuestionBank.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaQuestionbankPojo saQuestionbankPojo = this.saQuestionbankService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saQuestionbankPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
          content = reportsPojo.getRptdata();
        } else {
          throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
       if(reportsPojo.getPagerow()>0){
       int index=0;
      // 取行余数
      index =saQuestionbankPojo.getItem().size()%reportsPojo.getPagerow();
      if(index>0){
        // 补全空白行
        for(int i=0;i<reportsPojo.getPagerow()-index;i++){
            SaQuestionbankitemPojo saQuestionbankitemPojo = new SaQuestionbankitemPojo();
            saQuestionbankPojo.getItem().add(saQuestionbankitemPojo);
          }
      }
     }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saQuestionbankPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

