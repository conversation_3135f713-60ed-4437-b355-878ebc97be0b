package inks.service.sa.table.controller;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;

import java.io.File;

public class OSSUploadTest {
    public static void main(String[] args) {
        // 配置信息
        String endpoint = "https://oss-cn-qingdao.aliyuncs.com";
        String accessKeyId = "LTAI5t7gvbML44MA1pxPbr21";
        String accessKeySecret = "******************************";
        String bucketName = "inkstable";
        String objectName = "test-upload/example.jpg"; // 上传到 OSS 的文件路径
        String localFilePath = "D:\\face\\image\\me\\z1.jpg"; // 本地文件路径
        // 创建 OSS 客户端实例
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            // 创建上传请求
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, new File(localFilePath));
            // 执行上传
            ossClient.putObject(putObjectRequest);
            System.out.println("文件上传成功！访问链接: https://inkstable.oss-cn-qingdao.aliyuncs.com/" + objectName);
        } catch (Exception e) {
            System.err.println("文件上传失败：" + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭客户端
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}
