package inks.service.sa.table.controller;

import cn.hutool.core.util.ObjectUtil;
import inks.common.core.domain.R;
import inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.table.domain.vo.OperateQuestionItemVO;
import inks.service.sa.table.domain.vo.SortQuestionItemRequest;
import inks.service.sa.table.mapper.SaQuestionbankitemMapper;
import inks.service.sa.table.service.SaQuestionbankitemService;
import inks.service.sa.table.utils.SortUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 题库(Sa_QuestionBank)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:43
 */
@RestController
@RequestMapping("S18M10B1")
@Api(tags = "S18M10B1:题库")
public class S18M10B1Controller extends SaQuestionbankController {

    @Resource
    private SaQuestionbankitemService saQuestionbankitemService;
    @Resource
    private SaQuestionbankitemMapper saQuestionbankitemMapper;
    @Resource
    private SortUtils sortUtils;

    /**
     * 题库题目排序
     *
     * @param request 排序请求参数
     * @return 排序结果
     */
    @ApiOperation(value = "题库题目排序", notes = "题库题目排序")
    @PostMapping("/sort")
    public R sortQuestionItem(@RequestBody SortQuestionItemRequest request) {
        // 参数校验：前后位置不能都为空
        if (ObjectUtil.isNull(request.getAfterPosition()) && ObjectUtil.isNull(request.getBeforePosition())) {
            return R.ok();
        }

        // 获取题目实体
        SaQuestionbankitemPojo itemEntity = saQuestionbankitemMapper.getByQuestionItemId(
                request.getQuestionItemId(), request.getQuestionBankKey());

        if (itemEntity == null) {
            return R.fail("题目不存在");
        }

        // 计算新的排序位置
        Long sort = sortUtils.calcQuestionSortPosition(
                request.getBeforePosition(),
                request.getAfterPosition(),
                request.getQuestionBankKey());

        // 检查是否需要刷新全部列表
        if (sortUtils.sortAllQuestionList(
                request.getBeforePosition(),
                request.getAfterPosition(),
                request.getQuestionBankKey(),
                sort)) {
            return R.ok(new OperateQuestionItemVO(
                    itemEntity.getSortorder().longValue(),
                    itemEntity.getId(),
                    true,
                    true));
        }

        // 更新题目排序
        itemEntity.setSortorder(Math.toIntExact(sort));
        saQuestionbankitemService.update(itemEntity);

        return R.ok(new OperateQuestionItemVO(
                itemEntity.getSortorder().longValue(),
                itemEntity.getId(),
                true,
                false));
    }

}
