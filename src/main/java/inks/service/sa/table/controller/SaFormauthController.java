package inks.service.sa.table.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.service.sa.table.domain.pojo.SaFormauthPojo;
import inks.service.sa.table.service.SaFormauthService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 表单授权对象(Sa_FormAuth)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-02 16:48:38
 */
@RestController
@RequestMapping("saFormauth")
public class SaFormauthController {
    /**
     * 服务对象
     */
    @Resource
    private SaFormauthService saFormauthService;

    @Resource
    private SaRedisService saRedisService;
    
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(SaFormauthController.class);

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value=" 获取表单授权对象详细信息", notes="获取表单授权对象详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_FormAuth.List")
    public R<SaFormauthPojo> getEntity(String key) {
    try {
           // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormauthService.getEntity(key));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_FormAuth.List")
    public R<PageInfo<SaFormauthPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_FormAuth.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormauthService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value=" 新增表单授权对象", notes="新增表单授权对象", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_FormAuth.Add")
    public R<SaFormauthPojo> create(@RequestBody String json) {
       try {
       SaFormauthPojo saFormauthPojo = JSONArray.parseObject(json,SaFormauthPojo.class);       
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                                                                                                                saFormauthPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFormauthPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFormauthPojo.setCreatedate(new Date());   // 创建时间
            saFormauthPojo.setLister(loginUser.getRealname());   // 制表
            saFormauthPojo.setListerid(loginUser.getUserid());    // 制表id  
            saFormauthPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saFormauthService.insert(saFormauthPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value="修改表单授权对象", notes="修改表单授权对象", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_FormAuth.Edit")
    public R<SaFormauthPojo> update(@RequestBody String json) {
       try {
         SaFormauthPojo saFormauthPojo = JSONArray.parseObject(json,SaFormauthPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFormauthPojo.setLister(loginUser.getRealname());   // 制表
            saFormauthPojo.setListerid(loginUser.getUserid());    // 制表id  
            saFormauthPojo.setModifydate(new Date());   //修改时间
//            saFormauthPojo.setAssessor(""); // 审核员
//            saFormauthPojo.setAssessorid(""); // 审核员id
//            saFormauthPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.saFormauthService.update(saFormauthPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value="删除表单授权对象", notes="删除表单授权对象", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_FormAuth.Delete")
    public R<Integer> delete(String key) {
    try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormauthService.delete(key));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
                                                                                                            
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_FormAuth.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaFormauthPojo saFormauthPojo = this.saFormauthService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFormauthPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

