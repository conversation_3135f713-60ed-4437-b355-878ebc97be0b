package inks.service.sa.table.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.table.domain.pojo.SaReportsPojo;
import inks.service.sa.table.service.Tb_SaReportsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 报表中心(table项目专属,含Formid)(Sa_Reports)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:48
 */
@RestController
@RequestMapping("S18M08B1")
@Api(tags = "S18M08B1:报表中心(table项目专属,含Formid)")
public class S18M08B1Controller extends Tb_SaReportsController {
    @Resource
    private Tb_SaReportsService tb_saReportsService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "拉取默认报表,重复code&name跳过", notes = "拉取默认报表 code可选", produces = "application/json")
    @RequestMapping(value = "/pullDefault", method = RequestMethod.GET)
    public R<List<SaReportsPojo>> pullDefault(String code) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaReportsPojo> fmAccountPojos = this.tb_saReportsService.pullDefault(code, loginUser);
            return R.ok(fmAccountPojos);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListAll", method = RequestMethod.POST)
    public R<PageInfo<SaReportsPojo>> getPageListAll(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Reports.ModuleCode");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.tb_saReportsService.getPageListAll(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "复制模板(传入formid和Sa_Reports.id)", notes = "", produces = "application/json")
    @RequestMapping(value = "/copyReports", method = RequestMethod.GET)
    public R<SaReportsPojo> copyReports(String formid, String reportsid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            SaReportsPojo saReportsPojoDB = tb_saReportsService.getEntity(reportsid);
            saReportsPojoDB.setFormid(formid);
//            saReportsPojoDB.setCreateby(loginUser.getRealName());   // 创建者
//            saReportsPojoDB.setCreatebyid(loginUser.getUserid());  // 创建者id
//            saReportsPojoDB.setCreatedate(new Date());   // 创建时间
            saReportsPojoDB.setLister(loginUser.getRealname());   // 制表
            saReportsPojoDB.setListerid(loginUser.getUserid());    // 制表id
            saReportsPojoDB.setModifydate(new Date());   //修改时间
            return R.ok(tb_saReportsService.insert(saReportsPojoDB, false));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


//----------------------------------查询报表-----------------------------------------

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<SaReportsPojo>> getListByModuleCode(String code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaReportsPojo> list = this.tb_saReportsService.getListByModuleCode(code);
            if (list != null) {
                for (int i = 0; i < list.size(); i++) {
                    String verifyKey = CacheConstants.REPORT_CODES_KEY + list.get(i).getId();
                    ReportsPojo reportsPojo = new ReportsPojo();
                    reportsPojo.setRptdata(list.get(i).getRptdata());
                    reportsPojo.setPagerow(list.get(i).getPagerow());
                    reportsPojo.setTempurl(list.get(i).getTempurl());
                    reportsPojo.setPrintersn(list.get(i).getPrintersn());
                    reportsPojo.setPaperlength(list.get(i).getPaperlength());
                    reportsPojo.setPaperwidth(list.get(i).getPaperwidth());
                    reportsPojo.setGrfdata(list.get(i).getGrfdata());
                    saRedisService.setCacheObject(verifyKey, reportsPojo, (long) (60 * 12), TimeUnit.MINUTES);
                    if (list.get(i).getGrfdata() != null && !"".equals(list.get(i).getGrfdata())) {
                        list.get(i).setGrfdata("true");
                    } else {
                        list.get(i).setGrfdata(null);
                    }
                    if (list.get(i).getRptdata() != null && !"".equals(list.get(i).getRptdata())) {
                        list.get(i).setRptdata("true");
                    } else {
                        list.get(i).setRptdata(null);
                    }

                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 按模块编码查询报表
     *
     * @param formid 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按formid(和ModuleCode)查询报表", notes = "", produces = "application/json")
    @RequestMapping(value = "/getListByFormId", method = RequestMethod.GET)
    public R<List<SaReportsPojo>> getListByFormId(String formid, String code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = new QueryParam();
//            // 过滤部门
//            FilterSqlUtils.filterDept(queryParam, loginUser,"Sa_Form");
            List<SaReportsPojo> list = this.tb_saReportsService.getListByFormId(formid, code, queryParam);
            if (list != null) {
                for (SaReportsPojo saReportsPojo : list) {
                    String verifyKey = CacheConstants.REPORT_CODES_KEY + saReportsPojo.getId();
                    ReportsPojo reportsPojo = new ReportsPojo();
                    reportsPojo.setRptdata(saReportsPojo.getRptdata());
                    reportsPojo.setPagerow(saReportsPojo.getPagerow());
                    reportsPojo.setTempurl(saReportsPojo.getTempurl());
                    reportsPojo.setPrintersn(saReportsPojo.getPrintersn());
                    reportsPojo.setPaperlength(saReportsPojo.getPaperlength());
                    reportsPojo.setPaperwidth(saReportsPojo.getPaperwidth());
                    reportsPojo.setGrfdata(saReportsPojo.getGrfdata());
                    saRedisService.setCacheObject(verifyKey, reportsPojo, (long) (60 * 12), TimeUnit.MINUTES);
                    if (saReportsPojo.getGrfdata() != null && !"".equals(saReportsPojo.getGrfdata())) {
                        saReportsPojo.setGrfdata("true");
                    } else {
                        saReportsPojo.setGrfdata(null);
                    }
                    if (saReportsPojo.getRptdata() != null && !"".equals(saReportsPojo.getRptdata())) {
                        saReportsPojo.setRptdata("true");
                    } else {
                        saReportsPojo.setRptdata(null);
                    }
                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
