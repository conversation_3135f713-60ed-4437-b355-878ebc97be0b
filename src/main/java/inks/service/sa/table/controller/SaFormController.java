package inks.service.sa.table.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.table.domain.pojo.SaFormPojo;
import inks.service.sa.table.domain.pojo.SaFormitemPojo;
import inks.service.sa.table.domain.pojo.SaFormitemdetailPojo;
import inks.service.sa.table.service.SaFormService;
import inks.service.sa.table.service.SaFormitemService;

import inks.service.sa.table.utils.FilterSqlUtils;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 用户表单(SaForm)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:12:56
 */
@RestController
@RequestMapping("saForm")
public class SaFormController {
    /**
     * 服务对象
     */
    @Resource
    private SaFormService saFormService;

    /**
     * 服务对象Item
     */
    @Resource
    private SaFormitemService saFormitemService;
    @Resource
    private SaRedisService saRedisService;


    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(SaFormController.class);

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取用户表单详细信息", notes = "获取用户表单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_Form.List")
    public R<SaFormPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " getEntityItem", notes = "获取用户表单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityItem", method = RequestMethod.GET)
    public R<SaFormitemPojo> getEntityItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormitemService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_Form.List")
    public R<PageInfo<SaFormitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Form.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            // 最下面过滤部门
            FilterSqlUtils.filterDept(queryParam, loginUser);
            return R.ok(this.saFormService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取用户表单详细信息", notes = "获取用户表单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_Form.List")
    public R<SaFormPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_Form.List")
    public R<PageInfo<SaFormPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Form.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            // 最下面过滤部门
            FilterSqlUtils.filterDept(queryParam, loginUser);
            return R.ok(this.saFormService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_Form.List")
    public R<PageInfo<SaFormPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Form.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            // 最下面过滤部门
            FilterSqlUtils.filterDept(queryParam, loginUser);
            return R.ok(this.saFormService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增用户表单", notes = "新增用户表单", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_Form.Add")
    public R<SaFormPojo> create(@RequestBody String json) {
        try {
            SaFormPojo saFormPojo = JSONArray.parseObject(json, SaFormPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 管理员才能创建表单
            if (loginUser.getIsadmin() == null || loginUser.getIsadmin() == 0) {
                throw new BaseBusinessException("非管理员无权限创建表单");
            }
            // 表单必须关联上部门id
            if (StringUtils.isBlank(loginUser.getTenantinfo().getDeptid())) {
                throw new BaseBusinessException("创建表单人员必须先关联一个部门");
            }
            saFormPojo.setDeptid(loginUser.getTenantinfo().getDeptid());  // 部门id
            saFormPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFormPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFormPojo.setCreatedate(new Date());   // 创建时间
            saFormPojo.setLister(loginUser.getRealname());   // 制表
            saFormPojo.setListerid(loginUser.getUserid());    // 制表id
            saFormPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFormService.insert(saFormPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改用户表单", notes = "修改用户表单", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_Form.Edit")
    public R<SaFormPojo> update(@RequestBody String json) {
        try {
            SaFormPojo saFormPojo = JSONArray.parseObject(json, SaFormPojo.class);
//             获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFormPojo.setLister(loginUser.getRealname());   // 制表
            saFormPojo.setListerid(loginUser.getUserid());    // 制表id
            saFormPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFormService.update(saFormPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "(从回收站)完全删除用户表单", notes = "删除用户表单", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_Form.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增用户表单Item", notes = "新增用户表单Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_Form.Add")
    public R<SaFormitemPojo> createItem(@RequestBody String json) {
        try {
            SaFormitemPojo saFormitemPojo = JSONArray.parseObject(json, SaFormitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormitemService.insert(saFormitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改表单项", notes = "修改表单项", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_FormItem.Edit")
    public R<SaFormitemPojo> updateItem(@RequestBody String json) {
        try {
            SaFormitemPojo saFormitemPojo = JSONArray.parseObject(json, SaFormitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormitemService.update(saFormitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除用户表单Item", notes = "删除用户表单Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_Form.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_Form.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaFormPojo saFormPojo = this.saFormService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFormPojo);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saFormPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaFormitemPojo saFormitemPojo = new SaFormitemPojo();
                    saFormPojo.getItem().add(saFormitemPojo);
                }
            }
        }
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saFormPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

