package inks.service.sa.table.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.table.domain.constant.CommonConstants;
import inks.service.sa.table.domain.pojo.SaFormdataPojo;
import inks.service.sa.table.service.SaFormdataService;
import inks.service.sa.table.utils.AddressUtils;
import inks.service.sa.table.utils.CacheUtils;
import inks.service.sa.table.utils.FilterSqlUtils;
import inks.service.sa.table.utils.HttpUtils;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 表单收集数据结果(Sa_FormData)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-06 10:57:51
 */

public class SaFormdataController {
    /**
     * 服务对象
     */
    @Resource
    private SaFormdataService saFormdataService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private CacheUtils redisUtils;
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(SaFormdataController.class);
    String FORM_RESULT_NUMBER = "form:result:number:{}";

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取表单收集数据结果详细信息", notes = "获取表单收集数据结果详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_FormData.List")
    public R<SaFormdataPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormdataService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_FormData.List")
    public R<PageInfo<SaFormdataPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_FormData.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            // 最下面过滤部门
            FilterSqlUtils.filterDept(queryParam, loginUser, "Sa_FormData");
            return R.ok(this.saFormdataService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增表单收集数据结果", notes = "新增表单收集数据结果", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//     @PreAuthorize(hasPermi = "Sa_FormData.Add")
    public R<SaFormdataPojo> create(@RequestBody String json, HttpServletRequest request) {
        try {
            SaFormdataPojo saFormdataPojo = JSONArray.parseObject(json, SaFormdataPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFormdataPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFormdataPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFormdataPojo.setLister(loginUser.getRealname());   // 制表
            saFormdataPojo.setListerid(loginUser.getUserid());    // 制表id
            saFormdataPojo.setCreatedate(new Date());   // 创建时间
            saFormdataPojo.setModifydate(new Date());   //修改时间
            saFormdataPojo.setSubreqip(HttpUtils.getIpAddr(request));//提交IP
            saFormdataPojo.setSubaddr(AddressUtils.getRealAddressByIP(saFormdataPojo.getSubreqip()));//提交地址
            saFormdataPojo.setSeqlnum(Math.toIntExact(redisUtils.incr(StrUtil.format(FORM_RESULT_NUMBER, saFormdataPojo.getFormid()), CommonConstants.ConstantNumber.ONE)));
            return R.ok(this.saFormdataService.insert(saFormdataPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改表单收集数据结果", notes = "修改表单收集数据结果", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_FormData.Edit")
    public R<SaFormdataPojo> update(@RequestBody String json) {
        try {
            SaFormdataPojo saFormdataPojo = JSONArray.parseObject(json, SaFormdataPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFormdataPojo.setLister(loginUser.getRealname());   // 制表
            saFormdataPojo.setListerid(loginUser.getUserid());    // 制表id  
            saFormdataPojo.setModifydate(new Date());   //修改时间
            saFormdataPojo.setAssessor(""); // 审核员
            saFormdataPojo.setAssessorid(""); // 审核员id
            saFormdataPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFormdataService.update(saFormdataPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除表单收集数据结果", notes = "删除表单收集数据结果", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_FormData.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormdataService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核表单收集数据结果", notes = "审核表单收集数据结果", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_FormData.Approval")
    public R<SaFormdataPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaFormdataPojo saFormdataPojo = this.saFormdataService.getEntity(key);
            if (saFormdataPojo.getAssessor().equals("")) {
                saFormdataPojo.setAssessor(loginUser.getRealname()); //审核员
                saFormdataPojo.setAssessorid(loginUser.getUserid()); //审核员id
                saFormdataPojo.setAssessstatus("通过");
            } else {
                saFormdataPojo.setAssessor(""); //审核员
                saFormdataPojo.setAssessorid(""); //审核员
                saFormdataPojo.setAssessstatus("驳回");
                // 2DO 审核驳回的时候需要将改FormData的Submit置0(前端做了)
            }
            saFormdataPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFormdataService.approval(saFormdataPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

