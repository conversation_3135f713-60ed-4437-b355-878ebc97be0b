package inks.service.sa.table.controller;

import cn.hutool.core.util.ObjectUtil;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.mapper.SaDeptuserMapper;
import inks.service.sa.table.domain.pojo.SaFormPojo;
import inks.service.sa.table.domain.pojo.SaFormitemPojo;
import inks.service.sa.table.domain.pojo.SaReportsPojo;
import inks.service.sa.table.domain.vo.OperateFormItemVO;
import inks.service.sa.table.domain.vo.SortFormItemRequest;
import inks.service.sa.table.domain.vo.UserFormDetailVO;
import inks.service.sa.table.mapper.SaFormdataMapper;
import inks.service.sa.table.mapper.SaFormitemMapper;
import inks.service.sa.table.service.SaFormService;
import inks.service.sa.table.service.SaFormitemService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.table.service.Tb_SaReportsService;
import inks.service.sa.table.utils.SortUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 用户表单(SaForm)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:12:56
 */
@RestController
@RequestMapping("S18M01B1")
@Api(tags = "S18M01B1:用户表单")
public class S18M01B1Controller extends SaFormController {//显示列
    @Resource
    private SaFormService saFormService;
    @Resource
    private SaFormitemService saFormitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaFormitemMapper saFormitemMapper;
    @Resource
    private SaFormdataMapper saFormdataMapper;
    @Resource
    private SaDeptuserMapper saDeptuserMapper;
    @Resource
    private SortUtils sortUtils;
    @Resource
    private Tb_SaReportsService tbSaReportsService;

    @ApiOperation(value = "逻辑删除用户表单,仅将Sa_Form.IsDeleted=1", notes = "删除用户表单", produces = "application/json")
    @RequestMapping(value = "/logicDelete", method = RequestMethod.GET)
    public R<Integer> logicDelete(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser= saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormService.logicDelete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "获取From主子表,逻辑表,主体表", notes = "预览", produces = "application/json")
    @RequestMapping(value = "/details", method = RequestMethod.GET)
    public R<UserFormDetailVO> details(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser= saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFormService.details(key));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 表单项排序
     *
     * @param request
     */
    @PostMapping("/sort")
    public R sortFormItem(@RequestBody SortFormItemRequest request) {
//        ValidatorUtils.validateEntity(request);/**/
        if (ObjectUtil.isNull(request.getAfterPosition()) && ObjectUtil.isNull(request.getBeforePosition())) {
            return R.ok();
        }
        SaFormitemPojo itemEntity = saFormitemMapper.getByFormItemId(request.getFormItemId(), request.getFormKey());
        Long sort = sortUtils.calcSortPosition(request.getBeforePosition(), request.getAfterPosition(), request.getFormKey());
        if (sortUtils.sortAllList(request.getBeforePosition(), request.getAfterPosition(), request.getFormKey(), sort)) {
            return R.ok(new OperateFormItemVO(itemEntity.getSort().longValue(), Long.parseLong(itemEntity.getId()), true, true));
        }
        itemEntity.setSort(Math.toIntExact(sort));
        saFormitemService.update(itemEntity);
        return R.ok(new OperateFormItemVO(itemEntity.getSort().longValue(), Long.parseLong(itemEntity.getId()), true, false));
    }

    /**
     * 发布表单
     */
    @PostMapping("/publish")
    @ApiOperation(value = "发布表单", notes = "发布表单", produces = "application/json")
    public R publishForm(String key) {//form.id
        List<SaFormitemPojo> list = saFormitemMapper.getList(key);
        if (CollectionUtils.isEmpty(list)) {
            return R.fail("无有效表单项，无法发布");
        }
        // 检查此form.id表单项是否已有填写记录(FormData表是否有此form.id的记录) TODO 发布
        int count = saFormdataMapper.getCountByFormIdWxAndIpAndUseridAndTime(key, null, null, null, null);
        // 如果有填写记录，则发布需要先把此form.id进行逻辑删除,再生成一个新的
        if (count > 0) {
//            return R.fail("此表单已有填写记录，无法发布");
        }
        SaFormPojo saFormPojo = saFormService.getEntity(key);
        saFormPojo.setStatus(2);//  状态 状态:1未发布2收集中3停止发布
        return R.ok(saFormService.update(saFormPojo));
    }

    @PostMapping("/stop")
    @ApiOperation(value = "停止发布表单", notes = "停止发布表单", produces = "application/json")
    public R stopForm(String key) {
        SaFormPojo saFormPojo = saFormService.getEntity(key);
        saFormPojo.setStatus(3);//  状态 状态:1未发布2收集中3停止发布
        return R.ok(saFormService.update(saFormPojo));
    }

    @PostMapping("/copyfrom")
    @ApiOperation(value = "复制表单主子表(key:复制前原表id; name:给复制表单命名; copyreports:是否复制原表所有打印模板;copytouserid:复制给指定用户(修改Sa_Form.Createbyid和Deptid))", notes = "", produces = "application/json")
    public R<SaFormPojo> copyfrom(String key, String name, boolean copyreports, String copytouserid) {
        SaFormPojo saFormPojoDB = saFormService.getBillEntity(key);
        if (isNotBlank(name)) {
            saFormPojoDB.setName(name);
        }
        saFormPojoDB.setStatus(1);//  状态 状态:1未发布2收集中3停止发布
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        // 管理员才能创建表单
        if (loginUser.getIsadmin() == null || loginUser.getIsadmin() == 0) {
            throw new BaseBusinessException("非管理员无权限创建表单");
        }
        saFormPojoDB.setCreateby(loginUser.getRealName());   // 创建者
        saFormPojoDB.setCreatebyid(loginUser.getUserid());  // 创建者id
        saFormPojoDB.setCreatedate(new Date());   // 创建时间
        saFormPojoDB.setLister(loginUser.getRealname());   // 制表
        saFormPojoDB.setListerid(loginUser.getUserid());    // 制表id
        saFormPojoDB.setModifydate(new Date());   //修改时间
        // 复制给指定用户 修改Sa_Form.Createbyid和Deptid,使得指定用户在S18M01B1/getPageTh能查到该表单
        if (isNotBlank(copytouserid)) {
            saFormPojoDB.setCreatebyid(copytouserid);
            saFormPojoDB.setDeptid(saDeptuserMapper.getDeptIDByUserId(copytouserid));
        }
        SaFormPojo insertSaFormPojo = saFormService.insert(saFormPojoDB);

        // 选择复制原表所有打印模板
        if (copyreports) {
            // 复制生成表单的id
            String newFormId = insertSaFormPojo.getId();
            // 原表关联的所有打印模板
            List<SaReportsPojo> reportsList = tbSaReportsService.getListByFormId(key, null, null);
            for (SaReportsPojo saReportsPojo : reportsList) {
                // 复制的打印模板名称加个后缀 _copy
                saReportsPojo.setRptname(saReportsPojo.getRptname() + "_copy");
                saReportsPojo.setFormid(newFormId);
//                saReportsPojo.setCreateby(loginUser.getRealName());   // 创建者
//                saReportsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
//                saReportsPojo.setCreatedate(new Date());   // 创建时间
                saReportsPojo.setLister(loginUser.getRealname());   // 制表
                saReportsPojo.setListerid(loginUser.getUserid());    // 制表id
                saReportsPojo.setModifydate(new Date());   //修改时间
                tbSaReportsService.insert(saReportsPojo, false);
            }
        }
        return R.ok(insertSaFormPojo);
    }


    //     FormItemId                  Label
//     input_map1698642221968      <p>地理位置</p>
//     checkbox1696573812178       验货员
    @ApiOperation(value = " 获取FormItem的FormItemId和Label字段,组装为List<Map<String,String>>", notes = "", produces = "application/json")
    @RequestMapping(value = "/getFormItemMap", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_Form.List")
    public R<List<Map<String, Object>>> getFormItemMap(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<Map<String, Object>> formItemMap = this.saFormService.getFormItemMap(key);
            // 给图片url赋值
            List<Map<String, Object>> newFormItemMap = new ArrayList<>();

            for (Map<String, Object> map : formItemMap) {
                String formItemId = (String) map.get("FormItemId");

                // 判断FormItemId对应的值是否包含"image",是图片控件就拼接10个
                if (formItemId != null && formItemId.contains("image")) {
                    // 复制原有的Map，并拼接后缀"_0", "_1", "_2"..."_9" 一共10个
                    for (int i = 0; i < 10; i++) {
                        Map<String, Object> newMap = new HashMap<>(map);// 复制原有的Map
                        newMap.put("FormItemId", formItemId + "_" + i);
                        newFormItemMap.add(newMap);
                    }
                } else {
                    // 如果不包含"image"，直接添加原有的Map
                    newFormItemMap.add(map);
                }
            }
            // 最终返回新的List<Map<String, Object>>
            return R.ok(newFormItemMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
