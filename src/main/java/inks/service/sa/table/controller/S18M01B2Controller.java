package inks.service.sa.table.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.config.oss.service.FileController;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.table.domain.pojo.SaFormdataPojo;
import inks.service.sa.table.domain.pojo.SaFormsharePojo;
import inks.service.sa.table.mapper.SaFormMapper;
import inks.service.sa.table.mapper.SaFormitemMapper;
import inks.service.sa.table.service.SaFormdataService;
import inks.service.sa.table.service.SaFormshareService;
import inks.service.sa.table.utils.CacheUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 表单收集数据结果(Sa_FormData)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:03
 */
@RestController
@RequestMapping("S18M01B2")
@Api(tags = "S18M01B2:表单收集数据结果")
public class S18M01B2Controller extends SaFormdataController {
    @Resource
    private SaFormdataService saFormdataService;
    @Resource
    private SaFormshareService saFormshareService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private CacheUtils redisUtils;
    @Resource
    private SaFormMapper saFormMapper;
    @Resource
    private SaFormitemMapper saFormitemMapper;
    @Resource
    private FileController fileController;


    // ----------------------------------------------------------------- 主子表都要的模板
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
// @PreAuthorize(hasPermi = "Sa_Form.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        SaFormdataPojo saFormdataPojo = this.saFormdataService.getEntity(key);
        // [OriginalData字段作为主表!!!] 字段内容为:{"date1695189380503": "2023-09-20", "input1695189373224": "娜诺", "textarea1695189378139": "西安是一座古朴的城市..."}
        String originaldata = saFormdataPojo.getOriginaldata();
        Map<String, Object> originaldataMap = JSONArray.parseObject(originaldata, Map.class);
        // 再拼个主表名字
        String formName = saFormMapper.getFormName(saFormdataPojo.getFormid());
        // 给多选框,性别,下拉框,图片url赋值
        ControlToPopulateData(originaldataMap, saFormdataPojo.getFormid());
        // 表名去掉html标签
        originaldataMap.put("formname", formName.replaceAll("<[^>]*>", ""));

        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 构建一个空的子表 滥竽充数
        List<Map<String, Object>> lst = new ArrayList<>();
        lst.add(new HashMap<>());
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, originaldataMap, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/sharePDF", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_Form.Print")
    public SaFormsharePojo sharePDF(@RequestBody String json, String key, String ptid) throws IOException, JRException {
        SaFormsharePojo formsharePojo = JSONArray.parseObject(json, SaFormsharePojo.class);
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        SaFormdataPojo saFormdataPojo = this.saFormdataService.getEntity(key);
        // [OriginalData字段作为主表!!!] 字段内容为:{"date1695189380503": "2023-09-20", "input1695189373224": "娜诺", "textarea1695189378139": "西安是一座古朴的城市..."}
        String originaldata = saFormdataPojo.getOriginaldata();
        Map<String, Object> originaldataMap = JSONArray.parseObject(originaldata, Map.class);
        // 再拼个主表名字
        String formName = saFormMapper.getFormName(saFormdataPojo.getFormid());
        // 给多选框,性别,下拉框,图片url赋值
        ControlToPopulateData(originaldataMap, saFormdataPojo.getFormid());
        // 表名去掉html标签
        originaldataMap.put("formname", formName.replaceAll("<[^>]*>", ""));

        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 构建一个空的子表 滥竽充数
        List<Map<String, Object>> lst = new ArrayList<>();
        lst.add(new HashMap<>());
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();

        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, originaldataMap, jrDataSource);
            //打印PDF数据流 这步返给前端流
            // JasperExportManager.exportReportToPdfStream(print, os);

            //-------------上传PDF到AliYun----------------
            FileInfo fileInfo = fileController.uploadByStream(convertJasperPrintToInputStream(print), "share", formName + ".pdf", null, null, null).getData();
            // 如果share参数为1，将PDF上传到Minio 再创建一条分享表记录
            Date now = new Date();
            SaFormsharePojo saFormsharePojo = new SaFormsharePojo();
            saFormsharePojo.setFormdataid(saFormdataPojo.getId());
            saFormsharePojo.setUrl(fileInfo.getDirname() + "/" + fileInfo.getFilename());
            //formsharePojo.getDeadtime()为null的话就当前时间加上一个月
            if (formsharePojo.getDeadtime() == null) {
                saFormsharePojo.setDeadtime(new Date(now.getTime() + 30L * 24 * 60 * 60 * 1000));
            }
            saFormsharePojo.setDeadtime(formsharePojo.getDeadtime() == null ? new Date(now.getTime() + 30L * 24 * 60 * 60 * 1000) : formsharePojo.getDeadtime());
            saFormsharePojo.setPassword(formsharePojo.getPassword());
            saFormsharePojo.setPasswordmark(formsharePojo.getPasswordmark());
            saFormsharePojo.setAllowdown(formsharePojo.getAllowdown());
            saFormsharePojo.setModifydate(now);
            saFormsharePojo.setCreatedate(now);
            return saFormshareService.insert(saFormsharePojo);
        } catch (JRException e) {
            e.printStackTrace();
        } finally {
            os.flush();
        }
        return null;
    }


    @ApiOperation(value = "云打印报表数据在主表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill数据在主表", method = RequestMethod.POST)
    public R<String> printWebBillOld(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaFormdataPojo saFormdataPojo = this.saFormdataService.getEntity(key);
            // [OriginalData字段作为主表!!!] 字段内容为:{"date1695189380503": "2023-09-20", "input1695189373224": "娜诺", "textarea1695189378139": "西安是一座古朴的城市..."}
            String originaldata = saFormdataPojo.getOriginaldata();
            // 获取单据表头.表头转MAP
            Map<String, Object> originaldataMap = JSONArray.parseObject(originaldata, Map.class);
            // 再拼个主表名字
            String formName = saFormMapper.getFormName(saFormdataPojo.getFormid());
            // 给多选框,性别,下拉框,图片url赋值
            ControlToPopulateData(originaldataMap, saFormdataPojo.getFormid());
            // 表名去掉html标签
            originaldataMap.put("formname", formName.replaceAll("<[^>]*>", ""));
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(originaldataMap, loginUser);
            //=========获取单据Item信息========
            // 构建一个空的子表 滥竽充数
            List<Map<String, Object>> lst = new ArrayList<>();
            lst.add(new HashMap<>());

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", originaldataMap);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else if (cmd != null && cmd == 2) {
                mapPrint.put("code", "exportpdf");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaForm.BillTitle:" + formName);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限

            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "云打印报表数据在子表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.POST)
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaFormdataPojo saFormdataPojo = this.saFormdataService.getEntity(key);
            // [OriginalData字段作为主表!!!] 字段内容为:{"date1695189380503": "2023-09-20", "input1695189373224": "娜诺", "textarea1695189378139": "西安是一座古朴的城市..."}
            String originaldata = saFormdataPojo.getOriginaldata();
            // 获取单据表头.表头转MAP
            Map<String, Object> originaldataMap = JSONArray.parseObject(originaldata, Map.class);
            Map<String, Object> stringObjectMap = JSON.parseObject(originaldata, new TypeReference<Map<String, Object>>() {
            });
            // 再拼个主表名字
            String formName = saFormMapper.getFormName(saFormdataPojo.getFormid());
            // 给多选框,性别,下拉框,图片url赋值
            ControlToPopulateData(originaldataMap, saFormdataPojo.getFormid());
            // 表名去掉html标签
            originaldataMap.put("formname", formName.replaceAll("<[^>]*>", ""));
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(originaldataMap, loginUser);
            //=========获取单据Item信息========
            // 构建一个空的子表 把主表originaldataMap转移到子表这里 然后主表改为传空的Map
            List<Map<String, Object>> lst = new ArrayList<>();
            lst.add(originaldataMap);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", new HashMap<String, Object>());
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else if (cmd != null && cmd == 2) {
                mapPrint.put("code", "exportpdf");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaForm.BillTitle:" + formName);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "废弃(数据未转换) 批量云打印SaFormdata单据(ids)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBatchBill", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "SaFormdata.Print")
    public R<String> printWebBatchBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";

            for (String key : lstkeys) {
                //=========获取单据表头信息========
                SaFormdataPojo saFormdataPojo = this.saFormdataService.getEntity(key);
                if (saFormdataPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(saFormdataPojo);

                // 获取单据表头.加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                // 构建一个空的子表 滥竽充数
                List<Map<String, Object>> lst = new ArrayList<>();
                lst.add(new HashMap<>());                // 单据Item. 带属性List转为Map  EricRen 20220427
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
//                ptRefNoMain += saFormdataPojo.getRefno() + ",";
                // 刷入打印Num++
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else if (cmd != null && cmd == 2) {
                mapPrint.put("code", "exportpdf");
            } else {
                mapPrint.put("code", "batchprint");
            }
//            String[] lstRefno = ptRefNoMain.split(",");
//            mapPrint.put("msg", "SaFormdata：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("msg", "还没定义-.-");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    ----------------------------------------------------------------------------------------------------------------------------

    /**
     * @Description 给多选框, 性别, 下拉框, 图片url赋值
     * "radio1695610627228": 1
     * "select1695610529526": 2
     * "checkbox1695610587356": [1, 3, 4]
     * <AUTHOR>
     * @time 2023/9/25 16:09
     */
    private void ControlToPopulateData(Map<String, Object> originaldataMap, String formId) {

        // 注意:图片赋值时: 循环中修改了originaldataMap，这可能导致ConcurrentModificationException异常。这是因为在遍历originaldataMap的同时，你在往它里面添加新的键值对。这会干扰迭代器的内部状态，导致异常。
        // 要解决这个问题，你可以考虑使用一个临时的Map来存储需要添加到originaldataMap的新键值对，然后在循环结束后一次性将它们添加到originaldataMap中。这样就不会在遍历的同时修改它了。
        Map<String, String> imageAddMap = new HashMap<>();
        for (String key : originaldataMap.keySet()) {
            // 给性别赋值 或者给下拉框赋值(两者逻辑相同,都是单个数字)
            if (key.contains("radio") || key.contains("select")) {
                String scheme = saFormitemMapper.getSchemeByFormItemId(key, formId);
                Map<Integer, String> labelAndValue = ParseScheme(scheme);//value 1 对应 label 男
                labelAndValue.forEach((value, label) -> {
                    if (originaldataMap.get(key).equals(value)) {
                        originaldataMap.put(key, label);
                    }
                });
            }
            // 给多选框赋值 (值是数字数组 如[1,3,4])
            else if (key.contains("checkbox")) {
                String scheme = saFormitemMapper.getSchemeByFormItemId(key, formId);
                Map<Integer, String> labelAndValue = ParseScheme(scheme);//value 1 对应 label 男
                // 获取原始数据中的值，多选框的值是一个数字数组 [1,3,4]
                Object value = originaldataMap.get(key);
                // 确保值是一个数组
                if (value instanceof List<?>) {
                    List<Integer> selectedValues = (List<Integer>) value;
                    // 创建一个用于存储替换后的值的列表
                    List<String> replacedValues = new ArrayList<>();
                    // 遍历选中的值并替换成对应的label
                    for (Integer selectedValue : selectedValues) {
                        String label = labelAndValue.get(selectedValue);
                        if (label != null) {
                            replacedValues.add(label);
                        }
                    }
                    // 更新原始数据Map中的值为替换后的列表 replacedValues的值是[电影, 篮球] 需要去掉中括号:  电影, 篮球
                    originaldataMap.put(key, String.join(", ", replacedValues));
                }
            }
            // 给图片url赋值
            else if (key.contains("image")) {
                Object imageDataObject = originaldataMap.get(key);
                // 确保值是一个数组
                if (imageDataObject instanceof List<?>) {
                    List<Map<String, String>> imageList = (List<Map<String, String>>) imageDataObject;
                    // 遍历数组中所有对象的 "图片url"
                    if (!imageList.isEmpty()) {
                        for (Map<String, String> imageMap : imageList) {
                            String url = imageMap.get("url") == null ? "" : imageMap.get("url");
                            // 覆盖原始 Map 中的值 key(是原key加上后缀_0,_1,_2...)    indexOf是从0开始的
                            imageAddMap.put(key + "_" + imageList.indexOf(imageMap), url);
                        }
                    }
                }
            }
        }
        // 一次性将新键值对imageAddMap添加到originaldataMap
        originaldataMap.putAll(imageAddMap);
    }

    // 解析scheme字段  { "config":
    //                  { "options": [
    //                       {
    //                         "label": "电影",
    //                         "value": 1
    //                       },
    //                       {
    //                         "label": "音乐",
    //                         "value": 2
    //                       }
    //                    ]
    //                }
    private Map<Integer, String> ParseScheme(String scheme) {
        Map<Integer, String> parseLabel = new HashMap<>();

        try {
            // 使用FastJSON解析JSON字符串
            JSONObject schemeJson = JSON.parseObject(scheme);
            // 获取options字段
            JSONArray options = schemeJson.getJSONObject("config").getJSONArray("options");
            // 遍历options数组并提取label和value对应的信息
            for (int i = 0; i < options.size(); i++) {
                JSONObject option = options.getJSONObject(i);
                int value = option.getInteger("value");
                String label = option.getString("label");
                parseLabel.put(value, label);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return parseLabel;
    }


    public static InputStream convertJasperPrintToInputStream(JasperPrint jasperPrint) throws JRException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 导出 JasperPrint 到 ByteArrayOutputStream
        JasperExportManager.exportReportToPdfStream(jasperPrint, byteArrayOutputStream);
        // 将 ByteArrayOutputStream 转换为 ByteArrayInputStream
        ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        return inputStream;
    }


}
