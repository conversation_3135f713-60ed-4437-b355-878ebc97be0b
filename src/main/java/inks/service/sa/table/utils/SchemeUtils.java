package inks.service.sa.table.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.service.sa.table.domain.vo.ExamConfigVO;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Scheme字段工具类
 * 用于处理Sa_FormItem和Sa_QuestionbankItem的scheme字段
 * 支持examConfig的解析、生成和验证
 * 
 * <AUTHOR>
 * @since 2025-07-09
 */
public class SchemeUtils {

    /**
     * 解析scheme字段中的examConfig
     * 
     * @param scheme JSON字符串
     * @return ExamConfigVO对象，如果没有examConfig则返回null
     */
    public static ExamConfigVO parseExamConfig(String scheme) {
        if (!StringUtils.hasText(scheme)) {
            return null;
        }

        try {
            JSONObject schemeJson = JSON.parseObject(scheme);
            JSONObject examConfigJson = schemeJson.getJSONObject("examConfig");
            
            if (examConfigJson == null) {
                return null; // 向后兼容：没有examConfig字段
            }

            ExamConfigVO examConfig = new ExamConfigVO();
            
            // 解析分数
            if (examConfigJson.containsKey("score")) {
                examConfig.setScore(examConfigJson.getBigDecimal("score"));
            }
            
            // 解析正确答案
            if (examConfigJson.containsKey("answer")) {
                examConfig.setAnswer(examConfigJson.get("answer"));
            }
            
            // 解析是否启用评分
            if (examConfigJson.containsKey("enableScore")) {
                examConfig.setEnableScore(examConfigJson.getBoolean("enableScore"));
            }
            
            // 解析评分类型
            if (examConfigJson.containsKey("scoringType")) {
                examConfig.setScoringType(examConfigJson.getInteger("scoringType"));
            }
            
            // 解析答案解析
            if (examConfigJson.containsKey("answerAnalysis")) {
                examConfig.setAnswerAnalysis(examConfigJson.getString("answerAnalysis"));
            }
            
            // 解析是否显示答案
            if (examConfigJson.containsKey("showAnswer")) {
                examConfig.setShowAnswer(examConfigJson.getBoolean("showAnswer"));
            }
            
            // 解析横向填空多点评分
            if (examConfigJson.containsKey("scoreList")) {
                JSONArray scoreListJson = examConfigJson.getJSONArray("scoreList");
                if (scoreListJson != null) {
                    List<BigDecimal> scoreList = scoreListJson.toJavaList(BigDecimal.class);
                    examConfig.setScoreList(scoreList);
                }
            }
            
            return examConfig;
            
        } catch (Exception e) {
            // 解析异常时返回null，保持向后兼容
            return null;
        }
    }

    /**
     * 将examConfig添加到现有的scheme中
     * 
     * @param scheme 原始scheme JSON字符串
     * @param examConfig 考试配置
     * @return 包含examConfig的新scheme JSON字符串
     */
    public static String addExamConfigToScheme(String scheme, ExamConfigVO examConfig) {
        if (!StringUtils.hasText(scheme)) {
            return scheme;
        }
        
        if (examConfig == null) {
            return scheme;
        }

        try {
            JSONObject schemeJson = JSON.parseObject(scheme);
            
            // 创建examConfig JSON对象
            JSONObject examConfigJson = new JSONObject();
            
            if (examConfig.getScore() != null) {
                examConfigJson.put("score", examConfig.getScore());
            }
            if (examConfig.getAnswer() != null) {
                examConfigJson.put("answer", examConfig.getAnswer());
            }
            if (examConfig.getEnableScore() != null) {
                examConfigJson.put("enableScore", examConfig.getEnableScore());
            }
            if (examConfig.getScoringType() != null) {
                examConfigJson.put("scoringType", examConfig.getScoringType());
            }
            if (examConfig.getAnswerAnalysis() != null) {
                examConfigJson.put("answerAnalysis", examConfig.getAnswerAnalysis());
            }
            if (examConfig.getShowAnswer() != null) {
                examConfigJson.put("showAnswer", examConfig.getShowAnswer());
            }
            if (examConfig.getScoreList() != null && !examConfig.getScoreList().isEmpty()) {
                examConfigJson.put("scoreList", examConfig.getScoreList());
            }
            
            // 将examConfig添加到scheme中
            schemeJson.put("examConfig", examConfigJson);
            
            return schemeJson.toJSONString();
            
        } catch (Exception e) {
            // 异常时返回原始scheme
            return scheme;
        }
    }

    /**
     * 验证scheme中的examConfig是否有效
     * 
     * @param scheme JSON字符串
     * @return 验证结果
     */
    public static boolean validateExamConfig(String scheme) {
        ExamConfigVO examConfig = parseExamConfig(scheme);
        return examConfig != null && examConfig.isValid();
    }

    /**
     * 解析scheme中的options选项
     * 复用现有的ParseScheme逻辑
     * 
     * @param scheme JSON字符串
     * @return Map<value, label>
     */
    public static Map<Integer, String> parseOptions(String scheme) {
        Map<Integer, String> parseLabel = new HashMap<>();

        try {
            JSONObject schemeJson = JSON.parseObject(scheme);
            JSONObject config = schemeJson.getJSONObject("config");
            if (config != null) {
                JSONArray options = config.getJSONArray("options");
                if (options != null) {
                    for (int i = 0; i < options.size(); i++) {
                        JSONObject option = options.getJSONObject(i);
                        Integer value = option.getInteger("value");
                        String label = option.getString("label");
                        if (value != null && label != null) {
                            parseLabel.put(value, label);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 解析异常时返回空Map
        }
        
        return parseLabel;
    }

    /**
     * 获取scheme中的控件类型
     * 
     * @param scheme JSON字符串
     * @return 控件类型
     */
    public static String getQuestionType(String scheme) {
        if (!StringUtils.hasText(scheme)) {
            return null;
        }

        try {
            JSONObject schemeJson = JSON.parseObject(scheme);
            return schemeJson.getString("typeId");
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取scheme中的题目标题
     * 
     * @param scheme JSON字符串
     * @return 题目标题
     */
    public static String getQuestionLabel(String scheme) {
        if (!StringUtils.hasText(scheme)) {
            return null;
        }

        try {
            JSONObject schemeJson = JSON.parseObject(scheme);
            JSONObject config = schemeJson.getJSONObject("config");
            if (config != null) {
                return config.getString("label");
            }
        } catch (Exception e) {
            // 解析异常时返回null
        }
        
        return null;
    }

    /**
     * 检查scheme是否为考试类型
     * 
     * @param scheme JSON字符串
     * @return 是否包含examConfig
     */
    public static boolean isExamQuestion(String scheme) {
        return parseExamConfig(scheme) != null;
    }

    /**
     * 为普通表单项添加默认的examConfig
     * 
     * @param scheme 原始scheme
     * @param questionType 题目类型
     * @return 包含默认examConfig的scheme
     */
    public static String addDefaultExamConfig(String scheme, String questionType) {
        ExamConfigVO defaultConfig = ExamConfigVO.createByQuestionType(questionType);
        return addExamConfigToScheme(scheme, defaultConfig);
    }

    /**
     * 移除scheme中的examConfig
     * 
     * @param scheme 原始scheme
     * @return 移除examConfig后的scheme
     */
    public static String removeExamConfig(String scheme) {
        if (!StringUtils.hasText(scheme)) {
            return scheme;
        }

        try {
            JSONObject schemeJson = JSON.parseObject(scheme);
            schemeJson.remove("examConfig");
            return schemeJson.toJSONString();
        } catch (Exception e) {
            return scheme;
        }
    }
}
