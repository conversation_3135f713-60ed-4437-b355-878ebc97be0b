package inks.service.sa.table.utils.RSA;

import cn.hutool.core.net.NetUtil;
import cn.hutool.crypto.digest.MD5;

public class SN {

//    @ApiOperation(value = "获取当前设备SN码", notes = "MAC-->MD5", produces = "application/json")
//    @RequestMapping(value = "/getSN", method = RequestMethod.GET)
    public static String getSN() {
        //hutool包里的获取MAC地址
        String localMacAddress = NetUtil.getLocalMacAddress();//package cn.hutool.core.net;
        //hutool包里的md5加密
        return MD5.create().digestHex(localMacAddress);
    }

}
