package inks.service.sa.table.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 考试评分结果VO
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public class ExamScoreResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总分
     */
    private BigDecimal totalScore;

    /**
     * 满分
     */
    private BigDecimal fullScore;

    /**
     * 客观题得分
     */
    private BigDecimal objectiveScore;

    /**
     * 主观题得分
     */
    private BigDecimal subjectiveScore;

    /**
     * 是否及格 (0否 1是)
     */
    private Integer isPassed;

    /**
     * 评分状态 (自动评分完成、待人工评分、评分完成)
     */
    private String scoringStatus;

    /**
     * 各题得分详情 Map<题目ID, 得分>
     */
    private Map<String, BigDecimal> questionScores;

    /**
     * 各题评分详情
     */
    private List<QuestionScoreDetail> questionDetails;

    /**
     * 需要人工评分的题目列表
     */
    private List<String> manualGradingQuestions;

    /**
     * 评分错误信息
     */
    private String errorMessage;

    /**
     * 题目评分详情
     */
    public static class QuestionScoreDetail implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 题目ID
         */
        private String questionId;

        /**
         * 题目类型
         */
        private String questionType;

        /**
         * 用户答案
         */
        private Object userAnswer;

        /**
         * 正确答案
         */
        private Object correctAnswer;

        /**
         * 得分
         */
        private BigDecimal score;

        /**
         * 满分
         */
        private BigDecimal fullScore;

        /**
         * 是否正确
         */
        private Boolean isCorrect;

        /**
         * 评分类型 (0手动 1自动 2主观题 5横向填空)
         */
        private Integer scoringType;

        /**
         * 评分说明
         */
        private String scoringNote;

        // Getters and Setters
        public String getQuestionId() {
            return questionId;
        }

        public void setQuestionId(String questionId) {
            this.questionId = questionId;
        }

        public String getQuestionType() {
            return questionType;
        }

        public void setQuestionType(String questionType) {
            this.questionType = questionType;
        }

        public Object getUserAnswer() {
            return userAnswer;
        }

        public void setUserAnswer(Object userAnswer) {
            this.userAnswer = userAnswer;
        }

        public Object getCorrectAnswer() {
            return correctAnswer;
        }

        public void setCorrectAnswer(Object correctAnswer) {
            this.correctAnswer = correctAnswer;
        }

        public BigDecimal getScore() {
            return score;
        }

        public void setScore(BigDecimal score) {
            this.score = score;
        }

        public BigDecimal getFullScore() {
            return fullScore;
        }

        public void setFullScore(BigDecimal fullScore) {
            this.fullScore = fullScore;
        }

        public Boolean getIsCorrect() {
            return isCorrect;
        }

        public void setIsCorrect(Boolean isCorrect) {
            this.isCorrect = isCorrect;
        }

        public Integer getScoringType() {
            return scoringType;
        }

        public void setScoringType(Integer scoringType) {
            this.scoringType = scoringType;
        }

        public String getScoringNote() {
            return scoringNote;
        }

        public void setScoringNote(String scoringNote) {
            this.scoringNote = scoringNote;
        }
    }

    // Getters and Setters
    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public BigDecimal getFullScore() {
        return fullScore;
    }

    public void setFullScore(BigDecimal fullScore) {
        this.fullScore = fullScore;
    }

    public BigDecimal getObjectiveScore() {
        return objectiveScore;
    }

    public void setObjectiveScore(BigDecimal objectiveScore) {
        this.objectiveScore = objectiveScore;
    }

    public BigDecimal getSubjectiveScore() {
        return subjectiveScore;
    }

    public void setSubjectiveScore(BigDecimal subjectiveScore) {
        this.subjectiveScore = subjectiveScore;
    }

    public Integer getIsPassed() {
        return isPassed;
    }

    public void setIsPassed(Integer isPassed) {
        this.isPassed = isPassed;
    }

    public String getScoringStatus() {
        return scoringStatus;
    }

    public void setScoringStatus(String scoringStatus) {
        this.scoringStatus = scoringStatus;
    }

    public Map<String, BigDecimal> getQuestionScores() {
        return questionScores;
    }

    public void setQuestionScores(Map<String, BigDecimal> questionScores) {
        this.questionScores = questionScores;
    }

    public List<QuestionScoreDetail> getQuestionDetails() {
        return questionDetails;
    }

    public void setQuestionDetails(List<QuestionScoreDetail> questionDetails) {
        this.questionDetails = questionDetails;
    }

    public List<String> getManualGradingQuestions() {
        return manualGradingQuestions;
    }

    public void setManualGradingQuestions(List<String> manualGradingQuestions) {
        this.manualGradingQuestions = manualGradingQuestions;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
