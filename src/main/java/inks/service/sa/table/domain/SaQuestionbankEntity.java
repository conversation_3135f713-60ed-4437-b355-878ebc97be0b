package inks.service.sa.table.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 题库表(SaQuestionbank)实体类
 *
 * <AUTHOR>
 * @since 2025-07-09 10:49:16
 */
@Data
public class SaQuestionbankEntity implements Serializable {
    private static final long serialVersionUID = -48930131018989842L;
     // 题库ID
    private String id;
     // 题库名称
    private String name;
     // 题库描述
    private String description;
     // 题库类型 0普通题库1考试题库
    private Integer banktype;
     // 是否公开
    private Integer ispublic;
     // 状态 1启用 0禁用
    private Integer status;
     // 是否删除 0否 1是
    private Integer isdeleted;
     // 题目数量
    private Integer itemcount;
     // 备注
    private String remark;
     // 行号
    private Integer rownum;
     // 创建者ID
    private String createbyid;
     // 创建者
    private String createby;
     // 创建时间
    private Date createdate;
     // 制表人ID
    private String listerid;
     // 制表人
    private String lister;
     // 修改时间
    private Date modifydate;
     // 自定义字段1
    private String custom1;
     // 自定义字段2
    private String custom2;
     // 自定义字段3
    private String custom3;
     // 自定义字段4
    private String custom4;
     // 自定义字段5
    private String custom5;
     // 部门ID
    private String deptid;
     // 租户ID
    private String tenantid;
     // 乐观锁版本号
    private Integer revision;


}

