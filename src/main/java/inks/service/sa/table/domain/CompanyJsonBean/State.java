/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.table.domain.CompanyJsonBean;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */

public class State {

    private Data data;
    private int dataUpdateCount;
    private long dataUpdatedAt;
    private String error;
    private int errorUpdateCount;
    private int errorUpdatedAt;
    private int fetchFailureCount;
    private String fetchMeta;
    private boolean isFetching;
    private boolean isInvalidated;
    private boolean isPaused;
    private String status;

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public int getDataUpdateCount() {
        return dataUpdateCount;
    }

    public void setDataUpdateCount(int dataUpdateCount) {
        this.dataUpdateCount = dataUpdateCount;
    }

    public long getDataUpdatedAt() {
        return dataUpdatedAt;
    }

    public void setDataUpdatedAt(long dataUpdatedAt) {
        this.dataUpdatedAt = dataUpdatedAt;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public int getErrorUpdateCount() {
        return errorUpdateCount;
    }

    public void setErrorUpdateCount(int errorUpdateCount) {
        this.errorUpdateCount = errorUpdateCount;
    }

    public int getErrorUpdatedAt() {
        return errorUpdatedAt;
    }

    public void setErrorUpdatedAt(int errorUpdatedAt) {
        this.errorUpdatedAt = errorUpdatedAt;
    }

    public int getFetchFailureCount() {
        return fetchFailureCount;
    }

    public void setFetchFailureCount(int fetchFailureCount) {
        this.fetchFailureCount = fetchFailureCount;
    }

    public String getFetchMeta() {
        return fetchMeta;
    }

    public void setFetchMeta(String fetchMeta) {
        this.fetchMeta = fetchMeta;
    }

    public boolean isFetching() {
        return isFetching;
    }

    public void setFetching(boolean fetching) {
        isFetching = fetching;
    }

    public boolean isInvalidated() {
        return isInvalidated;
    }

    public void setInvalidated(boolean invalidated) {
        isInvalidated = invalidated;
    }

    public boolean isPaused() {
        return isPaused;
    }

    public void setPaused(boolean paused) {
        isPaused = paused;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}