package inks.service.sa.table.domain.vo;

import inks.service.sa.table.domain.pojo.SaFormitemPojo;
import inks.service.sa.table.domain.pojo.SaFormlogicPojo;
import inks.service.sa.table.domain.pojo.SaFormthemePojo;
import lombok.Data;

import java.util.List;

@Data
public class DefinitionVO {
    private Integer formType;
    private List<SaFormitemPojo> formItems;
    private SaFormthemePojo userFormTheme;
    private SaFormlogicPojo userFormLogic;
}
