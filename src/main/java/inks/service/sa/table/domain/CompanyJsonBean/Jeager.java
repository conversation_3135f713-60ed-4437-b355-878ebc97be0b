/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.table.domain.CompanyJsonBean;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class Jeager {

    private String endpoint;
    public void setEndpoint(String endpoint) {
         this.endpoint = endpoint;
     }
     public String getEndpoint() {
         return endpoint;
     }

}