/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.table.domain.CompanyJsonBean;

import java.util.List;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */

public class LegalInfo {

    private String name;
    private long hid;
    private String headUrl;
    private String introduction;
    private String event;
    private int bossCertificate;
    private int companyNum;
    private List<Office> office;
    private String companys;
    private int partnerNum;
    private int coopCount;
    private String partners;
    private long cid;
    private String typeJoin;
    private String alias;
    private int serviceType;
    private int serviceCount;
    private List<OfficeV1> officeV1;
    private String pid;
    private String role;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getHid() {
        return hid;
    }

    public void setHid(long hid) {
        this.hid = hid;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public int getBossCertificate() {
        return bossCertificate;
    }

    public void setBossCertificate(int bossCertificate) {
        this.bossCertificate = bossCertificate;
    }

    public int getCompanyNum() {
        return companyNum;
    }

    public void setCompanyNum(int companyNum) {
        this.companyNum = companyNum;
    }

    public List<Office> getOffice() {
        return office;
    }

    public void setOffice(List<Office> office) {
        this.office = office;
    }

    public String getCompanys() {
        return companys;
    }

    public void setCompanys(String companys) {
        this.companys = companys;
    }

    public int getPartnerNum() {
        return partnerNum;
    }

    public void setPartnerNum(int partnerNum) {
        this.partnerNum = partnerNum;
    }

    public int getCoopCount() {
        return coopCount;
    }

    public void setCoopCount(int coopCount) {
        this.coopCount = coopCount;
    }

    public String getPartners() {
        return partners;
    }

    public void setPartners(String partners) {
        this.partners = partners;
    }

    public long getCid() {
        return cid;
    }

    public void setCid(long cid) {
        this.cid = cid;
    }

    public String getTypeJoin() {
        return typeJoin;
    }

    public void setTypeJoin(String typeJoin) {
        this.typeJoin = typeJoin;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public int getServiceType() {
        return serviceType;
    }

    public void setServiceType(int serviceType) {
        this.serviceType = serviceType;
    }

    public int getServiceCount() {
        return serviceCount;
    }

    public void setServiceCount(int serviceCount) {
        this.serviceCount = serviceCount;
    }

    public List<OfficeV1> getOfficeV1() {
        return officeV1;
    }

    public void setOfficeV1(List<OfficeV1> officeV1) {
        this.officeV1 = officeV1;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }
}