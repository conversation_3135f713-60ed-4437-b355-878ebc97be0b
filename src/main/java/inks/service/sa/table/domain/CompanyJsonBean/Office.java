/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.table.domain.CompanyJsonBean;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class Office {

    private String area;
    private int total;
    private String companyName;
    private long cid;
    private int score;
    private String state;
    public void setArea(String area) {
         this.area = area;
     }
     public String getArea() {
         return area;
     }

    public void setTotal(int total) {
         this.total = total;
     }
     public int getTotal() {
         return total;
     }

    public void setCompanyName(String companyName) {
         this.companyName = companyName;
     }
     public String getCompanyName() {
         return companyName;
     }

    public void setCid(long cid) {
         this.cid = cid;
     }
     public long getCid() {
         return cid;
     }

    public void setScore(int score) {
         this.score = score;
     }
     public int getScore() {
         return score;
     }

    public void setState(String state) {
         this.state = state;
     }
     public String getState() {
         return state;
     }

}