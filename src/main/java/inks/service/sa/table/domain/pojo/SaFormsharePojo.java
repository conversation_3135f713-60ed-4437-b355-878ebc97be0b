package inks.service.sa.table.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 表单分享表(SaFormshare)实体类
 *
 * <AUTHOR>
 * @since 2023-09-20 14:18:23
 */
public class SaFormsharePojo implements Serializable {
    private static final long serialVersionUID = 684339388551707912L;
         // 分享ID
         @Excel(name = "分享ID") 
    private String id;
         // FormDataId
         @Excel(name = "FormDataId") 
    private String formdataid;
         // minio地址(只要ObjectName)
         @Excel(name = "minio地址(只要ObjectName)") 
    private String url;
         // 截止期限，默认为创建时间的一个月后
         @Excel(name = "截止期限，默认为创建时间的一个月后") 
    private Date deadtime;
         // 是否设置密码
         @Excel(name = "是否设置密码") 
    private Integer passwordmark;
         // 分享密码，如果未设置则为空
         @Excel(name = "分享密码，如果未设置则为空") 
    private String password;
         // 是否允许下载
         @Excel(name = "是否允许下载") 
    private Integer allowdown;
         // 备注
         @Excel(name = "备注") 
    private String remark;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 自定义1
         @Excel(name = "自定义1") 
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2") 
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3") 
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4") 
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5") 
    private String custom5;
         // 租户id
         @Excel(name = "租户id") 
    private String tenantid;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;

     // 分享ID
   
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
     // FormDataId
   
    public String getFormdataid() {
        return formdataid;
    }

    public void setFormdataid(String formdataid) {
        this.formdataid = formdataid;
    }
     // minio地址(只要ObjectName)
   
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
     // 截止期限，默认为创建时间的一个月后
   
    public Date getDeadtime() {
        return deadtime;
    }

    public void setDeadtime(Date deadtime) {
        this.deadtime = deadtime;
    }
     // 是否设置密码
   
    public Integer getPasswordmark() {
        return passwordmark;
    }

    public void setPasswordmark(Integer passwordmark) {
        this.passwordmark = passwordmark;
    }
     // 分享密码，如果未设置则为空
   
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
     // 是否允许下载
   
    public Integer getAllowdown() {
        return allowdown;
    }

    public void setAllowdown(Integer allowdown) {
        this.allowdown = allowdown;
    }
     // 备注
   
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
     // 创建者
   
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
     // 创建者id
   
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
     // 新建日期
   
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
     // 制表
   
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
     // 制表id
   
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
     // 修改日期
   
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
     // 自定义1
   
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
     // 自定义2
   
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
     // 自定义3
   
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
     // 自定义4
   
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
     // 自定义5
   
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
     // 租户id
   
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
     // 乐观锁
   
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

