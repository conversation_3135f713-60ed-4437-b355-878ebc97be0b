/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.table.domain.CompanyJsonBean;

import java.util.List;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class DehydratedState {

    private List<String> mutations;
    private List<Queries> queries;
    public void setMutations(List<String> mutations) {
         this.mutations = mutations;
     }
     public List<String> getMutations() {
         return mutations;
     }

    public void setQueries(List<Queries> queries) {
         this.queries = queries;
     }
     public List<Queries> getQueries() {
         return queries;
     }

}