/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.table.domain.CompanyJsonBean;

import java.util.List;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */

public class TagList  {


    private String color;
    private String background;
    private List<String> layerArray;
    private int sort;
    private String title;
    private int type;
    private String value;
    private String layer;

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public List<String> getLayerArray() {
        return layerArray;
    }

    public void setLayerArray(List<String> layerArray) {
        this.layerArray = layerArray;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLayer() {
        return layer;
    }

    public void setLayer(String layer) {
        this.layer = layer;
    }
}