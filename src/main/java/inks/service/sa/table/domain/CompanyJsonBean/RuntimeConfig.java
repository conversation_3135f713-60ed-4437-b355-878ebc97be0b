/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.table.domain.CompanyJsonBean;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class RuntimeConfig {

    private Sensors sensors;
    private Sentry sentry;
    private Jeager jeager;
    private Zipkin zipkin;
    private String subDomainSuffix;
    private String serverHttp;
    private String serverDomain;
    private String qifuLoginDomain;
    private String qifuServerDomain;
    private String disServerDomain;
    private String newsServerDomain;
    private String wapServerDomain;
    private String discussServerDomain;
    private String env;
    public void setSensors(Sensors sensors) {
         this.sensors = sensors;
     }
     public Sensors getSensors() {
         return sensors;
     }

    public void setSentry(Sentry sentry) {
         this.sentry = sentry;
     }
     public Sentry getSentry() {
         return sentry;
     }

    public void setJeager(Jeager jeager) {
         this.jeager = jeager;
     }
     public Jeager getJeager() {
         return jeager;
     }

    public void setZipkin(Zipkin zipkin) {
         this.zipkin = zipkin;
     }
     public Zipkin getZipkin() {
         return zipkin;
     }

    public void setSubDomainSuffix(String subDomainSuffix) {
         this.subDomainSuffix = subDomainSuffix;
     }
     public String getSubDomainSuffix() {
         return subDomainSuffix;
     }

    public void setServerHttp(String serverHttp) {
         this.serverHttp = serverHttp;
     }
     public String getServerHttp() {
         return serverHttp;
     }

    public void setServerDomain(String serverDomain) {
         this.serverDomain = serverDomain;
     }
     public String getServerDomain() {
         return serverDomain;
     }

    public void setQifuLoginDomain(String qifuLoginDomain) {
         this.qifuLoginDomain = qifuLoginDomain;
     }
     public String getQifuLoginDomain() {
         return qifuLoginDomain;
     }

    public void setQifuServerDomain(String qifuServerDomain) {
         this.qifuServerDomain = qifuServerDomain;
     }
     public String getQifuServerDomain() {
         return qifuServerDomain;
     }

    public void setDisServerDomain(String disServerDomain) {
         this.disServerDomain = disServerDomain;
     }
     public String getDisServerDomain() {
         return disServerDomain;
     }

    public void setNewsServerDomain(String newsServerDomain) {
         this.newsServerDomain = newsServerDomain;
     }
     public String getNewsServerDomain() {
         return newsServerDomain;
     }

    public void setWapServerDomain(String wapServerDomain) {
         this.wapServerDomain = wapServerDomain;
     }
     public String getWapServerDomain() {
         return wapServerDomain;
     }

    public void setDiscussServerDomain(String discussServerDomain) {
         this.discussServerDomain = discussServerDomain;
     }
     public String getDiscussServerDomain() {
         return discussServerDomain;
     }

    public void setEnv(String env) {
         this.env = env;
     }
     public String getEnv() {
         return env;
     }

}