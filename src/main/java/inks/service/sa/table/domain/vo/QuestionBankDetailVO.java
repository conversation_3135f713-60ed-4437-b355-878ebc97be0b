package inks.service.sa.table.domain.vo;

import inks.service.sa.table.domain.pojo.SaQuestionbankPojo;
import inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 题库详情VO
 * 
 * <AUTHOR>
 * @create 2025-07-29
 */
@Data
@AllArgsConstructor
public class QuestionBankDetailVO {
    /**
     * 题库基础信息
     */
    private SaQuestionbankPojo questionBank;

    /**
     * 题库题目列表
     */
    private List<SaQuestionbankitemPojo> questionItems;
}
