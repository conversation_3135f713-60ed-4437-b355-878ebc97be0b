package inks.service.sa.table.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 题库题目表(SaQuestionbankitem)Entity
 *
 * <AUTHOR>
 * @since 2025-07-09 09:03:14
 */
@Data
public class SaQuestionbankitemEntity implements Serializable {
    private static final long serialVersionUID = -84765600394632017L;
     // 题目ID
    private String id;
     // 所属题库ID
    private String pid;
     // 题目类型: RADIO,CHECKBOX,INPUT,TEXTAREA等
    private String itemtype;
     // 题目标题
    private String label;
     // 题目完整配置
    private String scheme;
     // 排序序号
    private Integer sortorder;
     // 状态 1启用 0禁用
    private Integer status;
     // 是否删除 0否 1是
    private Integer isdeleted;
     // 使用次数统计
    private Integer usagecount;
     // 正确率(%)
    private Double correctrate;
     // 备注
    private String remark;
     // 行号
    private Integer rownum;
     // 创建者ID
    private String createbyid;
     // 创建者
    private String createby;
     // 创建时间
    private Date createdate;
     // 制表人ID
    private String listerid;
     // 制表人
    private String lister;
     // 修改时间
    private Date modifydate;
     // 自定义字段1
    private String custom1;
     // 自定义字段2
    private String custom2;
     // 自定义字段3
    private String custom3;
     // 自定义字段4
    private String custom4;
     // 自定义字段5
    private String custom5;
     // 部门ID
    private String deptid;
     // 租户ID
    private String tenantid;
     // 乐观锁版本号
    private Integer revision;


}

