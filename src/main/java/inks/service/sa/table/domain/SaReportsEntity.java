package inks.service.sa.table.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 报表中心(含Formid)(SaReports)实体类
 *
 * <AUTHOR>
 * @since 2023-10-10 15:59:30
 */
public class SaReportsEntity implements Serializable {
    private static final long serialVersionUID = 334929799000737543L;
         // id
         private String id;
         // 表单key
         private String formid;
         // 通用分组
         private String gengroupid;
         // 模块编码
         private String modulecode;
         // 报表类型
         private String rpttype;
         // 报表名称
         private String rptname;
         // 报表数据
         private String rptdata;
         // 单页行数
         private Integer pagerow;
         // 远程打印模版Url
         private String tempurl;
         // 模版文件名
         private String filename;
         // 远程打印机SN
         private String printersn;
         // 序号
         private Integer rownum;
         // 有效标识
         private Integer enabledmark;
         // grf文本备用
         private String grfdata;
         // 长
         private Double paperlength;
         // 宽
         private Double paperwidth;
         // 备注
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 租户id
         private String tenantid;
         // 租户名称
         private String tenantname;
         // 乐观锁
         private Integer revision;

// id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 表单key

    public String getFormid() {
        return formid;
    }

    public void setFormid(String formid) {
        this.formid = formid;
    }
// 通用分组

    public String getGengroupid() {
        return gengroupid;
    }

    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }
// 模块编码

    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
// 报表类型

    public String getRpttype() {
        return rpttype;
    }

    public void setRpttype(String rpttype) {
        this.rpttype = rpttype;
    }
// 报表名称

    public String getRptname() {
        return rptname;
    }

    public void setRptname(String rptname) {
        this.rptname = rptname;
    }
// 报表数据

    public String getRptdata() {
        return rptdata;
    }

    public void setRptdata(String rptdata) {
        this.rptdata = rptdata;
    }
// 单页行数

    public Integer getPagerow() {
        return pagerow;
    }

    public void setPagerow(Integer pagerow) {
        this.pagerow = pagerow;
    }
// 远程打印模版Url

    public String getTempurl() {
        return tempurl;
    }

    public void setTempurl(String tempurl) {
        this.tempurl = tempurl;
    }
// 模版文件名

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }
// 远程打印机SN

    public String getPrintersn() {
        return printersn;
    }

    public void setPrintersn(String printersn) {
        this.printersn = printersn;
    }
// 序号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
// 有效标识

    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
// grf文本备用

    public String getGrfdata() {
        return grfdata;
    }

    public void setGrfdata(String grfdata) {
        this.grfdata = grfdata;
    }
// 长

    public Double getPaperlength() {
        return paperlength;
    }

    public void setPaperlength(Double paperlength) {
        this.paperlength = paperlength;
    }
// 宽

    public Double getPaperwidth() {
        return paperwidth;
    }

    public void setPaperwidth(Double paperwidth) {
        this.paperwidth = paperwidth;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 租户名称

    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

