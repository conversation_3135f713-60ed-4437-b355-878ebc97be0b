package inks.service.sa.table.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> nanno
 * @description : 排序题库题目
 * @create : 2025-07-29
 **/
@Data
public class SortQuestionItemRequest {
    /**
     * 题库ID
     */
    @NotNull(message = "questionBankKey请求异常")
    private String questionBankKey;

    /**
     * 前一个位置
     */
    private Long beforePosition;

    /**
     * 后一个位置
     */
    private Long afterPosition;
    
    /**
     * 题目ID
     */
    @NotBlank(message = "questionItemId请求异常")
    private String questionItemId;
}
