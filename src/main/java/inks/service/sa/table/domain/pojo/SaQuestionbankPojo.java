package inks.service.sa.table.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 题库表(SaQuestionbank)实体类
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:44
 */
@Data
public class SaQuestionbankPojo implements Serializable {
    private static final long serialVersionUID = -43897552496686506L;
     // 题库ID
     @Excel(name = "题库ID")
    private String id;
     // 题库名称
     @Excel(name = "题库名称")
    private String name;
     // 题库描述
     @Excel(name = "题库描述")
    private String description;
     // 题库类型 0普通题库1考试题库
     @Excel(name = "题库类型 0普通题库1考试题库")
    private Integer banktype;
     // 是否公开
     @Excel(name = "是否公开")
    private Integer ispublic;
     // 状态 1启用 0禁用
     @Excel(name = "状态 1启用 0禁用")
    private Integer status;
     // 是否删除 0否 1是
     @Excel(name = "是否删除 0否 1是")
    private Integer isdeleted;
     // 题目数量
     @Excel(name = "题目数量")
    private Integer itemcount;
     // 备注
     @Excel(name = "备注")
    private String remark;
     // 行号
     @Excel(name = "行号")
    private Integer rownum;
     // 创建者ID
     @Excel(name = "创建者ID")
    private String createbyid;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建时间
     @Excel(name = "创建时间")
    private Date createdate;
     // 制表人ID
     @Excel(name = "制表人ID")
    private String listerid;
     // 制表人
     @Excel(name = "制表人")
    private String lister;
     // 修改时间
     @Excel(name = "修改时间")
    private Date modifydate;
     // 自定义字段1
     @Excel(name = "自定义字段1")
    private String custom1;
     // 自定义字段2
     @Excel(name = "自定义字段2")
    private String custom2;
     // 自定义字段3
     @Excel(name = "自定义字段3")
    private String custom3;
     // 自定义字段4
     @Excel(name = "自定义字段4")
    private String custom4;
     // 自定义字段5
     @Excel(name = "自定义字段5")
    private String custom5;
     // 部门ID
     @Excel(name = "部门ID")
    private String deptid;
     // 租户ID
     @Excel(name = "租户ID")
    private String tenantid;
     // 乐观锁版本号
     @Excel(name = "乐观锁版本号")
    private Integer revision;
    // 子表
    private List<SaQuestionbankitemPojo> item;
    

}

