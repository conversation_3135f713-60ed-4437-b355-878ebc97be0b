package inks.service.sa.table.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户表单(SaForm)实体类
 *
 * <AUTHOR>
 * @since 2023-10-10 14:48:16
 */
public class SaFormEntity implements Serializable {
    private static final long serialVersionUID = 859986443460650452L;
        private String id;
         // 来源Id
         private String sourceid;
         // 来源类型1空白创建2模板
         private Integer sourcetype;
         // 表单名称
         private String name;
         // 表单描述
         private String description;
         // 用户ID
         private String userid;
         // 表单类型
         private String formtype;
         // 状态:1未发布2收集中3停止发布
         private Integer status;
         // 是否删除
         private Integer isdeleted;
         // 是文件夹
         private Integer isfolder;
         // 文件夹Id
         private Integer folderid;
         // 备注
         private String remark;
         // 创建者id
         private String createbyid;
         // 创建者
         private String createby;
         // 新建日期
         private Date createdate;
         // 制表id
         private String listerid;
         // 制表
         private String lister;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 组织id
         private String deptid;
         // 租户id
         private String tenantid;
         // 乐观锁
         private Integer revision;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 来源Id

    public String getSourceid() {
        return sourceid;
    }

    public void setSourceid(String sourceid) {
        this.sourceid = sourceid;
    }
// 来源类型1空白创建2模板

    public Integer getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
// 表单名称

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
// 表单描述

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
// 用户ID

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }
// 表单类型

    public String getFormtype() {
        return formtype;
    }

    public void setFormtype(String formtype) {
        this.formtype = formtype;
    }
// 状态:1未发布2收集中3停止发布

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
// 是否删除

    public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }
// 是文件夹

    public Integer getIsfolder() {
        return isfolder;
    }

    public void setIsfolder(Integer isfolder) {
        this.isfolder = isfolder;
    }
// 文件夹Id

    public Integer getFolderid() {
        return folderid;
    }

    public void setFolderid(Integer folderid) {
        this.folderid = folderid;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 组织id

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
// 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

