package inks.service.sa.table.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 表单收集数据结果(SaFormdata)实体类
 *
 * <AUTHOR>
 * @since 2023-09-06 10:57:52
 */
@Data
public class SaFormdataPojo implements Serializable {
    private static final long serialVersionUID = 640602415213890567L;
        @Excel(name = "")
    private String id;
         // 表单key
         @Excel(name = "表单key")
    private String formid;
         // 序号
         @Excel(name = "序号")
    private Integer seqlnum;
         // 填写结果
         @Excel(name = "填写结果")
    private String originaldata;
    // OriginalData的单选多选数字转换为实际值
    @Excel(name = "OriginalData的单选多选数字转换为实际值")
    private String realdata;
         // 提交ua
         @Excel(name = "提交ua")
    private String useragent;
         // 提交系统
         @Excel(name = "提交系统")
    private String subos;
         // 提交浏览器
         @Excel(name = "提交浏览器")
    private String subbrowser;
         // 请求ip
         @Excel(name = "请求ip")
    private String subreqip;
         // 提交地址
         @Excel(name = "提交地址")
    private String subaddr;
         // 完成时间 毫秒
         @Excel(name = "完成时间 毫秒")
    private Integer completetime;
         // 微信openId
         @Excel(name = "微信openId")
    private String wxopenid;
         // 微信用户信息
         @Excel(name = "微信用户信息")
    private String wxuserinfo;
         // 扩展字段记录来源等
         @Excel(name = "扩展字段记录来源等")
    private String extvalue;
         // 备注
         @Excel(name = "备注")
    private String remark;
         // 是否提交
         @Excel(name = "是否提交")
    private Integer submit;
     // 用户得分
    @Excel(name = "用户得分")
    private Double totalscore;
     // 试卷总分
    @Excel(name = "试卷总分")
    private Double sumtotalscore;
     // 得分详情
    @Excel(name = "得分详情")
    private String scoredetail;
         // 审核员
         @Excel(name = "审核员")
    private String assessor;
         // 审核员id
         @Excel(name = "审核员id")
    private String assessorid;
         // 审核日期
         @Excel(name = "审核日期")
    private Date assessdate;
         // 审核状态
         @Excel(name = "审核状态")
    private String assessstatus;
         // 创建者id
         @Excel(name = "创建者id")
    private String createbyid;
         // 创建者
         @Excel(name = "创建者")
    private String createby;
         // 新建日期
         @Excel(name = "新建日期")
    private Date createdate;
         // 制表id
         @Excel(name = "制表id")
    private String listerid;
         // 制表
         @Excel(name = "制表")
    private String lister;
         // 修改日期
         @Excel(name = "修改日期")
    private Date modifydate;
         // 自定义1
         @Excel(name = "自定义1")
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2")
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3")
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4")
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5")
    private String custom5;
    // 组织id
    @Excel(name = "组织id")
    private String deptid;
         // 租户id
         @Excel(name = "租户id")
    private String tenantid;
         // 乐观锁
         @Excel(name = "乐观锁")
    private Integer revision;
         // 关联的Form主表name
    @Excel(name = "关联的Form主表name")
    private String formname;
    // 关联的Form主表描述
    @Excel(name = "关联的Form主表描述")
    private String formdesc;
    // 是否到达账号下填写次数限制
    @Excel(name = "是否到达账号下填写次数限制,默认false")
    private Boolean isAccountWriteCountLimit = false;
    // 账号下填写次数限制后的提示语
    @Excel(name = "账号下填写次数限制后的提示语")
    private String accountWriteCountLimitText;


    //FormItemId (暂时只用来查询FormItem表的Label字段like 'DPCI#'的FormItemId)
    private String formitemid;

}
