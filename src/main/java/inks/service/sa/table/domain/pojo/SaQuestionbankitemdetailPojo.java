package inks.service.sa.table.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * 题库题目表(SaQuestionbankitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:51
 */
@Data
public class SaQuestionbankitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 331932641806503842L;
     // 题目ID
  @Excel(name = "题目ID")    
  private String id;
     // 所属题库ID
  @Excel(name = "所属题库ID")    
  private String pid;
     // 题目类型: RADIO,CHECKBOX,INPUT,TEXTAREA等
  @Excel(name = "题目类型: RADIO,CHECKBOX,INPUT,TEXTAREA等")    
  private String itemtype;
     // 题目标题
  @Excel(name = "题目标题")    
  private String label;
     // 题目完整配置
  @Excel(name = "题目完整配置")    
  private String scheme;
     // 排序序号
  @Excel(name = "排序序号")    
  private Integer sortorder;
     // 状态 1启用 0禁用
  @Excel(name = "状态 1启用 0禁用")    
  private Integer status;
     // 是否删除 0否 1是
  @Excel(name = "是否删除 0否 1是")    
  private Integer isdeleted;
     // 使用次数统计
  @Excel(name = "使用次数统计")    
  private Integer usagecount;
     // 正确率(%)
  @Excel(name = "正确率(%)")    
  private Double correctrate;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 行号
  @Excel(name = "行号")
  private Integer rownum;
     // 创建者ID
  @Excel(name = "创建者ID")    
  private String createbyid;
     // 创建者
  @Excel(name = "创建者")    
  private String createby;
     // 创建时间
  @Excel(name = "创建时间")    
  private Date createdate;
     // 制表人ID
  @Excel(name = "制表人ID")    
  private String listerid;
     // 制表人
  @Excel(name = "制表人")    
  private String lister;
     // 修改时间
  @Excel(name = "修改时间")    
  private Date modifydate;
     // 自定义字段1
  @Excel(name = "自定义字段1")    
  private String custom1;
     // 自定义字段2
  @Excel(name = "自定义字段2")    
  private String custom2;
     // 自定义字段3
  @Excel(name = "自定义字段3")    
  private String custom3;
     // 自定义字段4
  @Excel(name = "自定义字段4")    
  private String custom4;
     // 自定义字段5
  @Excel(name = "自定义字段5")    
  private String custom5;
     // 部门ID
  @Excel(name = "部门ID")    
  private String deptid;
     // 租户ID
  @Excel(name = "租户ID")    
  private String tenantid;
     // 乐观锁版本号
  @Excel(name = "乐观锁版本号")    
  private Integer revision;



}

