package inks.service.sa.table.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
// 添加表单协作的userList和deptList后返回前端的用户,部门消息视图vo
public class FormAuthVO {
    private String id; // 实体的唯一标识
    private String authid; // 授权对象ID
    private Date createTime; // 创建时间
    private Date updateTime; // 更新时间（可能为null）
    private String formKey; // 表单键
    private Integer type; // 类型
    private String typeDesc; // 类型描述
    private String shareId; // 共享ID
    private String collaborator; // 协作者 记录的是username或deptname
    // realname 只有用户才要的真实名字
    private String realname;
}
