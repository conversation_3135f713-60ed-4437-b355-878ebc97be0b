package inks.service.sa.table.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaUserformthemePojo;

/**
 * 项目表单项(SaUserformtheme)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
public interface SaUserformthemeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaUserformthemePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaUserformthemePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saUserformthemePojo 实例对象
     * @return 实例对象
     */
    SaUserformthemePojo insert(SaUserformthemePojo saUserformthemePojo);

    /**
     * 修改数据
     *
     * @param saUserformthemepojo 实例对象
     * @return 实例对象
     */
    SaUserformthemePojo update(SaUserformthemePojo saUserformthemepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                                                                       }
