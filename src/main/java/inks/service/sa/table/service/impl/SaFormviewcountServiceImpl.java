package inks.service.sa.table.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.table.domain.SaFormviewcountEntity;
import inks.service.sa.table.domain.pojo.SaFormviewcountPojo;
import inks.service.sa.table.mapper.SaFormviewcountMapper;
import inks.service.sa.table.service.SaFormviewcountService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 用户表单查看次数(SaFormviewcount)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
@Service("saFormviewcountService")
public class SaFormviewcountServiceImpl implements SaFormviewcountService {
    @Resource
    private SaFormviewcountMapper saFormviewcountMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormviewcountPojo getEntity(String key) {
        return this.saFormviewcountMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormviewcountPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormviewcountPojo> lst = saFormviewcountMapper.getPageList(queryParam);
            PageInfo<SaFormviewcountPojo> pageInfo = new PageInfo<SaFormviewcountPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saFormviewcountPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormviewcountPojo insert(SaFormviewcountPojo saFormviewcountPojo) {
    //初始化NULL字段
     if(saFormviewcountPojo.getFormid()==null) saFormviewcountPojo.setFormid("");
     if(saFormviewcountPojo.getCount()==null) saFormviewcountPojo.setCount(0);
     if(saFormviewcountPojo.getRemark()==null) saFormviewcountPojo.setRemark("");
     if(saFormviewcountPojo.getCreatebyid()==null) saFormviewcountPojo.setCreatebyid("");
     if(saFormviewcountPojo.getCreateby()==null) saFormviewcountPojo.setCreateby("");
     if(saFormviewcountPojo.getCreatedate()==null) saFormviewcountPojo.setCreatedate(new Date());
     if(saFormviewcountPojo.getListerid()==null) saFormviewcountPojo.setListerid("");
     if(saFormviewcountPojo.getLister()==null) saFormviewcountPojo.setLister("");
     if(saFormviewcountPojo.getModifydate()==null) saFormviewcountPojo.setModifydate(new Date());
     if(saFormviewcountPojo.getCustom1()==null) saFormviewcountPojo.setCustom1("");
     if(saFormviewcountPojo.getCustom2()==null) saFormviewcountPojo.setCustom2("");
     if(saFormviewcountPojo.getCustom3()==null) saFormviewcountPojo.setCustom3("");
     if(saFormviewcountPojo.getCustom4()==null) saFormviewcountPojo.setCustom4("");
     if(saFormviewcountPojo.getCustom5()==null) saFormviewcountPojo.setCustom5("");
     if(saFormviewcountPojo.getTenantid()==null) saFormviewcountPojo.setTenantid("");
     if(saFormviewcountPojo.getRevision()==null) saFormviewcountPojo.setRevision(0);
        SaFormviewcountEntity saFormviewcountEntity = new SaFormviewcountEntity(); 
        BeanUtils.copyProperties(saFormviewcountPojo,saFormviewcountEntity);
        //生成雪花id
          saFormviewcountEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFormviewcountEntity.setRevision(1);  //乐观锁
          this.saFormviewcountMapper.insert(saFormviewcountEntity);
        return this.getEntity(saFormviewcountEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFormviewcountPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormviewcountPojo update(SaFormviewcountPojo saFormviewcountPojo) {
        SaFormviewcountEntity saFormviewcountEntity = new SaFormviewcountEntity(); 
        BeanUtils.copyProperties(saFormviewcountPojo,saFormviewcountEntity);
        this.saFormviewcountMapper.update(saFormviewcountEntity);
        return this.getEntity(saFormviewcountEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormviewcountMapper.delete(key) ;
    }
    
                                                                                         
}
