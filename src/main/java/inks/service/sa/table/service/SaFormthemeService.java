package inks.service.sa.table.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaFormthemePojo;

/**
 * 项目主题外观模板(SaFormtheme)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:43
 */
public interface SaFormthemeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormthemePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormthemePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFormthemePojo 实例对象
     * @return 实例对象
     */
    SaFormthemePojo insert(SaFormthemePojo saFormthemePojo);

    /**
     * 修改数据
     *
     * @param saFormthemepojo 实例对象
     * @return 实例对象
     */
    SaFormthemePojo update(SaFormthemePojo saFormthemepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                                    }
