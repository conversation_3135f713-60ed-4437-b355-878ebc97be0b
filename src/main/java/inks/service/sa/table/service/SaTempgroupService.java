package inks.service.sa.table.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaTempgroupPojo;

/**
 * 项目模板分类(SaTempgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:45
 */
public interface SaTempgroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTempgroupPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTempgroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saTempgroupPojo 实例对象
     * @return 实例对象
     */
    SaTempgroupPojo insert(SaTempgroupPojo saTempgroupPojo);

    /**
     * 修改数据
     *
     * @param saTempgrouppojo 实例对象
     * @return 实例对象
     */
    SaTempgroupPojo update(SaTempgroupPojo saTempgrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                     }
