package inks.service.sa.table.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.table.domain.SaQuestionbankitemEntity;
import inks.service.sa.table.mapper.SaQuestionbankitemMapper;
import inks.service.sa.table.service.SaQuestionbankitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 题库题目表(SaQuestionbankitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:52
 */
@Service("saQuestionbankitemService")
public class SaQuestionbankitemServiceImpl implements SaQuestionbankitemService {
    @Resource
    private SaQuestionbankitemMapper saQuestionbankitemMapper;

    @Override
    public SaQuestionbankitemPojo getEntity(String key) {
        return this.saQuestionbankitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaQuestionbankitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuestionbankitemPojo> lst = saQuestionbankitemMapper.getPageList(queryParam);
            PageInfo<SaQuestionbankitemPojo> pageInfo = new PageInfo<SaQuestionbankitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaQuestionbankitemPojo> getList(String Pid) { 
        try {
            List<SaQuestionbankitemPojo> lst = saQuestionbankitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaQuestionbankitemPojo insert(SaQuestionbankitemPojo saQuestionbankitemPojo) {
        //初始化item的NULL
        SaQuestionbankitemPojo itempojo =this.clearNull(saQuestionbankitemPojo);
        SaQuestionbankitemEntity saQuestionbankitemEntity = new SaQuestionbankitemEntity(); 
        BeanUtils.copyProperties(itempojo,saQuestionbankitemEntity);
         //生成雪花id
          saQuestionbankitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saQuestionbankitemEntity.setRevision(1);  //乐观锁      
          this.saQuestionbankitemMapper.insert(saQuestionbankitemEntity);
        return this.getEntity(saQuestionbankitemEntity.getId());
  
    }

    @Override
    public SaQuestionbankitemPojo update(SaQuestionbankitemPojo saQuestionbankitemPojo) {
        SaQuestionbankitemEntity saQuestionbankitemEntity = new SaQuestionbankitemEntity(); 
        BeanUtils.copyProperties(saQuestionbankitemPojo,saQuestionbankitemEntity);
        this.saQuestionbankitemMapper.update(saQuestionbankitemEntity);
        return this.getEntity(saQuestionbankitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saQuestionbankitemMapper.delete(key) ;
    }

     @Override
     public SaQuestionbankitemPojo clearNull(SaQuestionbankitemPojo saQuestionbankitemPojo){
     //初始化NULL字段
     if(saQuestionbankitemPojo.getPid()==null) saQuestionbankitemPojo.setPid("");
     if(saQuestionbankitemPojo.getItemtype()==null) saQuestionbankitemPojo.setItemtype("");
     if(saQuestionbankitemPojo.getLabel()==null) saQuestionbankitemPojo.setLabel("");
     if(saQuestionbankitemPojo.getScheme()==null) saQuestionbankitemPojo.setScheme("");
     if(saQuestionbankitemPojo.getSortorder()==null) saQuestionbankitemPojo.setSortorder(0);
     if(saQuestionbankitemPojo.getStatus()==null) saQuestionbankitemPojo.setStatus(0);
     if(saQuestionbankitemPojo.getIsdeleted()==null) saQuestionbankitemPojo.setIsdeleted(0);
     if(saQuestionbankitemPojo.getUsagecount()==null) saQuestionbankitemPojo.setUsagecount(0);
     if(saQuestionbankitemPojo.getCorrectrate()==null) saQuestionbankitemPojo.setCorrectrate(0D);
     if(saQuestionbankitemPojo.getRemark()==null) saQuestionbankitemPojo.setRemark("");
     if(saQuestionbankitemPojo.getRownum()==null) saQuestionbankitemPojo.setRownum(0);
     if(saQuestionbankitemPojo.getCreatebyid()==null) saQuestionbankitemPojo.setCreatebyid("");
     if(saQuestionbankitemPojo.getCreateby()==null) saQuestionbankitemPojo.setCreateby("");
     if(saQuestionbankitemPojo.getCreatedate()==null) saQuestionbankitemPojo.setCreatedate(new Date());
     if(saQuestionbankitemPojo.getListerid()==null) saQuestionbankitemPojo.setListerid("");
     if(saQuestionbankitemPojo.getLister()==null) saQuestionbankitemPojo.setLister("");
     if(saQuestionbankitemPojo.getModifydate()==null) saQuestionbankitemPojo.setModifydate(new Date());
     if(saQuestionbankitemPojo.getCustom1()==null) saQuestionbankitemPojo.setCustom1("");
     if(saQuestionbankitemPojo.getCustom2()==null) saQuestionbankitemPojo.setCustom2("");
     if(saQuestionbankitemPojo.getCustom3()==null) saQuestionbankitemPojo.setCustom3("");
     if(saQuestionbankitemPojo.getCustom4()==null) saQuestionbankitemPojo.setCustom4("");
     if(saQuestionbankitemPojo.getCustom5()==null) saQuestionbankitemPojo.setCustom5("");
     if(saQuestionbankitemPojo.getDeptid()==null) saQuestionbankitemPojo.setDeptid("");
     if(saQuestionbankitemPojo.getTenantid()==null) saQuestionbankitemPojo.setTenantid("");
     if(saQuestionbankitemPojo.getRevision()==null) saQuestionbankitemPojo.setRevision(0);
     return saQuestionbankitemPojo;
     }
}
