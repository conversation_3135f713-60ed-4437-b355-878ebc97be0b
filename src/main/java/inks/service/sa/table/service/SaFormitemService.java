package inks.service.sa.table.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaFormitemPojo;

import java.util.List;
/**
 * 表单项(SaFormitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 14:20:02
 */
public interface SaFormitemService{

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaFormitemPojo> getList(String Pid);  
    
    /**
     * 新增数据
     *
     * @param saFormitemPojo 实例对象
     * @return 实例对象
     */
    SaFormitemPojo insert(SaFormitemPojo saFormitemPojo);

    /**
     * 修改数据
     *
     * @param saFormitempojo 实例对象
     * @return 实例对象
     */
    SaFormitemPojo update(SaFormitemPojo saFormitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

     /**
     * 修改数据
     *
     * @param saFormitempojo 实例对象
     * @return 实例对象
     */
    SaFormitemPojo clearNull(SaFormitemPojo saFormitempojo);

    Long getLastItemSort(String formKey);

    void updateBatchById(List<SaFormitemPojo> itemEntityList);
}
