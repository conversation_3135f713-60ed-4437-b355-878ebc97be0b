package inks.service.sa.table.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaFormlogicPojo;

/**
 * 项目逻辑(SaFormlogic)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17 15:02:54
 */
public interface SaFormlogicService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormlogicPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormlogicPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFormlogicPojo 实例对象
     * @return 实例对象
     */
    SaFormlogicPojo insert(SaFormlogicPojo saFormlogicPojo);

    /**
     * 修改数据
     *
     * @param saFormlogicpojo 实例对象
     * @return 实例对象
     */
    SaFormlogicPojo update(SaFormlogicPojo saFormlogicpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                     }
