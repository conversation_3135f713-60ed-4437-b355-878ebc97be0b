package inks.service.sa.table.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaFormthemegroupPojo;

/**
 * 表单主题分类(SaFormthemegroup)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
public interface SaFormthemegroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormthemegroupPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormthemegroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFormthemegroupPojo 实例对象
     * @return 实例对象
     */
    SaFormthemegroupPojo insert(SaFormthemegroupPojo saFormthemegroupPojo);

    /**
     * 修改数据
     *
     * @param saFormthemegrouppojo 实例对象
     * @return 实例对象
     */
    SaFormthemegroupPojo update(SaFormthemegroupPojo saFormthemegrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                     }
