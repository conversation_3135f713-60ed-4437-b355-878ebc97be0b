package inks.service.sa.table.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.mapper.SaUserMapper;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.table.domain.SaFormdataEntity;
import inks.service.sa.table.domain.pojo.SaFormPojo;
import inks.service.sa.table.domain.pojo.SaFormdataPojo;
import inks.service.sa.table.domain.pojo.SaFormitemPojo;
import inks.service.sa.table.mapper.SaFormMapper;
import inks.service.sa.table.mapper.SaFormdataMapper;
import inks.service.sa.table.mapper.SaFormitemMapper;
import inks.service.sa.table.service.SaFormdataService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 表单收集数据结果(SaFormdata)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-06 10:57:53
 */
@Service("saFormdataService")
public class SaFormdataServiceImpl implements SaFormdataService {
    @Resource
    private SaFormdataMapper saFormdataMapper;
    @Resource
    private SaUserMapper saUserMapper;
    @Resource
    private SaFormitemMapper saFormitemMapper;
    @Resource
    private SaFormMapper saFormMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormdataPojo getEntity(String key) {
        return this.saFormdataMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormdataPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormdataPojo> lst = saFormdataMapper.getPageList(queryParam);
            PageInfo<SaFormdataPojo> pageInfo = new PageInfo<SaFormdataPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saFormdataPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormdataPojo insert(SaFormdataPojo saFormdataPojo) {
        //初始化NULL字段
        if (saFormdataPojo.getFormid() == null) saFormdataPojo.setFormid("");
        if (saFormdataPojo.getSeqlnum() == null) saFormdataPojo.setSeqlnum(0);
        if (saFormdataPojo.getOriginaldata() == null) saFormdataPojo.setOriginaldata("{}");
        if (saFormdataPojo.getRealdata() == null) saFormdataPojo.setRealdata("{}");
        if (saFormdataPojo.getUseragent() == null) saFormdataPojo.setUseragent("{}");
        if (saFormdataPojo.getSubos() == null) saFormdataPojo.setSubos("");
        if (saFormdataPojo.getSubbrowser() == null) saFormdataPojo.setSubbrowser("");
        if (saFormdataPojo.getSubreqip() == null) saFormdataPojo.setSubreqip("");
        if (saFormdataPojo.getSubaddr() == null) saFormdataPojo.setSubaddr("");
        if (saFormdataPojo.getCompletetime() == null) saFormdataPojo.setCompletetime(0);
        if (saFormdataPojo.getWxopenid() == null) saFormdataPojo.setWxopenid("");
        if (saFormdataPojo.getWxuserinfo() == null) saFormdataPojo.setWxuserinfo("{}");
        if (saFormdataPojo.getExtvalue() == null) saFormdataPojo.setExtvalue("");
        if (saFormdataPojo.getRemark() == null) saFormdataPojo.setRemark("");
        if (saFormdataPojo.getSubmit() == null) saFormdataPojo.setSubmit(0);
        if (saFormdataPojo.getTotalscore() == null) saFormdataPojo.setTotalscore(0D);
        if (saFormdataPojo.getSumtotalscore() == null) saFormdataPojo.setSumtotalscore(0D);
        if (saFormdataPojo.getScoredetail() == null) saFormdataPojo.setScoredetail("");
        if (saFormdataPojo.getAssessor() == null) saFormdataPojo.setAssessor("");
        if (saFormdataPojo.getAssessorid() == null) saFormdataPojo.setAssessorid("");
        if (saFormdataPojo.getAssessdate() == null) saFormdataPojo.setAssessdate(new Date());
        if (saFormdataPojo.getAssessstatus() == null) saFormdataPojo.setAssessstatus("");
        if (saFormdataPojo.getCreatebyid() == null) saFormdataPojo.setCreatebyid("");
        if (saFormdataPojo.getCreateby() == null) saFormdataPojo.setCreateby("");
        if (saFormdataPojo.getCreatedate() == null) saFormdataPojo.setCreatedate(new Date());
        if (saFormdataPojo.getListerid() == null) saFormdataPojo.setListerid("");
        if (saFormdataPojo.getLister() == null) saFormdataPojo.setLister("");
        if (saFormdataPojo.getModifydate() == null) saFormdataPojo.setModifydate(new Date());
        if (saFormdataPojo.getCustom1() == null) saFormdataPojo.setCustom1("");
        if (saFormdataPojo.getCustom2() == null) saFormdataPojo.setCustom2("");
        if (saFormdataPojo.getCustom3() == null) saFormdataPojo.setCustom3("");
        if (saFormdataPojo.getCustom4() == null) saFormdataPojo.setCustom4("");
        if (saFormdataPojo.getCustom5() == null) saFormdataPojo.setCustom5("");
        if (saFormdataPojo.getDeptid() == null) saFormdataPojo.setDeptid("");
        if (saFormdataPojo.getTenantid() == null) saFormdataPojo.setTenantid("");
        if (saFormdataPojo.getRevision() == null) saFormdataPojo.setRevision(0);
        SaFormdataEntity saFormdataEntity = new SaFormdataEntity();
        BeanUtils.copyProperties(saFormdataPojo, saFormdataEntity);
        //生成雪花id
        saFormdataEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saFormdataEntity.setRevision(1);  //乐观锁

        //将OriginalData的单选多选数字转换为实际值,存储到RealData字段中,供查询时使用
        // [OriginalData字段作为主表!!!] 字段内容为:{"date1695189380503": "2023-09-20", "input1695189373224": "娜诺", "textarea1695189378139": "西安是一座古朴的城市..."}
        String originaldata = saFormdataPojo.getOriginaldata();
        Map<String, Object> originaldataMap = JSONArray.parseObject(originaldata, Map.class);
        // 给多选框,性别,下拉框,图片url赋值
        ControlToPopulateData(originaldataMap, saFormdataPojo.getFormid());
        // 将Map转换为JSON字符串
        String realdata = JSON.toJSONString(originaldataMap);
        saFormdataEntity.setRealdata(realdata);
        // 考试算分逻辑：当submit=1时，如果是考试类型表单，则进行算分
        if (Objects.equals(saFormdataPojo.getSubmit(), 1)) {
            try {
                // 获取表单信息，检查是否为考试类型
                SaFormPojo formPojo = saFormMapper.getEntity(saFormdataPojo.getFormid());
                if (formPojo != null && "4".equals(formPojo.getFormtype())) {
                    // 进行考试算分
                    Map<String, Object> scoreResult = calculateExamScoreWithDetails(saFormdataPojo.getFormid(), originaldata);
                    saFormdataEntity.setTotalscore((Double) scoreResult.get("totalscore"));
                    saFormdataEntity.setSumtotalscore((Double) scoreResult.get("sumtotalscore"));
                    // 存储详细得分信息到ScoreDetail字段
                    String scoredetailJson = (String) scoreResult.get("scoredetailJson");
                    saFormdataEntity.setScoredetail(scoredetailJson);
                }
            } catch (Exception e) {
                // 算分失败时记录日志，但不影响正常更新流程
                System.err.println("考试算分失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
        this.saFormdataMapper.insert(saFormdataEntity);
        return this.getEntity(saFormdataEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saFormdataPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormdataPojo update(SaFormdataPojo saFormdataPojo) {
        SaFormdataEntity saFormdataEntity = new SaFormdataEntity();
        BeanUtils.copyProperties(saFormdataPojo, saFormdataEntity);
        //将OriginalData的单选多选数字转换为实际值,存储到RealData字段中,供查询时使用
        // [OriginalData字段作为主表!!!] 字段内容为:{"date1695189380503": "2023-09-20", "input1695189373224": "娜诺", "textarea1695189378139": "西安是一座古朴的城市..."}
        String originaldata = saFormdataPojo.getOriginaldata();
        Map<String, Object> originaldataMap = JSONArray.parseObject(originaldata, Map.class);
        // 给多选框,性别,下拉框,图片url赋值
        ControlToPopulateData(originaldataMap, saFormdataPojo.getFormid());
        // 将Map转换为JSON字符串
        String realdata = JSON.toJSONString(originaldataMap);
        saFormdataEntity.setRealdata(realdata);

        // 考试算分逻辑：当submit=1时，如果是考试类型表单，则进行算分
        if (Objects.equals(saFormdataPojo.getSubmit(), 1)) {
            try {
                // 获取表单信息，检查是否为考试类型
                SaFormPojo formPojo = saFormMapper.getEntity(saFormdataPojo.getFormid());
                if (formPojo != null && "4".equals(formPojo.getFormtype())) {
                    // 进行考试算分
                    Map<String, Object> scoreResult = calculateExamScoreWithDetails(saFormdataPojo.getFormid(), originaldata);
                    saFormdataEntity.setTotalscore((Double) scoreResult.get("totalscore"));
                    saFormdataEntity.setSumtotalscore((Double) scoreResult.get("sumtotalscore"));
                    // 存储详细得分信息到ScoreDetail字段
                    String scoredetailJson = (String) scoreResult.get("scoredetailJson");
                    saFormdataEntity.setScoredetail(scoredetailJson);
                }
            } catch (Exception e) {
                // 算分失败时记录日志，但不影响正常更新流程
                System.err.println("考试算分失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        this.saFormdataMapper.update(saFormdataEntity);
        return this.getEntity(saFormdataEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormdataMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saFormdataPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaFormdataPojo approval(SaFormdataPojo saFormdataPojo) {
        //主表更改
        SaFormdataEntity saFormdataEntity = new SaFormdataEntity();
        BeanUtils.copyProperties(saFormdataPojo, saFormdataEntity);
        this.saFormdataMapper.approval(saFormdataEntity);
        //返回Bill实例
        return this.getEntity(saFormdataEntity.getId());
    }

    @Override
    public List<Map<String, Object>> formSubmitNumberByDay(QueryParam queryParam) {
        //构建传入的日期范围内的数据，如传入 "StartDate": "2023-03-01 00:00:00","EndDate": "2023-03-31 23:59:59"
        //此处为近7天的时间范围
        Date endDate = queryParam.getDateRange().getEndDate();
        Date startDate = queryParam.getDateRange().getStartDate();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        // 使用 SimpleDateFormat 格式化日期，以便进行日期比较
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 构建传入的日期范围内的数据list30Day
        List<Map<String, Object>> list7Day = new ArrayList<>();
        while (!calendar.getTime().after(endDate)) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("date", dateFormat.format(calendar.getTime()));//每天日期
            map.put("submitcount", 0);// 每天提交表单数
            list7Day.add(map);
            calendar.add(Calendar.DATE, 1);
        }
        //  DB查询出传入的日期范围内的每天填写数
        List<Map<String, Object>> formFilledInNumberMapList = this.saFormdataMapper.formFilledInNumber(queryParam);
        // 遍历list30Day,将每天的来料重量,出货重量赋值给list30Day
        for (Map<String, Object> map : list7Day) {
            String date7 = (String) map.get("date");
            for (Map<String, Object> formFilledInNumberMap : formFilledInNumberMapList) {
                Date mapsMachQtyDate = (Date) formFilledInNumberMap.get("date");
                String dateForm = dateFormat.format(mapsMachQtyDate);// 将字符串转换为日期
                if (date7.equals(dateForm)) {
                    map.put("submitcount", formFilledInNumberMap.get("submitcount"));
                }
            }
        }
        return list7Day;
    }

    @Override
    public List<Map<String, Object>> formSubmitNumberByForm() {
        return this.saFormdataMapper.formSubmitNumberByForm();
    }

    /**
     * @Description 给多选框, 性别, 下拉框, 图片url赋值
     * "radio1695610627228": 1
     * "select1695610529526": 2
     * "checkbox1695610587356": [1, 3, 4]
     * <AUTHOR>
     * @time 2023/9/25 16:09
     */
    public void ControlToPopulateData(Map<String, Object> originaldataMap, String formId) {

        // 注意:图片赋值时: 循环中修改了originaldataMap，这可能导致ConcurrentModificationException异常。这是因为在遍历originaldataMap的同时，你在往它里面添加新的键值对。这会干扰迭代器的内部状态，导致异常。
        // 要解决这个问题，你可以考虑使用一个临时的Map来存储需要添加到originaldataMap的新键值对，然后在循环结束后一次性将它们添加到originaldataMap中。这样就不会在遍历的同时修改它了。
        Map<String, String> imageAddMap = new HashMap<>();
        for (String key : originaldataMap.keySet()) {
            // 给性别赋值 或者给下拉框赋值(两者逻辑相同,都是单个数字)
            if (key.contains("radio") || key.contains("select")) {
                String scheme = saFormitemMapper.getSchemeByFormItemId(key, formId);
                Map<Integer, String> labelAndValue = ParseScheme(scheme);//value 1 对应 label 男
                labelAndValue.forEach((value, label) -> {
                    if (originaldataMap.get(key).equals(value)) {
                        originaldataMap.put(key, label);
                    }
                });
            }
            // 给多选框赋值 (值是数字数组 如[1,3,4])
            else if (key.contains("checkbox")) {
                String scheme = saFormitemMapper.getSchemeByFormItemId(key, formId);
                Map<Integer, String> labelAndValue = ParseScheme(scheme);//value 1 对应 label 男
                // 获取原始数据中的值，多选框的值是一个数字数组 [1,3,4]
                Object value = originaldataMap.get(key);
                // 确保值是一个数组
                if (value instanceof List<?>) {
                    List<Integer> selectedValues = (List<Integer>) value;
                    // 创建一个用于存储替换后的值的列表
                    List<String> replacedValues = new ArrayList<>();
                    // 遍历选中的值并替换成对应的label
                    for (Integer selectedValue : selectedValues) {
                        String label = labelAndValue.get(selectedValue);
                        if (label != null) {
                            replacedValues.add(label);
                        }
                    }
                    // 更新原始数据Map中的值为替换后的列表 replacedValues的值是[电影, 篮球] 需要去掉中括号:  电影, 篮球
                    originaldataMap.put(key, String.join(", ", replacedValues));
                }
            }
//            // 给图片url赋值
//            else if (key.contains("image")) {
//                Object imageDataObject = originaldataMap.get(key);
//                // 确保值是一个数组
//                if (imageDataObject instanceof List<?>) {
//                    List<Map<String, String>> imageList = (List<Map<String, String>>) imageDataObject;
//                    // 遍历数组中所有对象的 "图片url"
//                    if (!imageList.isEmpty()) {
//                        for (Map<String, String> imageMap : imageList) {
//                            String url = imageMap.get("url") == null ? "" : imageMap.get("url");
//                            // 覆盖原始 Map 中的值 key(是原key加上后缀_0,_1,_2...)    indexOf是从0开始的
//                            imageAddMap.put(key + "_" + imageList.indexOf(imageMap), url);
//                        }
//                    }
//                }
//            }
        }
        // 一次性将新键值对imageAddMap添加到originaldataMap
        originaldataMap.putAll(imageAddMap);
    }

    private Map<Integer, String> ParseScheme(String scheme) {
        Map<Integer, String> parseLabel = new HashMap<>();

        try {
            // 使用FastJSON解析JSON字符串
            JSONObject schemeJson = JSON.parseObject(scheme);
            // 获取options字段
            JSONArray options = schemeJson.getJSONObject("config").getJSONArray("options");
            // 遍历options数组并提取label和value对应的信息
            for (int i = 0; i < options.size(); i++) {
                JSONObject option = options.getJSONObject(i);
                int value = option.getInteger("value");
                String label = option.getString("label");
                parseLabel.put(value, label);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return parseLabel;
    }

    /**
     * 考试算分方法
     *
     * @param formId       表单ID
     * @param originaldata 用户填写的原始数据JSON
     * @return Map包含totalscore(用户得分)和sumtotalscore(试卷总分)
     */
    private Map<String, Double> calculateExamScore(String formId, String originaldata) {
        Map<String, Double> result = new HashMap<>();
        double totalScore = 0.0; // 用户得分
        double sumTotalScore = 0.0; // 试卷总分

        try {
            // 解析用户填写的数据
            Map<String, Object> userAnswers = JSONArray.parseObject(originaldata, Map.class);

            // 获取表单的所有表单项
            List<SaFormitemPojo> formItems = saFormitemMapper.getList(formId);

            for (SaFormitemPojo formItem : formItems) {
                String scheme = formItem.getScheme();
                if (scheme == null || scheme.trim().isEmpty()) {
                    continue;
                }

                try {
                    // 解析scheme JSON，提取examConfig
                    Map<String, Object> schemeMap = JSONArray.parseObject(scheme, Map.class);
                    Map<String, Object> examConfig = (Map<String, Object>) schemeMap.get("examConfig");

                    if (examConfig == null) {
                        continue; // 没有考试配置，跳过
                    }

                    // 检查是否启用评分
                    Boolean enableScore = (Boolean) examConfig.get("enableScore");
                    if (enableScore == null || !enableScore) {
                        continue; // 未启用评分，跳过
                    }

                    // 获取题目分值
                    Object scoreObj = examConfig.get("score");
                    double questionScore = 0.0;
                    if (scoreObj instanceof Integer) {
                        questionScore = ((Integer) scoreObj).doubleValue();
                    } else if (scoreObj instanceof Double) {
                        questionScore = (Double) scoreObj;
                    }

                    // 累计试卷总分
                    sumTotalScore += questionScore;

                    // 获取评分类型
                    Integer scoringType = (Integer) examConfig.get("scoringType");
                    if (scoringType == null || scoringType == 0) {
                        continue; // 人工评分，跳过自动算分
                    }

                    // 获取用户答案
                    String formItemId = formItem.getFormitemid();
                    Object userAnswer = userAnswers.get(formItemId);
                    if (userAnswer == null) {
                        continue; // 用户未作答，跳过
                    }

                    // 获取标准答案
                    Object standardAnswer = examConfig.get("answer");

                    // 根据评分类型进行算分
                    double earnedScore = calculateQuestionScore(scoringType, userAnswer, standardAnswer, questionScore, examConfig, formItem.getType());
                    totalScore += earnedScore;

                } catch (Exception e) {
                    System.err.println("解析表单项算分配置失败: " + formItem.getFormitemid() + ", " + e.getMessage());
                    // 继续处理下一个表单项
                }
            }

        } catch (Exception e) {
            System.err.println("考试算分过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        result.put("totalscore", totalScore);
        result.put("sumtotalscore", sumTotalScore);
        PrintColor.red("用户得分: " + totalScore + ", 试卷总分: " + sumTotalScore);
        return result;
    }

    /**
     * 根据评分类型计算单题得分
     *
     * @param scoringType    评分类型
     * @param userAnswer     用户答案
     * @param standardAnswer 标准答案
     * @param questionScore  题目分值
     * @param examConfig     考试配置
     * @param itemType       表单项类型(RADIO/CHECKBOX/INPUT等)
     * @return 用户在该题的得分
     */
    private double calculateQuestionScore(Integer scoringType, Object userAnswer, Object standardAnswer,
                                          double questionScore, Map<String, Object> examConfig, String itemType) {
        try {
            switch (scoringType) {
                case 1: // 单选/多选完全匹配
                    return calculateExactMatchScore(userAnswer, standardAnswer, questionScore, itemType);

                case 2: // 文本包含答案即可得分
                    return calculateContainsScore(userAnswer, standardAnswer, questionScore);

                case 3: // 文本完全相同才得分
                    return calculateExactTextScore(userAnswer, standardAnswer, questionScore);

                case 4: // 多选每个选项自定义分值
                    return calculateCustomOptionScore(userAnswer, examConfig);

                default:
                    return 0.0; // 未知评分类型或人工评分
            }
        } catch (Exception e) {
            System.err.println("计算单题得分失败: " + e.getMessage());
            return 0.0;
        }
    }

    /**
     * scoringType=1: 单选/多选完全匹配
     */
    private double calculateExactMatchScore(Object userAnswer, Object standardAnswer, double questionScore, String itemType) {
        // 根据表单项类型判断是单选还是多选
        if ("RADIO".equals(itemType) || "SELECT".equals(itemType)) {
            // 单选题：标准答案可能是数组格式[3]，需要提取第一个元素
            Object targetAnswer = standardAnswer;
            if (standardAnswer instanceof List) {
                List<?> standardList = (List<?>) standardAnswer;
                if (!standardList.isEmpty()) {
                    targetAnswer = standardList.get(0);
                }
            }
            return Objects.equals(userAnswer, targetAnswer) ? questionScore : 0.0;
        } else if ("CHECKBOX".equals(itemType)) {
            // 多选题：比较数组
            if (standardAnswer instanceof List && userAnswer instanceof List) {
                List<?> standardList = (List<?>) standardAnswer;
                List<?> userList = (List<?>) userAnswer;
                // 转换为Set进行比较，忽略顺序
                Set<Object> standardSet = new HashSet<>(standardList);
                Set<Object> userSet = new HashSet<>(userList);
                return standardSet.equals(userSet) ? questionScore : 0.0;
            }
        } else {
            // 其他类型，直接比较
            return Objects.equals(userAnswer, standardAnswer) ? questionScore : 0.0;
        }
        return 0.0;
    }

    /**
     * scoringType=2: 文本包含答案即可得分
     */
    private double calculateContainsScore(Object userAnswer, Object standardAnswer, double questionScore) {
        if (userAnswer == null || standardAnswer == null) {
            return 0.0;
        }
        String userText = userAnswer.toString().trim();
        String standardText = standardAnswer.toString().trim();
        return userText.contains(standardText) ? questionScore : 0.0;
    }

    /**
     * scoringType=3: 文本完全相同才得分
     */
    private double calculateExactTextScore(Object userAnswer, Object standardAnswer, double questionScore) {
        if (userAnswer == null || standardAnswer == null) {
            return 0.0;
        }
        String userText = userAnswer.toString().trim();
        String standardText = standardAnswer.toString().trim();
        return userText.equals(standardText) ? questionScore : 0.0;
    }

    /**
     * scoringType=4: 多选每个选项自定义分值
     */
    private double calculateCustomOptionScore(Object userAnswer, Map<String, Object> examConfig) {
        if (!(userAnswer instanceof List)) {
            return 0.0;
        }

        List<?> userSelectedOptions = (List<?>) userAnswer;
        double totalScore = 0.0;

        // 这里需要根据examConfig中的选项分值配置来计算
        // 由于当前examConfig结构中没有明确的选项分值配置，暂时使用平均分配
        Object scoreObj = examConfig.get("score");
        double questionTotalScore = 0.0;
        if (scoreObj instanceof Integer) {
            questionTotalScore = ((Integer) scoreObj).doubleValue();
        } else if (scoreObj instanceof Double) {
            questionTotalScore = (Double) scoreObj;
        }

        // 简单实现：每个选中的选项平均分配分数
        // 实际应用中可能需要在examConfig中定义每个选项的具体分值
        if (!userSelectedOptions.isEmpty()) {
            totalScore = questionTotalScore; // 暂时给满分，实际需要根据选项配置
        }

        return totalScore;
    }

    /**
     * 考试算分方法（带详细结果）
     *
     * @param formId       表单ID
     * @param originaldata 用户填写的原始数据JSON
     * @return Map包含totalscore、sumtotalscore和scoredetailJson
     */
    private Map<String, Object> calculateExamScoreWithDetails(String formId, String originaldata) {
        Map<String, Object> result = new HashMap<>();
        double totalScore = 0.0; // 用户得分
        double sumTotalScore = 0.0; // 试卷总分
        List<Map<String, Object>> questionDetails = new ArrayList<>();
        int correctCount = 0;
        int totalCount = 0;

        try {
            // 解析用户填写的数据
            Map<String, Object> userAnswers = JSONArray.parseObject(originaldata, Map.class);

            // 获取表单的所有表单项
            List<SaFormitemPojo> formItems = saFormitemMapper.getList(formId);

            for (SaFormitemPojo formItem : formItems) {
                String scheme = formItem.getScheme();
                if (scheme == null || scheme.trim().isEmpty()) {
                    continue;
                }

                try {
                    // 解析scheme JSON，提取examConfig
                    Map<String, Object> schemeMap = JSONArray.parseObject(scheme, Map.class);
                    Map<String, Object> examConfig = (Map<String, Object>) schemeMap.get("examConfig");

                    if (examConfig == null) {
                        continue; // 没有考试配置，跳过
                    }

                    // 检查是否启用评分
                    Boolean enableScore = (Boolean) examConfig.get("enableScore");
                    if (enableScore == null || !enableScore) {
                        continue; // 未启用评分，跳过
                    }

                    totalCount++; // 计入题目总数

                    // 获取题目分值
                    Object scoreObj = examConfig.get("score");
                    double questionScore = 0.0;
                    if (scoreObj instanceof Integer) {
                        questionScore = ((Integer) scoreObj).doubleValue();
                    } else if (scoreObj instanceof Double) {
                        questionScore = (Double) scoreObj;
                    }

                    // 累计试卷总分
                    sumTotalScore += questionScore;

                    // 获取评分类型
                    Integer scoringType = (Integer) examConfig.get("scoringType");
                    if (scoringType == null) {
                        scoringType = 0; // 默认人工评分
                    }

                    // 获取用户答案
                    String formItemId = formItem.getFormitemid();
                    Object userAnswer = userAnswers.get(formItemId);

                    // 获取标准答案
                    Object standardAnswer = examConfig.get("answer");

                    // 根据评分类型进行算分
                    double earnedScore = 0.0;
                    boolean isCorrect = false;
                    if (scoringType != 0 && userAnswer != null) {
                        earnedScore = calculateQuestionScore(scoringType, userAnswer, standardAnswer, questionScore, examConfig, formItem.getType());
                        isCorrect = earnedScore > 0;
                        if (isCorrect) {
                            correctCount++;
                        }
                    }
                    totalScore += earnedScore;

                    // 构建题目详细信息
                    Map<String, Object> questionDetail = new HashMap<>();
                    questionDetail.put("formItemId", formItemId);
                    questionDetail.put("questionTitle", formItem.getLabel().replaceAll("<[^>]*>", "")); // 去除HTML标签
                    questionDetail.put("userAnswer", userAnswer);
                    questionDetail.put("standardAnswer", standardAnswer);
                    questionDetail.put("userAnswerText", convertAnswerToText(userAnswer, formItem));
                    questionDetail.put("standardAnswerText", convertAnswerToText(standardAnswer, formItem));
                    questionDetail.put("isCorrect", isCorrect);
                    questionDetail.put("earnedScore", earnedScore);
                    questionDetail.put("totalScore", questionScore);
                    questionDetail.put("scoringType", scoringType);
                    // 获取答案解析
                    questionDetail.put("answerAnalysis", examConfig.get("answerAnalysis") == null ? "" : examConfig.get("answerAnalysis"));

                    questionDetails.add(questionDetail);

                } catch (Exception e) {
                    System.err.println("解析表单项算分配置失败: " + formItem.getFormitemid() + ", " + e.getMessage());
                    // 继续处理下一个表单项
                }
            }

        } catch (Exception e) {
            System.err.println("考试算分过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        // 构建详细结果JSON
        Map<String, Object> detailsMap = new HashMap<>();
        detailsMap.put("questionDetails", questionDetails);

        Map<String, Object> summary = new HashMap<>();
        summary.put("totalEarnedScore", totalScore);
        summary.put("totalPossibleScore", sumTotalScore);
        summary.put("correctCount", correctCount);
        summary.put("totalCount", totalCount);
        summary.put("accuracy", totalCount > 0 ? (correctCount * 100.0 / totalCount) : 0.0);
        detailsMap.put("summary", summary);

        String scoredetailJson = JSON.toJSONString(detailsMap);

        result.put("totalscore", totalScore);
        result.put("sumtotalscore", sumTotalScore);
        result.put("scoredetailJson", scoredetailJson);
        return result;
    }

    /**
     * 将答案转换为可读的文本格式
     *
     * @param answer   答案（可能是数字、数组等）
     * @param formItem 表单项信息
     * @return 可读的文本
     */
    private String convertAnswerToText(Object answer, SaFormitemPojo formItem) {
        if (answer == null) {
            return "";
        }

        try {
            String scheme = formItem.getScheme();
            if (scheme == null || scheme.trim().isEmpty()) {
                return answer.toString();
            }

            // 对于单选和多选，需要转换为选项文本
            if ("RADIO".equals(formItem.getType()) || "CHECKBOX".equals(formItem.getType()) || "SELECT".equals(formItem.getType())) {
                Map<Integer, String> labelAndValue = ParseScheme(scheme);

                if (answer instanceof List) {
                    // 多选答案
                    List<?> answerList = (List<?>) answer;
                    List<String> textList = new ArrayList<>();
                    for (Object item : answerList) {
                        if (item instanceof Integer) {
                            String label = labelAndValue.get(item);
                            if (label != null) {
                                textList.add(label);
                            }
                        }
                    }
                    return String.join(", ", textList);
                } else if (answer instanceof Integer) {
                    // 单选答案
                    String label = labelAndValue.get(answer);
                    return label != null ? label : answer.toString();
                }
            }

            // 其他类型直接返回字符串
            return answer.toString();

        } catch (Exception e) {
            return answer.toString();
        }
    }


}
