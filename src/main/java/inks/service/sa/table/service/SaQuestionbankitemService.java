package inks.service.sa.table.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.table.domain.SaQuestionbankitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 题库题目表(SaQuestionbankitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:52
 */
public interface SaQuestionbankitemService {


    SaQuestionbankitemPojo getEntity(String key);

    PageInfo<SaQuestionbankitemPojo> getPageList(QueryParam queryParam);

    List<SaQuestionbankitemPojo> getList(String Pid);  

    SaQuestionbankitemPojo insert(SaQuestionbankitemPojo saQuestionbankitemPojo);

    SaQuestionbankitemPojo update(SaQuestionbankitemPojo saQuestionbankitempojo);

    int delete(String key);

    SaQuestionbankitemPojo clearNull(SaQuestionbankitemPojo saQuestionbankitempojo);
}
