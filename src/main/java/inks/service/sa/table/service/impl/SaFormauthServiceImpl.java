package inks.service.sa.table.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.mapper.SaDeptuserMapper;
import inks.sa.common.core.mapper.SaUserMapper;
import inks.sa.common.core.service.SaDeptService;
import inks.service.sa.table.domain.SaFormauthEntity;
import inks.service.sa.table.domain.pojo.SaFormPojo;
import inks.service.sa.table.domain.pojo.SaFormauthPojo;
import inks.service.sa.table.domain.pojo.SaFormdataPojo;
import inks.service.sa.table.domain.pojo.SaFormsettingPojo;
import inks.service.sa.table.domain.vo.FormAuthVO;
import inks.service.sa.table.domain.vo.FormSettingSchemaStruct;
import inks.service.sa.table.mapper.*;
import inks.service.sa.table.service.SaFormauthService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表单授权对象(SaFormauth)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-02 16:48:41
 */
@Service("saFormauthService")
public class SaFormauthServiceImpl implements SaFormauthService {
    @Resource
    private SaFormauthMapper saFormauthMapper;
    @Resource
    private SaDeptuserMapper saDeptuserMapper;
    @Resource
    private SaFormsettingMapper saFormsettingMapper;
    @Resource
    private SaFormdataMapper saFormdataMapper;
    @Resource
    private SaUserMapper saUserMapper;
    @Resource
    private SaDeptService saDeptService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormauthPojo getEntity(String key) {
        return this.saFormauthMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormauthPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormauthPojo> lst = saFormauthMapper.getPageList(queryParam);
            PageInfo<SaFormauthPojo> pageInfo = new PageInfo<SaFormauthPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saFormauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormauthPojo insert(SaFormauthPojo saFormauthPojo) {
        //初始化NULL字段
        if (saFormauthPojo.getFormid() == null) saFormauthPojo.setFormid("");
        if (saFormauthPojo.getAuthgroupid() == null) saFormauthPojo.setAuthgroupid("");
        if (saFormauthPojo.getUseridlist() == null) saFormauthPojo.setUseridlist("{}");
        if (saFormauthPojo.getRoleidlist() == null) saFormauthPojo.setRoleidlist("{}");
        if (saFormauthPojo.getDeptidlist() == null) saFormauthPojo.setDeptidlist("{}");
        if (saFormauthPojo.getRemark() == null) saFormauthPojo.setRemark("");
        if (saFormauthPojo.getCreatebyid() == null) saFormauthPojo.setCreatebyid("");
        if (saFormauthPojo.getCreateby() == null) saFormauthPojo.setCreateby("");
        if (saFormauthPojo.getCreatedate() == null) saFormauthPojo.setCreatedate(new Date());
        if (saFormauthPojo.getListerid() == null) saFormauthPojo.setListerid("");
        if (saFormauthPojo.getLister() == null) saFormauthPojo.setLister("");
        if (saFormauthPojo.getModifydate() == null) saFormauthPojo.setModifydate(new Date());
        if (saFormauthPojo.getCustom1() == null) saFormauthPojo.setCustom1("");
        if (saFormauthPojo.getCustom2() == null) saFormauthPojo.setCustom2("");
        if (saFormauthPojo.getCustom3() == null) saFormauthPojo.setCustom3("");
        if (saFormauthPojo.getCustom4() == null) saFormauthPojo.setCustom4("");
        if (saFormauthPojo.getCustom5() == null) saFormauthPojo.setCustom5("");
        if (saFormauthPojo.getTenantid() == null) saFormauthPojo.setTenantid("");
        if (saFormauthPojo.getRevision() == null) saFormauthPojo.setRevision(0);
        SaFormauthEntity saFormauthEntity = new SaFormauthEntity();
        BeanUtils.copyProperties(saFormauthPojo, saFormauthEntity);
        //生成雪花id
        saFormauthEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saFormauthEntity.setRevision(1);  //乐观锁
        this.saFormauthMapper.insert(saFormauthEntity);
        return this.getEntity(saFormauthEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saFormauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormauthPojo update(SaFormauthPojo saFormauthPojo) {
        SaFormauthEntity saFormauthEntity = new SaFormauthEntity();
        BeanUtils.copyProperties(saFormauthPojo, saFormauthEntity);
        this.saFormauthMapper.update(saFormauthEntity);
        return this.getEntity(saFormauthEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormauthMapper.delete(key);
    }

    @Override
    public List<FormAuthVO> getAuthUsersAndDepts(String formid) {
        SaFormauthPojo saFormauthPojo = saFormauthMapper.getEntityByFormId(formid);
        List<String> useridList = JSONArray.parseObject(saFormauthPojo.getUseridlist(), List.class);
        List<String> deptidList = JSONArray.parseObject(saFormauthPojo.getDeptidlist(), List.class);
        // 构建返回对象FormAuthVO 包含了所有的用户和部门信息
        List<FormAuthVO> formAuthVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(useridList)) {
            List<Map<String, String>> userInfoList = saFormauthMapper.getUserInfo(useridList);
            for (Map<String, String> userInfoMap : userInfoList) {
                FormAuthVO formAuthVO = new FormAuthVO();
                formAuthVO.setFormKey(formid);
                formAuthVO.setCollaborator(userInfoMap.get("UserName"));
                formAuthVO.setShareId(userInfoMap.get("id"));
                formAuthVO.setType(1);
                formAuthVO.setTypeDesc("用户");
                formAuthVO.setRealname(userInfoMap.get("RealName"));
                formAuthVO.setCreateTime(saFormauthPojo.getCreatedate());
                formAuthVO.setUpdateTime(saFormauthPojo.getModifydate());
                formAuthVO.setAuthid(saFormauthPojo.getId());
                formAuthVOList.add(formAuthVO);
            }
        }
        if (CollectionUtils.isNotEmpty(deptidList)) {
            List<Map<String, String>> deptInfoList = saFormauthMapper.getDeptInfo(deptidList);
            for (Map<String, String> deptInfoMap : deptInfoList) {
                FormAuthVO formAuthVO = new FormAuthVO();
                formAuthVO.setFormKey(formid);
                formAuthVO.setCollaborator(deptInfoMap.get("DeptName"));
                formAuthVO.setShareId(deptInfoMap.get("id"));
                formAuthVO.setType(2);
                formAuthVO.setTypeDesc("部门");
                formAuthVO.setCreateTime(saFormauthPojo.getCreatedate());
                formAuthVO.setUpdateTime(saFormauthPojo.getModifydate());
                formAuthVO.setAuthid(saFormauthPojo.getId());
                formAuthVOList.add(formAuthVO);
            }
        }
        // 如果用户和部门都为空，也要返回一个空的FormAuthVO对象(只需要Authid),前端需要拿到Authid
        if (CollectionUtils.isEmpty(useridList) && CollectionUtils.isEmpty(deptidList)) {
            List<Map<String, String>> userInfoList = saFormauthMapper.getUserInfo(useridList);
            FormAuthVO formAuthVO = new FormAuthVO();
            formAuthVO.setFormKey(formid);
            formAuthVO.setCreateTime(saFormauthPojo.getCreatedate());
            formAuthVO.setUpdateTime(saFormauthPojo.getModifydate());
            formAuthVO.setAuthid(saFormauthPojo.getId());
            formAuthVOList.add(formAuthVO);
        }
        return formAuthVOList;
    }

    @Override
    public List<String> getAllFormIdByUserid(String userid) {
        return saFormauthMapper.getAllFormIdByUserid(userid);
    }

    @Override
    public List<SaFormPojo> getAllFormByUserid(String userid) {
        return saFormauthMapper.getAllFormByUserid(userid);
    }

    @Override
    public List<SaFormPojo> getUnWriteFormByUserid(String userid) {
        return saFormauthMapper.getUnWriteFormByUserid(userid);
    }

    @Override
    public PageInfo<SaFormdataPojo> getSomeFormByUserid(QueryParam queryParam, String userid, String type) {

        try {
            // 1.判断是不是超级管理员用户(AdminMark=2),是则查询所有的填表
            int adminMark = saUserMapper.getAdminMarkByUserid(userid);
            boolean isSuperAdmin = adminMark == 2;
            // 2.不是超级管理员,查询userid是否是部门管理员,一个userid只能关联一个部门
            // 若不是部门管理员,则只查询本人填表
            // 当前用户可查看填表的所有用户id
            List<String> useridList = new ArrayList<>();
            if (!isSuperAdmin) {
                // 查询当前用户关联的当前部门和下级部门id集合
                // 注意 getDeptinfolistByUser方法 即使当前用户不是部门管理员,也会返回当前部门和所有下级部门信息,只不过部门信息的isdeptadmin字段都=0; 且如果当前用户未关联部门,则返回null
                // 所以这里要判断一下 如果返回null, 或者返回集合的isdeptadmin都=0,则说明不是任何一个部门的管理员,只是普通用户
                List<DeptinfoPojo> deptinfolistByUser = saDeptService.getDeptinfolistByUser(userid);
                // 判断是否是普通员工 1先检查deptinfolistByUser是否为null 2(或者||)再检查集合中的所有元素的isdeptadmin属性是否都为0
                boolean isAllNotDeptAdmin = deptinfolistByUser == null
                                          ||deptinfolistByUser.stream().allMatch(deptinfo -> deptinfo.getIsdeptadmin() == 0);
                if (isAllNotDeptAdmin) {
                    useridList.add(userid);// 不是任何一个部门的管理员,则只查询本人填表
                }else {
                    List<String> deptIds = deptinfolistByUser.stream().map(DeptinfoPojo::getDeptid).collect(Collectors.toList());
                    useridList = saDeptuserMapper.getUseridListInDeptids(deptIds);// 部门id集合对应的所有用户id集合
                }
            }

            // PageHelper 只对其后的第一个查询有效
            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
            List<SaFormdataPojo> someFormData = saFormauthMapper.getSomeFormByUserid(queryParam, useridList, type, isSuperAdmin);

            someFormData.forEach(saFormdataPojo -> {
                String formId = saFormdataPojo.getFormid();
                // 每个账号答题次数限制
                SaFormsettingPojo settingEntity = saFormsettingMapper.getEntityByFormId(formId);
                if (settingEntity != null) {//如果设置表了单设置,才进行限制
                    //TODO 暂时用fastjson  用这个读取不到字段:
                    FormSettingSchemaStruct setting = JSONArray.parseObject(settingEntity.getSettings(), FormSettingSchemaStruct.class);
                    System.out.println("formSettingSchemaStruct.isAccountWriteCountLimitStatus() = " + setting.isAccountWriteCountLimitStatus());
                    if (setting.isAccountWriteCountLimitStatus()) {
                        String rangeTypeSql = FormSettingSchemaStruct.DateRangeType.getDateSql(setting.getAccountWriteCountLimitDateType());
                        int writeCount = saFormdataMapper.getCountByFormIdWxAndIpAndUseridAndTime(formId, null, null, userid, rangeTypeSql);
                        if (writeCount >= setting.getAccountWriteCountLimit()) {
                            saFormdataPojo.setIsAccountWriteCountLimit(true); //达到了次数限制
                            saFormdataPojo.setAccountWriteCountLimitText(setting.getAccountWriteCountLimitText());//设置提示文案
                        }
                    }
                }
            });
            PageInfo<SaFormdataPojo> saFormdataPageInfo = new PageInfo<SaFormdataPojo>(someFormData);
            return saFormdataPageInfo;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public int countAllUsersUnWrite() {
        List<String> userids = saUserMapper.getUserIds();
        int sum = 0;
        for (String userid : userids) {
            int count = saFormauthMapper.countAllUsersUnWrite(userid);
            sum += count;
        }
        return sum;
    }

}
