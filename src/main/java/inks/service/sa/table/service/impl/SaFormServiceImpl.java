package inks.service.sa.table.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.table.domain.SaFormEntity;
import inks.service.sa.table.domain.SaFormitemEntity;
import inks.service.sa.table.domain.pojo.SaFormPojo;
import inks.service.sa.table.domain.pojo.SaFormitemPojo;
import inks.service.sa.table.domain.pojo.SaFormitemdetailPojo;
import inks.service.sa.table.domain.vo.UserFormDetailVO;
import inks.service.sa.table.mapper.SaFormMapper;
import inks.service.sa.table.mapper.SaFormitemMapper;
import inks.service.sa.table.service.SaFormService;
import inks.service.sa.table.service.SaFormitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
/**
 * 用户表单(SaForm)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 14:12:56
 */
@Service("saFormService")
public class SaFormServiceImpl implements SaFormService {
    @Resource
    private SaFormMapper saFormMapper;
    
    @Resource
    private SaFormitemMapper saFormitemMapper;
    
     /**
     * 服务对象Item
     */
    @Resource
    private SaFormitemService saFormitemService;
    
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormPojo getEntity(String key) {
        return this.saFormMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormitemdetailPojo> lst = saFormMapper.getPageList(queryParam);
            PageInfo<SaFormitemdetailPojo> pageInfo = new PageInfo<SaFormitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
     /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormPojo getBillEntity(String key) {
       try {
        //读取主表
        SaFormPojo saFormPojo = this.saFormMapper.getEntity(key);
        //读取子表
        saFormPojo.setItem(saFormitemMapper.getList(saFormPojo.getId()));
        return saFormPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormPojo> lst = saFormMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saFormitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaFormPojo> pageInfo = new PageInfo<SaFormPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
       /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormPojo> lst = saFormMapper.getPageTh(queryParam);
            PageInfo<SaFormPojo> pageInfo = new PageInfo<SaFormPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saFormPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaFormPojo insert(SaFormPojo saFormPojo) {
//初始化NULL字段
     if(saFormPojo.getSourceid()==null) saFormPojo.setSourceid("");
     if(saFormPojo.getSourcetype()==null) saFormPojo.setSourcetype(1);//来源类型1空白创建2模板
     if(saFormPojo.getName()==null) saFormPojo.setName("");
     if(saFormPojo.getDescription()==null) saFormPojo.setDescription("");
     if(saFormPojo.getUserid()==null) saFormPojo.setUserid(saFormPojo.getCreatebyid());//--
     if(saFormPojo.getFormtype()==null) saFormPojo.setFormtype("");
     if(saFormPojo.getStatus()==null) saFormPojo.setStatus(1);//状态:1未发布2收集中3停止发布
     if(saFormPojo.getIsdeleted()==null) saFormPojo.setIsdeleted(0);
     if(saFormPojo.getIsfolder()==null) saFormPojo.setIsfolder(0);
     if(saFormPojo.getFolderid()==null) saFormPojo.setFolderid(0);
     if(saFormPojo.getRemark()==null) saFormPojo.setRemark("");
     if(saFormPojo.getCreatebyid()==null) saFormPojo.setCreatebyid("");
     if(saFormPojo.getCreateby()==null) saFormPojo.setCreateby("");
     if(saFormPojo.getCreatedate()==null) saFormPojo.setCreatedate(new Date());
     if(saFormPojo.getListerid()==null) saFormPojo.setListerid("");
     if(saFormPojo.getLister()==null) saFormPojo.setLister("");
     if(saFormPojo.getModifydate()==null) saFormPojo.setModifydate(new Date());
     if(saFormPojo.getCustom1()==null) saFormPojo.setCustom1("");
     if(saFormPojo.getCustom2()==null) saFormPojo.setCustom2("");
     if(saFormPojo.getCustom3()==null) saFormPojo.setCustom3("");
     if(saFormPojo.getCustom4()==null) saFormPojo.setCustom4("");
     if(saFormPojo.getCustom5()==null) saFormPojo.setCustom5("");
     if(saFormPojo.getDeptid()==null) saFormPojo.setDeptid("");
     if(saFormPojo.getTenantid()==null) saFormPojo.setTenantid("");
     if(saFormPojo.getRevision()==null) saFormPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaFormEntity saFormEntity = new SaFormEntity(); 
        BeanUtils.copyProperties(saFormPojo,saFormEntity);
        //设置id和新建日期
        saFormEntity.setId(id);
        saFormEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saFormMapper.insert(saFormEntity);
        //Item子表处理
        List<SaFormitemPojo> lst = saFormPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaFormitemPojo itemPojo =this.saFormitemService.clearNull(lst.get(i));
               SaFormitemEntity saFormitemEntity = new SaFormitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saFormitemEntity);
               //设置id和Pid
               saFormitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saFormitemEntity.setPid(id);
               saFormitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saFormitemMapper.insert(saFormitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saFormEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFormPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaFormPojo update(SaFormPojo saFormPojo) {
        //主表更改
        SaFormEntity saFormEntity = new SaFormEntity(); 
        BeanUtils.copyProperties(saFormPojo,saFormEntity);
        this.saFormMapper.update(saFormEntity);
        if (saFormPojo.getItem() != null) {
        //Item子表处理
        List<SaFormitemPojo> lst = saFormPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saFormMapper.getDelItemIds(saFormPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saFormitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaFormitemEntity saFormitemEntity = new SaFormitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaFormitemPojo itemPojo =this.saFormitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saFormitemEntity);
               //设置id和Pid
               saFormitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saFormitemEntity.setPid(saFormEntity.getId());  // 主表 id
               saFormitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saFormitemMapper.insert(saFormitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saFormitemEntity);             
               this.saFormitemMapper.update(saFormitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saFormEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
       SaFormPojo saFormPojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaFormitemPojo> lst = saFormPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saFormitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saFormMapper.delete(key) ;
    }

    @Override
    public Integer logicDelete(String key) {
        return this.saFormMapper.logicDelete(key);
    }

    @Override
    public UserFormDetailVO details(String key) {
        SaFormPojo formPojo = saFormMapper.getEntity(key);
        List<SaFormitemPojo> formitemList = saFormitemMapper.getList(key);

        return new UserFormDetailVO(formPojo, formitemList,null,null);
    }

    @Override
    public List<Map<String, Object>> getFormItemMap(String key) {
        return saFormMapper.getFormItemMap(key);
    }
}
