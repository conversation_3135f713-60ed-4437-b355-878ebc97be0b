package inks.service.sa.table.service;

import inks.service.sa.table.domain.vo.ExamScoreResult;

import java.util.Map;

/**
 * 考试评分服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface ExamScoringService {

    /**
     * 计算考试分数
     * 
     * @param formId 表单ID
     * @param answers 用户答题数据 Map<题目ID, 答案>
     * @return 评分结果
     */
    ExamScoreResult calculateScore(String formId, Map<String, Object> answers);

    /**
     * 重新计算考试分数（用于人工评分后的重新计算）
     * 
     * @param formDataId 答题记录ID
     * @return 评分结果
     */
    ExamScoreResult recalculateScore(String formDataId);

    /**
     * 获取题目的正确答案（用于评分参考）
     * 
     * @param formId 表单ID
     * @param questionId 题目ID
     * @return 正确答案
     */
    Object getCorrectAnswer(String formId, String questionId);

    /**
     * 验证examConfig配置的完整性
     * 
     * @param formId 表单ID
     * @return 验证结果，true表示配置完整
     */
    boolean validateExamConfig(String formId);
}
