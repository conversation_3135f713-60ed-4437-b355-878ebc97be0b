package inks.service.sa.table.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaFormviewcountPojo;

/**
 * 用户表单查看次数(SaFormviewcount)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
public interface SaFormviewcountService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormviewcountPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormviewcountPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFormviewcountPojo 实例对象
     * @return 实例对象
     */
    SaFormviewcountPojo insert(SaFormviewcountPojo saFormviewcountPojo);

    /**
     * 修改数据
     *
     * @param saFormviewcountpojo 实例对象
     * @return 实例对象
     */
    SaFormviewcountPojo update(SaFormviewcountPojo saFormviewcountpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                     }
