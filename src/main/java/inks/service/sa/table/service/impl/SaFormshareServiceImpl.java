package inks.service.sa.table.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.table.domain.SaFormshareEntity;
import inks.service.sa.table.domain.pojo.SaFormsharePojo;
import inks.service.sa.table.mapper.SaFormshareMapper;
import inks.service.sa.table.service.SaFormshareService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 表单分享表(SaFormshare)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20 14:18:23
 */
@Service("saFormshareService")
public class SaFormshareServiceImpl implements SaFormshareService {
    @Resource
    private SaFormshareMapper saFormshareMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormsharePojo getEntity(String key) {
        return this.saFormshareMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormsharePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormsharePojo> lst = saFormshareMapper.getPageList(queryParam);
            PageInfo<SaFormsharePojo> pageInfo = new PageInfo<SaFormsharePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saFormsharePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormsharePojo insert(SaFormsharePojo saFormsharePojo) {
    //初始化NULL字段
     if(saFormsharePojo.getFormdataid()==null) saFormsharePojo.setFormdataid("");
     if(saFormsharePojo.getUrl()==null) saFormsharePojo.setUrl("");
     if(saFormsharePojo.getDeadtime()==null) saFormsharePojo.setDeadtime(new Date());
     if(saFormsharePojo.getPasswordmark()==null) saFormsharePojo.setPasswordmark(0);
     if(saFormsharePojo.getPassword()==null) saFormsharePojo.setPassword("");
     if(saFormsharePojo.getAllowdown()==null) saFormsharePojo.setAllowdown(0);
     if(saFormsharePojo.getRemark()==null) saFormsharePojo.setRemark("");
     if(saFormsharePojo.getCreateby()==null) saFormsharePojo.setCreateby("");
     if(saFormsharePojo.getCreatebyid()==null) saFormsharePojo.setCreatebyid("");
     if(saFormsharePojo.getCreatedate()==null) saFormsharePojo.setCreatedate(new Date());
     if(saFormsharePojo.getLister()==null) saFormsharePojo.setLister("");
     if(saFormsharePojo.getListerid()==null) saFormsharePojo.setListerid("");
     if(saFormsharePojo.getModifydate()==null) saFormsharePojo.setModifydate(new Date());
     if(saFormsharePojo.getCustom1()==null) saFormsharePojo.setCustom1("");
     if(saFormsharePojo.getCustom2()==null) saFormsharePojo.setCustom2("");
     if(saFormsharePojo.getCustom3()==null) saFormsharePojo.setCustom3("");
     if(saFormsharePojo.getCustom4()==null) saFormsharePojo.setCustom4("");
     if(saFormsharePojo.getCustom5()==null) saFormsharePojo.setCustom5("");
     if(saFormsharePojo.getTenantid()==null) saFormsharePojo.setTenantid("");
     if(saFormsharePojo.getRevision()==null) saFormsharePojo.setRevision(0);
        SaFormshareEntity saFormshareEntity = new SaFormshareEntity(); 
        BeanUtils.copyProperties(saFormsharePojo,saFormshareEntity);
        //生成雪花id
          saFormshareEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFormshareEntity.setRevision(1);  //乐观锁
          this.saFormshareMapper.insert(saFormshareEntity);
        return this.getEntity(saFormshareEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFormsharePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormsharePojo update(SaFormsharePojo saFormsharePojo) {
        SaFormshareEntity saFormshareEntity = new SaFormshareEntity(); 
        BeanUtils.copyProperties(saFormsharePojo,saFormshareEntity);
        this.saFormshareMapper.update(saFormshareEntity);
        return this.getEntity(saFormshareEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormshareMapper.delete(key) ;
    }
    
                                                                                                             
}
