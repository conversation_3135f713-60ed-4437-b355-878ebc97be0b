package inks.service.sa.table.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaTemplatePojo;

/**
 * 表单模板(SaTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:28
 */
public interface SaTemplateService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTemplatePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTemplatePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saTemplatePojo 实例对象
     * @return 实例对象
     */
    SaTemplatePojo insert(SaTemplatePojo saTemplatePojo);

    /**
     * 修改数据
     *
     * @param saTemplatepojo 实例对象
     * @return 实例对象
     */
    SaTemplatePojo update(SaTemplatePojo saTemplatepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                                              }
