package inks.service.sa.table.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaQuestionbankPojo;
import inks.service.sa.table.domain.pojo.SaQuestionbankitemdetailPojo;
import inks.service.sa.table.domain.SaQuestionbankEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 题库表(SaQuestionbank)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:44
 */
public interface SaQuestionbankService {


    SaQuestionbankPojo getEntity(String key);

    PageInfo<SaQuestionbankitemdetailPojo> getPageList(QueryParam queryParam);

    SaQuestionbankPojo getBillEntity(String key);

    PageInfo<SaQuestionbankPojo> getBillList(QueryParam queryParam);

    PageInfo<SaQuestionbankPojo> getPageTh(QueryParam queryParam);

    SaQuestionbankPojo insert(SaQuestionbankPojo saQuestionbankPojo);

    SaQuestionbankPojo update(SaQuestionbankPojo saQuestionbankpojo);

    int delete(String key);

}
