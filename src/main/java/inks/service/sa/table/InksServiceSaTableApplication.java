package inks.service.sa.table;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;

@SpringBootApplication(scanBasePackages = {"inks.sa.common.core", "inks.common.core","inks.service.sa.table"})
@MapperScan({"inks.service.sa.table.mapper", "inks.sa.common.core.mapper"})
@EnableFeignClients(basePackages = {"inks.sa.common.core.feign"})
@EnableSwagger2
public class InksServiceSaTableApplication {

public static void main(String[] args) throws  UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(InksServiceSaTableApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("spring.application.name");
        String path = property == null ? "" : property;
        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "Application Sailrui-Boot is running! Access URLs 又去掉了SSL:\n\t" +
                        "Local: \t\thttp://localhost:" + port + "/swagger-ui.html\n\t" +
                        "Local: \t\thttp://localhost:" + port + "\n\t" +
                        "External: \thttp://" + ip + ":" + port + "/swagger-ui.html\n\t" +
                        "Nacos: \t\thttp://dev.inksyun.com:"+ port + "/swagger-ui.html\n\t" +
                        "------------------------------------------------------------");
    }

}
