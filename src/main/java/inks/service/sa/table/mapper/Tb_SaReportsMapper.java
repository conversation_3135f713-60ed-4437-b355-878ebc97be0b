package inks.service.sa.table.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.SaReportsEntity;
import inks.service.sa.table.domain.pojo.SaReportsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报表中心(含Formid)(SaReports)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:49
 */
@Mapper
public interface Tb_SaReportsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReportsPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaReportsPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saReportsEntity 实例对象
     * @return 影响行数
     */
    int insert(SaReportsEntity saReportsEntity);


    /**
     * 修改数据
     *
     * @param saReportsEntity 实例对象
     * @return 影响行数
     */
    int update(SaReportsEntity saReportsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaReportsPojo> getListByDef(@Param("moduleCode") String moduleCode);

    SaReportsPojo getEntityByNameCode(@Param("rptname") String rptname,@Param("moduleCode") String moduleCode);
    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaReportsPojo> getPageListAll(QueryParam queryParam);

    List<SaReportsPojo> getListByModuleCode(@Param("moduleCode") String moduleCode);

    List<SaReportsPojo> getListByFormId(@Param("formid") String formid, @Param("code") String code, @Param("queryParam") QueryParam queryParam);
}

