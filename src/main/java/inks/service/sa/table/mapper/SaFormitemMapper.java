package inks.service.sa.table.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.SaFormitemEntity;
import inks.service.sa.table.domain.pojo.SaFormitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表单项(SaFormitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:20:01
 */
 @Mapper
public interface SaFormitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaFormitemPojo> getList(@Param("Pid") String Pid);    
     
    
    /**
     * 新增数据
     *
     * @param saFormitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormitemEntity saFormitemEntity);

    
    /**
     * 修改数据
     *
     * @param saFormitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormitemEntity saFormitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    Long getLastItemSort(String formKey);


    SaFormitemPojo getByFormItemId(@Param("formItemId") String formItemId, @Param("formId") String formId);

    String getSchemeByFormItemId(@Param("formItemId") String formItemId, @Param("formId") String formId);
}

