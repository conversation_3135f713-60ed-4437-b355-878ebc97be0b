package inks.service.sa.table.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.SaTemplateEntity;
import inks.service.sa.table.domain.pojo.SaTemplatePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表单模板(SaTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:28
 */
@Mapper
public interface SaTemplateMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTemplatePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTemplatePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saTemplateEntity 实例对象
     * @return 影响行数
     */
    int insert(SaTemplateEntity saTemplateEntity);

    
    /**
     * 修改数据
     *
     * @param saTemplateEntity 实例对象
     * @return 影响行数
     */
    int update(SaTemplateEntity saTemplateEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
                                                                                                              }

