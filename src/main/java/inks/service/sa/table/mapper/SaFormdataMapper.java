package inks.service.sa.table.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.SaFormdataEntity;
import inks.service.sa.table.domain.pojo.SaFormdataPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 表单收集数据结果(SaFormdata)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-06 10:57:52
 */
@Mapper
public interface SaFormdataMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormdataPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormdataPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saFormdataEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormdataEntity saFormdataEntity);


    /**
     * 修改数据
     *
     * @param saFormdataEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormdataEntity saFormdataEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);


    //rangeTypeSql是一个时间范围的字符串，用于拼接sql语句
    int getCountByFormIdWxAndIpAndUseridAndTime(@Param("formId") String formId, @Param("wxOpenId") String wxOpenId,
                                           @Param("ipAddr") String ipAddr,@Param("userId") String userId, @Param("rangeTypeSql") String rangeTypeSql);

    int approval(SaFormdataEntity saFormdataEntity);


    Map<String, Object> countFormdataSubmit();

    List<Map<String, Object>> formFilledInNumber(QueryParam queryParam);

    List<Map<String, Object>> formSubmitNumberByForm();

    List<SaFormdataPojo> getAllNoRealData();

    void updateRealData(@Param("id") String id, @Param("realdata") String realdata);
}

