package inks.service.sa.table.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.SaFormthemeEntity;
import inks.service.sa.table.domain.pojo.SaFormthemePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目主题外观模板(SaFormtheme)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:43
 */
@Mapper
public interface SaFormthemeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormthemePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormthemePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saFormthemeEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormthemeEntity saFormthemeEntity);

    
    /**
     * 修改数据
     *
     * @param saFormthemeEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormthemeEntity saFormthemeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
                                                                                                    }

