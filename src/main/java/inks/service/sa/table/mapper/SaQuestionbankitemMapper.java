package inks.service.sa.table.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.table.domain.SaQuestionbankitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 题库题目表(SaQuestionbankitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:51
 */
 @Mapper
public interface SaQuestionbankitemMapper {

    SaQuestionbankitemPojo getEntity(@Param("key") String key);

    List<SaQuestionbankitemPojo> getPageList(QueryParam queryParam);

    List<SaQuestionbankitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaQuestionbankitemEntity saQuestionbankitemEntity);

    int update(SaQuestionbankitemEntity saQuestionbankitemEntity);

    int delete(@Param("key") String key);

    /**
     * 根据题目ID和题库ID获取题目信息
     * @param questionItemId 题目ID
     * @param questionBankId 题库ID
     * @return 题目信息
     */
    SaQuestionbankitemPojo getByQuestionItemId(@Param("questionItemId") String questionItemId, @Param("questionBankId") String questionBankId);

}

