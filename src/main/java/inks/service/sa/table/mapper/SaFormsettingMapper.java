package inks.service.sa.table.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.SaFormsettingEntity;
import inks.service.sa.table.domain.pojo.SaFormsettingPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表单设置表(SaFormsetting)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:43
 */
@Mapper
public interface SaFormsettingMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormsettingPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormsettingPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saFormsettingEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormsettingEntity saFormsettingEntity);

    
    /**
     * 修改数据
     *
     * @param saFormsettingEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormsettingEntity saFormsettingEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    SaFormsettingPojo getEntityByFormId(String formid);
}

