package inks.service.sa.table.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.table.domain.SaFormEntity;
import inks.service.sa.table.domain.pojo.SaFormPojo;
import inks.service.sa.table.domain.pojo.SaFormitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户表单(SaForm)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:12:56
 */
@Mapper
public interface SaFormMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param saFormEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormEntity saFormEntity);

    
    /**
     * 修改数据
     *
     * @param saFormEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormEntity saFormEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
     /**
     * 查询 被删除的Item
     *
     * @param saFormPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(SaFormPojo saFormPojo);

    Integer logicDelete(String key);

    String getFormName(String formid);

    int countForm();

    List<Map<String, Object>> getFormItemMap(String formid);
}

