package inks.service.sa.table.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.table.domain.pojo.SaFormdataPojo;
import inks.service.sa.table.mapper.SaFormdataMapper;
import inks.service.sa.table.service.SaFormdataService;
import inks.service.sa.table.service.impl.SaFormdataServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@SpringBootTest
public class MyTest {

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaFormdataMapper saFormdataMapper;
    @Resource
    private SaFormdataServiceImpl saFormdataServiceImpl;
    @Resource
    private SaFormdataService saFormdataService;

    @Test
    public void testGetMessage() throws InterruptedException {
        saRedisService.setCacheObject("message", "Hello World", 1000L, TimeUnit.MILLISECONDS);
        String message = saRedisService.getCacheObject("message", String.class);
        System.out.println(message);
        Thread.sleep(1000L);
        String message2 = saRedisService.getCacheObject("message", String.class);
        System.out.println("Thread.sleep(1000L) = " + message2);
    }

    //刷新Sa_FormData表: 将OriginalData的单选多选数字转换为实际值,存储到RealData字段中,供查询时使用
    @Test
    public void OriginalDataToRealData() {
        List<SaFormdataPojo> allNoRealData = saFormdataMapper.getAllNoRealData();
        for (SaFormdataPojo allNoRealDatum : allNoRealData) {
            String originalData = allNoRealDatum.getOriginaldata();
            String formid = allNoRealDatum.getFormid();
            String id = allNoRealDatum.getId();
            Map<String, Object> originaldataMap = JSONArray.parseObject(originalData, Map.class);
            // 给多选框,性别,下拉框,图片url赋值
            saFormdataServiceImpl.ControlToPopulateData(originaldataMap, formid);
            // 将Map转换为JSON字符串
            String realdata = JSON.toJSONString(originaldataMap);
            // 更新RealData字段
            saFormdataMapper.updateRealData(id, realdata);
        }
    }

}