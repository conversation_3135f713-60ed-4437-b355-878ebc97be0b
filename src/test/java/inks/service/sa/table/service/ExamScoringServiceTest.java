package inks.service.sa.table.service;

import com.alibaba.fastjson.JSON;
import inks.service.sa.table.domain.pojo.SaFormitemPojo;
import inks.service.sa.table.domain.vo.ExamScoreResult;
import inks.service.sa.table.service.impl.ExamScoringServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * ExamScoringService 单元测试
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public class ExamScoringServiceTest {

    @Mock
    private SaFormitemService saFormitemService;

    @Mock
    private SaFormdataService saFormdataService;

    @InjectMocks
    private ExamScoringServiceImpl examScoringService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCalculateScore_RadioQuestion() {
        // 准备测试数据 - 单选题
        String formId = "test-form-1";
        List<SaFormitemPojo> formItems = new ArrayList<>();
        
        SaFormitemPojo radioItem = new SaFormitemPojo();
        radioItem.setFormitemid("radio1751874012760");
        radioItem.setType("RADIO");
        
        // 构造scheme JSON，包含examConfig
        Map<String, Object> scheme = new HashMap<>();
        Map<String, Object> examConfig = new HashMap<>();
        examConfig.put("scoringType", 1);
        examConfig.put("score", 10);
        examConfig.put("enableScore", true);
        examConfig.put("answer", Arrays.asList(2)); // 正确答案是选项2
        examConfig.put("answerAnalysis", "这是答案解析");
        
        scheme.put("examConfig", examConfig);
        radioItem.setScheme(JSON.toJSONString(scheme));
        formItems.add(radioItem);
        
        when(saFormitemService.getList(formId)).thenReturn(formItems);
        
        // 准备用户答案
        Map<String, Object> answers = new HashMap<>();
        answers.put("radio1751874012760", Arrays.asList(2)); // 用户选择了正确答案
        
        // 执行评分
        ExamScoreResult result = examScoringService.calculateScore(formId, answers);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(new BigDecimal("10"), result.getTotalScore());
        assertEquals(new BigDecimal("10"), result.getFullScore());
        assertEquals(new BigDecimal("10"), result.getObjectiveScore());
        assertEquals(BigDecimal.ZERO, result.getSubjectiveScore());
        assertEquals(Integer.valueOf(1), result.getIsPassed()); // 100%通过
        assertEquals("自动评分完成", result.getScoringStatus());
        
        // 验证题目详情
        List<ExamScoreResult.QuestionScoreDetail> details = result.getQuestionDetails();
        assertEquals(1, details.size());
        
        ExamScoreResult.QuestionScoreDetail detail = details.get(0);
        assertEquals("radio1751874012760", detail.getQuestionId());
        assertEquals("RADIO", detail.getQuestionType());
        assertEquals(new BigDecimal("10"), detail.getScore());
        assertEquals(new BigDecimal("10"), detail.getFullScore());
        assertTrue(detail.getIsCorrect());
        assertEquals(Integer.valueOf(1), detail.getScoringType());
    }

    @Test
    void testCalculateScore_WrongAnswer() {
        // 准备测试数据 - 错误答案
        String formId = "test-form-2";
        List<SaFormitemPojo> formItems = new ArrayList<>();
        
        SaFormitemPojo radioItem = new SaFormitemPojo();
        radioItem.setFormitemid("radio1751874012760");
        radioItem.setType("RADIO");
        
        Map<String, Object> scheme = new HashMap<>();
        Map<String, Object> examConfig = new HashMap<>();
        examConfig.put("scoringType", 1);
        examConfig.put("score", 10);
        examConfig.put("enableScore", true);
        examConfig.put("answer", Arrays.asList(2)); // 正确答案是选项2
        
        scheme.put("examConfig", examConfig);
        radioItem.setScheme(JSON.toJSONString(scheme));
        formItems.add(radioItem);
        
        when(saFormitemService.getList(formId)).thenReturn(formItems);
        
        // 准备用户答案 - 错误答案
        Map<String, Object> answers = new HashMap<>();
        answers.put("radio1751874012760", Arrays.asList(1)); // 用户选择了错误答案
        
        // 执行评分
        ExamScoreResult result = examScoringService.calculateScore(formId, answers);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getTotalScore());
        assertEquals(new BigDecimal("10"), result.getFullScore());
        assertEquals(Integer.valueOf(0), result.getIsPassed()); // 0%不通过
        
        ExamScoreResult.QuestionScoreDetail detail = result.getQuestionDetails().get(0);
        assertEquals(BigDecimal.ZERO, detail.getScore());
        assertFalse(detail.getIsCorrect());
    }

    @Test
    void testCalculateScore_SubjectiveQuestion() {
        // 准备测试数据 - 主观题
        String formId = "test-form-3";
        List<SaFormitemPojo> formItems = new ArrayList<>();
        
        SaFormitemPojo inputItem = new SaFormitemPojo();
        inputItem.setFormitemid("input1751874012762");
        inputItem.setType("INPUT");
        
        Map<String, Object> scheme = new HashMap<>();
        Map<String, Object> examConfig = new HashMap<>();
        examConfig.put("scoringType", 2); // 主观题
        examConfig.put("score", 20);
        examConfig.put("enableScore", true);
        examConfig.put("answer", "标准答案");
        
        scheme.put("examConfig", examConfig);
        inputItem.setScheme(JSON.toJSONString(scheme));
        formItems.add(inputItem);
        
        when(saFormitemService.getList(formId)).thenReturn(formItems);
        
        // 准备用户答案
        Map<String, Object> answers = new HashMap<>();
        answers.put("input1751874012762", "用户的答案");
        
        // 执行评分
        ExamScoreResult result = examScoringService.calculateScore(formId, answers);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getTotalScore()); // 主观题初始分数为0
        assertEquals(new BigDecimal("20"), result.getFullScore());
        assertEquals("待人工评分", result.getScoringStatus());
        assertEquals(1, result.getManualGradingQuestions().size());
        assertTrue(result.getManualGradingQuestions().contains("input1751874012762"));
    }

    @Test
    void testValidateExamConfig() {
        // 准备测试数据
        String formId = "test-form-4";
        List<SaFormitemPojo> formItems = new ArrayList<>();
        
        SaFormitemPojo validItem = new SaFormitemPojo();
        validItem.setFormitemid("radio1");
        
        Map<String, Object> scheme = new HashMap<>();
        Map<String, Object> examConfig = new HashMap<>();
        examConfig.put("scoringType", 1);
        examConfig.put("score", 10);
        examConfig.put("enableScore", true);
        
        scheme.put("examConfig", examConfig);
        validItem.setScheme(JSON.toJSONString(scheme));
        formItems.add(validItem);
        
        when(saFormitemService.getList(formId)).thenReturn(formItems);
        
        // 执行验证
        boolean isValid = examScoringService.validateExamConfig(formId);
        
        // 验证结果
        assertTrue(isValid);
    }

    @Test
    void testValidateExamConfig_Invalid() {
        // 准备测试数据 - 无效配置
        String formId = "test-form-5";
        List<SaFormitemPojo> formItems = new ArrayList<>();
        
        SaFormitemPojo invalidItem = new SaFormitemPojo();
        invalidItem.setFormitemid("radio1");
        invalidItem.setScheme("{}"); // 缺少examConfig
        formItems.add(invalidItem);
        
        when(saFormitemService.getList(formId)).thenReturn(formItems);
        
        // 执行验证
        boolean isValid = examScoringService.validateExamConfig(formId);
        
        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testGetCorrectAnswer() {
        // 准备测试数据
        String formId = "test-form-6";
        String questionId = "radio1751874012760";
        
        List<SaFormitemPojo> formItems = new ArrayList<>();
        SaFormitemPojo item = new SaFormitemPojo();
        item.setFormitemid(questionId);
        
        Map<String, Object> scheme = new HashMap<>();
        Map<String, Object> examConfig = new HashMap<>();
        examConfig.put("answer", Arrays.asList(2));
        
        scheme.put("examConfig", examConfig);
        item.setScheme(JSON.toJSONString(scheme));
        formItems.add(item);
        
        when(saFormitemService.getList(formId)).thenReturn(formItems);
        
        // 执行获取正确答案
        Object correctAnswer = examScoringService.getCorrectAnswer(formId, questionId);
        
        // 验证结果
        assertNotNull(correctAnswer);
        assertEquals(Arrays.asList(2), correctAnswer);
    }
}
