#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes for AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=13348, tid=30268
#
# JRE version:  (********+7) (build )
# Java VM: OpenJDK 64-Bit Server VM (********+7-b1000.32, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://git.inksdev.com': 

Host: Intel(R) Xeon(R) CPU E5-2680 v4 @ 2.40GHz, 28 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
Time: Mon Dec  4 16:43:19 2023  Windows 10 , 64 bit Build 19041 (10.0.19041.3636) elapsed time: 0.025864 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000204e6f39570):  JavaThread "Unknown thread" [_thread_in_vm, id=30268, stack(0x000000e2e5200000,0x000000e2e5300000)]

Stack: [0x000000e2e5200000,0x000000e2e5300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6852da]
V  [jvm.dll+0x844c84]
V  [jvm.dll+0x84659e]
V  [jvm.dll+0x846c03]
V  [jvm.dll+0x24b7cf]
V  [jvm.dll+0xa6b2b]
V  [jvm.dll+0x2ddb24]
V  [jvm.dll+0x815bdc]
V  [jvm.dll+0x370df1]
V  [jvm.dll+0x7f4fdc]
V  [jvm.dll+0x3f3f1f]
V  [jvm.dll+0x3f5b31]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17344]
C  [ntdll.dll+0x526b1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffce7af00d8, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:

=>0x00000204e6f39570 (exited) JavaThread "Unknown thread" [_thread_in_vm, id=30268, stack(0x000000e2e5200000,0x000000e2e5300000)]

Threads with active compile tasks:

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000000000000, size: 0 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.015 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6e6b80000 - 0x00007ff6e6b8a000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.exe
0x00007ffd1b230000 - 0x00007ffd1b428000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffd1add0000 - 0x00007ffd1ae8d000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffd18ba0000 - 0x00007ffd18e96000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffd18910000 - 0x00007ffd18a10000 	C:\Windows\System32\ucrtbase.dll
0x00007ffce5980000 - 0x00007ffce5997000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jli.dll
0x00007ffd1a4c0000 - 0x00007ffd1a65e000 	C:\Windows\System32\USER32.dll
0x00007ffd0e9b0000 - 0x00007ffd0ec4a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5\COMCTL32.dll
0x00007ffd18f20000 - 0x00007ffd18f42000 	C:\Windows\System32\win32u.dll
0x00007ffcfd580000 - 0x00007ffcfd59b000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\VCRUNTIME140.dll
0x00007ffd19650000 - 0x00007ffd1967c000 	C:\Windows\System32\GDI32.dll
0x00007ffd1ac80000 - 0x00007ffd1ad1e000 	C:\Windows\System32\msvcrt.dll
0x00007ffd18a10000 - 0x00007ffd18b2a000 	C:\Windows\System32\gdi32full.dll
0x00007ffd191f0000 - 0x00007ffd1928d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffd1ad20000 - 0x00007ffd1ad50000 	C:\Windows\System32\IMM32.DLL
0x00007ffd0b9c0000 - 0x00007ffd0b9cc000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\vcruntime140_1.dll
0x00007ffcc15e0000 - 0x00007ffcc166d000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\msvcp140.dll
0x00007ffce6fb0000 - 0x00007ffce7c33000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server\jvm.dll
0x00007ffd198a0000 - 0x00007ffd1994f000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffd1a300000 - 0x00007ffd1a39c000 	C:\Windows\System32\sechost.dll
0x00007ffd19680000 - 0x00007ffd197a6000 	C:\Windows\System32\RPCRT4.dll
0x00007ffce8020000 - 0x00007ffce8029000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffd17ec0000 - 0x00007ffd17f0b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffd19a00000 - 0x00007ffd19a6b000 	C:\Windows\System32\WS2_32.dll
0x00007ffd0eec0000 - 0x00007ffd0eee7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffd107c0000 - 0x00007ffd107ca000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffd17d30000 - 0x00007ffd17d42000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffd167f0000 - 0x00007ffd16802000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffce6fa0000 - 0x00007ffce6faa000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll
0x00007ffd0dd40000 - 0x00007ffd0df24000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffd095a0000 - 0x00007ffd095d4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffd18f50000 - 0x00007ffd18fd2000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffce6f70000 - 0x00007ffce6f95000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://git.inksdev.com': 
java_class_path (initial): C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/plugins/vcs-git/lib/git4idea-rt.jar;C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 5                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 20                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8568963072                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8568963072                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:\ALL-JAVA\Git\mingw64\libexec\git-core;D:\ALL-JAVA\Git\mingw64\libexec\git-core;D:\ALL-JAVA\Git\mingw64\bin;D:\ALL-JAVA\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\ALL-APP\Bandizip;D:\ALL-JAVA\maven\apache-maven-3.9.1\bin;D:\ALL-JAVA\Git\cmd;D:\ALL-APP\TortoiseGit\bin;C:\Program Files\nodejs;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn;C:\Program Files\Microsoft SQL Server\160\Tools\Binn;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn;C:\Program Files\Microsoft SQL Server\160\DTS\Binn;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;D:\ALL-JAVA\JetBrains\DataGrip 2023.1.2\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts
USERNAME=ykhp02
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 79 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp


JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
OS uptime: 0 days 6:26 hours

CPU: total 28 (initial active 28) (14 cores per cpu, 2 threads per core) family 6 model 79 stepping 1 microcode 0xb000031, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush
Processor Information for processor 0
  Max Mhz: 2401, Current Mhz: 2401, Mhz Limit: 2401
Processor Information for processor 1
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 2
  Max Mhz: 2401, Current Mhz: 2401, Mhz Limit: 2401
Processor Information for processor 3
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 4
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 5
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 6
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 7
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 8
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 9
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 10
  Max Mhz: 2401, Current Mhz: 2401, Mhz Limit: 2401
Processor Information for processor 11
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 12
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 13
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 14
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 15
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 16
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 17
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 18
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 19
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 20
  Max Mhz: 2401, Current Mhz: 2401, Mhz Limit: 2401
Processor Information for processor 21
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 22
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 23
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 24
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 25
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 26
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401
Processor Information for processor 27
  Max Mhz: 2401, Current Mhz: 1200, Mhz Limit: 2401

Memory: 4k page, system-wide physical 32684M (2449M free)
TotalPageFile size 55720M (AvailPageFile size 11M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 15M, peak: 16M

vm_info: OpenJDK 64-Bit Server VM (********+7-b1000.32) for windows-amd64 JRE (********+7-b1000.32), built on 2023-09-15 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
